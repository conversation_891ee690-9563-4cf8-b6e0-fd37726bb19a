<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="site-title">Taxi Booking CMS</title>
    <meta name="description" content="Professional taxi booking system with admin management">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Loading Spinner -->
    <div id="loading" class="loading-overlay">
        <div class="spinner"></div>
    </div>

    <!-- Header -->
    <header id="header" class="header">
        <div class="top-bar">
            <div class="container">
                <div class="top-bar-content">
                    <div class="contact-info">
                        <span id="header-phone"><i class="fas fa-phone"></i> <span class="phone-number">Loading...</span></span>
                        <span id="header-email"><i class="fas fa-envelope"></i> <span class="email">Loading...</span></span>
                    </div>
                    <div class="social-links" id="social-links">
                        <!-- Dynamic social links -->
                    </div>
                </div>
            </div>
        </div>
        
        <div class="main-header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <img id="site-logo" src="assets/images/logo.png" alt="Logo" onerror="this.style.display='none'">
                        <h1 id="site-name">Taxi Booking</h1>
                    </div>
                    
                    <nav class="main-nav" id="main-nav">
                        <ul class="nav-menu" id="nav-menu">
                            <!-- Dynamic navigation -->
                        </ul>
                    </nav>
                    
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="openBookingModal()">
                            <i class="fas fa-car"></i> Đặt Xe Ngay
                        </button>
                        <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="hero" class="hero">
        <div class="hero-slider" id="hero-slider">
            <!-- Dynamic slides -->
        </div>
        <div class="hero-content">
            <div class="container">
                <div class="hero-text">
                    <h2 id="hero-title">Dịch Vụ Taxi Chuyên Nghiệp</h2>
                    <p id="hero-subtitle">An toàn - Nhanh chóng - Uy tín</p>
                    <div class="hero-actions">
                        <button class="btn btn-primary btn-lg" onclick="openBookingModal()">
                            <i class="fas fa-phone"></i> Gọi Ngay
                        </button>
                        <button class="btn btn-secondary btn-lg" onclick="scrollToServices()">
                            <i class="fas fa-info-circle"></i> Xem Dịch Vụ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services">
        <div class="container">
            <div class="section-header">
                <h2>Dịch Vụ Của Chúng Tôi</h2>
                <p>Đa dạng các loại hình dịch vụ taxi phù hợp với mọi nhu cầu</p>
            </div>
            
            <div class="services-grid" id="services-grid">
                <!-- Dynamic services -->
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section id="booking" class="booking">
        <div class="container">
            <div class="section-header">
                <h2>Đặt Xe Online</h2>
                <p>Điền thông tin để đặt xe nhanh chóng</p>
            </div>
            
            <div class="booking-form-container">
                <form id="booking-form" class="booking-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer-name">Họ và tên *</label>
                            <input type="text" id="customer-name" name="customerName" required>
                        </div>
                        <div class="form-group">
                            <label for="customer-phone">Số điện thoại *</label>
                            <input type="tel" id="customer-phone" name="customerPhone" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="pickup-location">Điểm đón *</label>
                            <input type="text" id="pickup-location" name="pickupLocation" required>
                        </div>
                        <div class="form-group">
                            <label for="destination">Điểm đến *</label>
                            <input type="text" id="destination" name="destination" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="service-type">Loại dịch vụ *</label>
                            <select id="service-type" name="serviceType" required>
                                <option value="">Chọn loại dịch vụ</option>
                                <!-- Dynamic options -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="pickup-time">Thời gian đón</label>
                            <input type="datetime-local" id="pickup-time" name="pickupTime">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Ghi chú</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="Ghi chú thêm (nếu có)"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane"></i> Gửi Yêu Cầu
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2>Liên Hệ</h2>
                <p>Hãy liên hệ với chúng tôi để được hỗ trợ tốt nhất</p>
            </div>
            
            <div class="contact-content">
                <div class="contact-info" id="contact-info">
                    <!-- Dynamic contact info -->
                </div>
                
                <div class="contact-form">
                    <form id="contact-form">
                        <div class="form-group">
                            <input type="text" name="name" placeholder="Họ và tên" required>
                        </div>
                        <div class="form-group">
                            <input type="email" name="email" placeholder="Email" required>
                        </div>
                        <div class="form-group">
                            <input type="text" name="subject" placeholder="Tiêu đề" required>
                        </div>
                        <div class="form-group">
                            <textarea name="message" rows="5" placeholder="Nội dung tin nhắn" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Gửi Tin Nhắn
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="footer" class="footer">
        <div class="container">
            <div class="footer-content" id="footer-content">
                <!-- Dynamic footer content -->
            </div>
            
            <div class="footer-bottom">
                <div class="copyright">
                    <p>&copy; <span id="current-year"></span> <span id="footer-company-name">Taxi Booking CMS</span>. All rights reserved.</p>
                </div>
                <div class="footer-links">
                    <a href="/admin" target="_blank">Admin Panel</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Contact Buttons -->
    <div class="floating-contacts" id="floating-contacts">
        <!-- Dynamic floating buttons -->
    </div>

    <!-- Booking Modal -->
    <div id="booking-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Đặt Xe Nhanh</h3>
                <button class="modal-close" onclick="closeBookingModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="quick-booking-form">
                    <div class="form-group">
                        <input type="tel" id="quick-phone" placeholder="Số điện thoại của bạn" required>
                    </div>
                    <div class="form-group">
                        <input type="text" id="quick-location" placeholder="Vị trí hiện tại" required>
                    </div>
                    <button class="btn btn-primary btn-block" onclick="submitQuickBooking()">
                        <i class="fas fa-phone"></i> Gọi Ngay
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="notification" class="notification">
        <div class="notification-content">
            <i class="notification-icon"></i>
            <span class="notification-message"></span>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="assets/js/data-manager.js"></script>
    <script src="assets/js/components.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
