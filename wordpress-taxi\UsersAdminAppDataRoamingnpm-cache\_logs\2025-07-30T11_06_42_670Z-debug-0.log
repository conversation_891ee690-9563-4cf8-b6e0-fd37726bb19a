0 verbose cli C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@11.2.0
2 info using node@v22.14.0
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:c:\xampp\htdocs\duan\wordpress-taxi\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm install
8 verbose argv "install" "--package-lock-only"
9 verbose logfile logs-max:10 dir:C:\xampp\htdocs\duan\wordpress-taxi\UsersAdminAppDataRoamingnpm-cache\_logs\2025-07-30T11_06_42_670Z-
10 verbose logfile C:\xampp\htdocs\duan\wordpress-taxi\UsersAdminAppDataRoamingnpm-cache\_logs\2025-07-30T11_06_42_670Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly packumentCache heap:2197815296 maxSize:********* maxEntrySize:*********
13 silly logfile done cleaning log files
14 silly idealTree buildDeps
15 silly audit bulk request {
15 silly audit   accepts: [ '1.3.8' ],
15 silly audit   negotiator: [ '0.6.3', '0.6.4' ],
15 silly audit   anymatch: [ '3.1.3' ],
15 silly audit   'append-field': [ '1.0.0' ],
15 silly audit   'array-flatten': [ '1.1.1' ],
15 silly audit   'aws-ssl-profiles': [ '1.1.2' ],
15 silly audit   'balanced-match': [ '1.0.2' ],
15 silly audit   'basic-auth': [ '2.0.1' ],
15 silly audit   'safe-buffer': [ '5.1.2', '5.2.1' ],
15 silly audit   bcryptjs: [ '2.4.3' ],
15 silly audit   'binary-extensions': [ '2.3.0' ],
15 silly audit   'body-parser': [ '1.20.3' ],
15 silly audit   'brace-expansion': [ '1.1.12' ],
15 silly audit   braces: [ '3.0.3' ],
15 silly audit   'buffer-equal-constant-time': [ '1.0.1' ],
15 silly audit   'buffer-from': [ '1.1.2' ],
15 silly audit   busboy: [ '1.6.0' ],
15 silly audit   bytes: [ '3.1.2' ],
15 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
15 silly audit   'call-bound': [ '1.0.4' ],
15 silly audit   chokidar: [ '3.6.0' ],
15 silly audit   compressible: [ '2.0.18' ],
15 silly audit   compression: [ '1.8.1' ],
15 silly audit   'concat-map': [ '0.0.1' ],
15 silly audit   'concat-stream': [ '1.6.2' ],
15 silly audit   'content-disposition': [ '0.5.4' ],
15 silly audit   'content-type': [ '1.0.5' ],
15 silly audit   cookie: [ '0.7.1' ],
15 silly audit   'cookie-signature': [ '1.0.6' ],
15 silly audit   'core-util-is': [ '1.0.3' ],
15 silly audit   cors: [ '2.8.5' ],
15 silly audit   debug: [ '2.6.9', '4.4.1' ],
15 silly audit   denque: [ '2.1.0' ],
15 silly audit   depd: [ '2.0.0' ],
15 silly audit   destroy: [ '1.2.0' ],
15 silly audit   dotenv: [ '16.6.1' ],
15 silly audit   'dunder-proto': [ '1.0.1' ],
15 silly audit   'ecdsa-sig-formatter': [ '1.0.11' ],
15 silly audit   'ee-first': [ '1.1.1' ],
15 silly audit   encodeurl: [ '2.0.0', '1.0.2' ],
15 silly audit   'es-define-property': [ '1.0.1' ],
15 silly audit   'es-errors': [ '1.3.0' ],
15 silly audit   'es-object-atoms': [ '1.1.1' ],
15 silly audit   'escape-html': [ '1.0.3' ],
15 silly audit   etag: [ '1.8.1' ],
15 silly audit   express: [ '4.21.2' ],
15 silly audit   'express-rate-limit': [ '6.11.2' ],
15 silly audit   'express-validator': [ '7.2.1' ],
15 silly audit   'fill-range': [ '7.1.1' ],
15 silly audit   finalhandler: [ '1.3.1' ],
15 silly audit   forwarded: [ '0.2.0' ],
15 silly audit   fresh: [ '0.5.2' ],
15 silly audit   fsevents: [ '2.3.3' ],
15 silly audit   'function-bind': [ '1.1.2' ],
15 silly audit   'generate-function': [ '2.3.1' ],
15 silly audit   'get-intrinsic': [ '1.3.0' ],
15 silly audit   'get-proto': [ '1.0.1' ],
15 silly audit   'glob-parent': [ '5.1.2' ],
15 silly audit   gopd: [ '1.2.0' ],
15 silly audit   'has-flag': [ '3.0.0' ],
15 silly audit   'has-symbols': [ '1.1.0' ],
15 silly audit   hasown: [ '2.0.2' ],
15 silly audit   helmet: [ '7.2.0' ],
15 silly audit   'http-errors': [ '2.0.0' ],
15 silly audit   'iconv-lite': [ '0.4.24', '0.6.3' ],
15 silly audit   'ignore-by-default': [ '1.0.1' ],
15 silly audit   inherits: [ '2.0.4' ],
15 silly audit   'ipaddr.js': [ '1.9.1' ],
15 silly audit   'is-binary-path': [ '2.1.0' ],
15 silly audit   'is-extglob': [ '2.1.1' ],
15 silly audit   'is-glob': [ '4.0.3' ],
15 silly audit   'is-number': [ '7.0.0' ],
15 silly audit   'is-property': [ '1.0.2' ],
15 silly audit   isarray: [ '1.0.0' ],
15 silly audit   jsonwebtoken: [ '9.0.2' ],
15 silly audit   ms: [ '2.1.3', '2.0.0' ],
15 silly audit   jwa: [ '1.4.2' ],
15 silly audit   jws: [ '3.2.2' ],
15 silly audit   lodash: [ '4.17.21' ],
15 silly audit   'lodash.includes': [ '4.3.0' ],
15 silly audit   'lodash.isboolean': [ '3.0.3' ],
15 silly audit   'lodash.isinteger': [ '4.0.4' ],
15 silly audit   'lodash.isnumber': [ '3.0.3' ],
15 silly audit   'lodash.isplainobject': [ '4.0.6' ],
15 silly audit   'lodash.isstring': [ '4.0.1' ],
15 silly audit   'lodash.once': [ '4.1.1' ],
15 silly audit   long: [ '5.3.2' ],
15 silly audit   'lru-cache': [ '7.18.3' ],
15 silly audit   'lru.min': [ '1.1.2' ],
15 silly audit   'math-intrinsics': [ '1.1.0' ],
15 silly audit   'media-typer': [ '0.3.0' ],
15 silly audit   'merge-descriptors': [ '1.0.3' ],
15 silly audit   methods: [ '1.1.2' ],
15 silly audit   mime: [ '1.6.0' ],
15 silly audit   'mime-db': [ '1.54.0', '1.52.0' ],
15 silly audit   'mime-types': [ '2.1.35' ],
15 silly audit   minimatch: [ '3.1.2' ],
15 silly audit   minimist: [ '1.2.8' ],
15 silly audit   mkdirp: [ '0.5.6' ],
15 silly audit   morgan: [ '1.10.1' ],
15 silly audit   'on-finished': [ '2.3.0', '2.4.1' ],
15 silly audit   multer: [ '1.4.5-lts.2' ],
15 silly audit   mysql2: [ '3.14.2' ],
15 silly audit   'named-placeholders': [ '1.1.3' ],
15 silly audit   nodemon: [ '3.1.10' ],
15 silly audit   'normalize-path': [ '3.0.0' ],
15 silly audit   'object-assign': [ '4.1.1' ],
15 silly audit   'object-inspect': [ '1.13.4' ],
15 silly audit   'on-headers': [ '1.1.0' ],
15 silly audit   parseurl: [ '1.3.3' ],
15 silly audit   'path-to-regexp': [ '0.1.12' ],
15 silly audit   picomatch: [ '2.3.1' ],
15 silly audit   'process-nextick-args': [ '2.0.1' ],
15 silly audit   'proxy-addr': [ '2.0.7' ],
15 silly audit   'pstree.remy': [ '1.1.8' ],
15 silly audit   qs: [ '6.13.0' ],
15 silly audit   'range-parser': [ '1.2.1' ],
15 silly audit   'raw-body': [ '2.5.2' ],
15 silly audit   'readable-stream': [ '2.3.8' ],
15 silly audit   readdirp: [ '3.6.0' ],
15 silly audit   'safer-buffer': [ '2.1.2' ],
15 silly audit   semver: [ '7.7.2' ],
15 silly audit   send: [ '0.19.0' ],
15 silly audit   'seq-queue': [ '0.0.5' ],
15 silly audit   'serve-static': [ '1.16.2' ],
15 silly audit   setprototypeof: [ '1.2.0' ],
15 silly audit   'side-channel': [ '1.1.0' ],
15 silly audit   'side-channel-list': [ '1.0.0' ],
15 silly audit   'side-channel-map': [ '1.0.1' ],
15 silly audit   'side-channel-weakmap': [ '1.0.2' ],
15 silly audit   'simple-update-notifier': [ '2.0.0' ],
15 silly audit   sqlstring: [ '2.3.3' ],
15 silly audit   statuses: [ '2.0.1' ],
15 silly audit   streamsearch: [ '1.1.0' ],
15 silly audit   string_decoder: [ '1.1.1' ],
15 silly audit   'supports-color': [ '5.5.0' ],
15 silly audit   'to-regex-range': [ '5.0.1' ],
15 silly audit   toidentifier: [ '1.0.1' ],
15 silly audit   touch: [ '3.1.1' ],
15 silly audit   'type-is': [ '1.6.18' ],
15 silly audit   typedarray: [ '0.0.6' ],
15 silly audit   undefsafe: [ '2.0.5' ],
15 silly audit   unpipe: [ '1.0.0' ],
15 silly audit   'util-deprecate': [ '1.0.2' ],
15 silly audit   'utils-merge': [ '1.0.1' ],
15 silly audit   validator: [ '13.12.0' ],
15 silly audit   vary: [ '1.1.2' ],
15 silly audit   xtend: [ '4.0.2' ]
15 silly audit }
16 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 860ms
17 silly audit report {}
18 verbose cwd c:\xampp\htdocs\duan\wordpress-taxi
19 verbose os Windows_NT 10.0.26100
20 verbose node v22.14.0
21 verbose npm  v11.2.0
22 verbose exit 0
23 info ok
