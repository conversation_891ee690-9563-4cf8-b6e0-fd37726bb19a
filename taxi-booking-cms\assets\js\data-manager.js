/**
 * Data Manager - <PERSON><PERSON><PERSON><PERSON> lý dữ liệu cho Taxi Booking CMS
 * Sử dụng localStorage để lưu trữ dữ liệu
 */

class DataManager {
    constructor() {
        this.storagePrefix = 'taxi_cms_';
        this.defaultData = this.getDefaultData();
        this.initializeData();
    }

    /**
     * Khởi tạo dữ liệu mặc định
     */
    getDefaultData() {
        return {
            settings: {
                siteName: 'Taxi Booking CMS',
                siteTitle: 'Dịch Vụ Taxi Chuyên Nghiệp',
                siteDescription: 'An toàn - Nhanh chóng - Uy tín',
                logo: 'assets/images/logo.png',
                phone: '0123 456 789',
                email: '<EMAIL>',
                address: '123 Đường ABC, Quận XYZ, TP.HCM',
                companyName: 'Công ty TNHH Taxi Booking',
                socialLinks: [
                    { name: 'facebook', url: 'https://facebook.com', icon: 'fab fa-facebook' },
                    { name: 'zalo', url: 'https://zalo.me', icon: 'fab fa-telegram' },
                    { name: 'youtube', url: 'https://youtube.com', icon: 'fab fa-youtube' }
                ],
                heroTitle: 'Dịch Vụ Taxi Chuyên Nghiệp',
                heroSubtitle: 'An toàn - Nhanh chóng - Uy tín',
                heroSlides: [
                    {
                        id: 1,
                        image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=1200',
                        title: 'Dịch vụ taxi chuyên nghiệp',
                        active: true
                    },
                    {
                        id: 2,
                        image: 'https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=1200',
                        title: 'Xe đời mới, sạch sẽ',
                        active: false
                    }
                ]
            },
            services: [
                {
                    id: 1,
                    name: 'Taxi Sân Bay',
                    description: 'Dịch vụ đưa đón sân bay 24/7, xe đời mới, tài xế kinh nghiệm',
                    price: '200,000đ',
                    priceUnit: '/chuyến',
                    icon: 'fas fa-plane',
                    image: 'https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=400',
                    features: [
                        'Đưa đón 24/7',
                        'Xe đời mới',
                        'Tài xế kinh nghiệm',
                        'Giá cố định'
                    ],
                    active: true,
                    featured: true
                },
                {
                    id: 2,
                    name: 'Taxi Đường Dài',
                    description: 'Dịch vụ taxi đi tỉnh, du lịch, giá cả hợp lý',
                    price: '15,000đ',
                    priceUnit: '/km',
                    icon: 'fas fa-road',
                    image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400',
                    features: [
                        'Giá theo km',
                        'Xe thoải mái',
                        'Tài xế địa phương',
                        'Hỗ trợ 24/7'
                    ],
                    active: true,
                    featured: false
                },
                {
                    id: 3,
                    name: 'Taxi Du Lịch',
                    description: 'Thuê xe theo ngày cho chuyến du lịch, tham quan',
                    price: '1,500,000đ',
                    priceUnit: '/ngày',
                    icon: 'fas fa-map-marked-alt',
                    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
                    features: [
                        'Thuê theo ngày',
                        'Tài xế hướng dẫn',
                        'Xe 4-7 chỗ',
                        'Bao xăng'
                    ],
                    active: true,
                    featured: false
                }
            ],
            navigation: [
                { name: 'Trang Chủ', url: '#hero', active: true },
                { name: 'Dịch Vụ', url: '#services', active: true },
                { name: 'Đặt Xe', url: '#booking', active: true },
                { name: 'Liên Hệ', url: '#contact', active: true }
            ],
            contactInfo: [
                {
                    id: 1,
                    title: 'Điện Thoại',
                    content: '0123 456 789',
                    icon: 'fas fa-phone',
                    link: 'tel:0123456789'
                },
                {
                    id: 2,
                    title: 'Email',
                    content: '<EMAIL>',
                    icon: 'fas fa-envelope',
                    link: 'mailto:<EMAIL>'
                },
                {
                    id: 3,
                    title: 'Địa Chỉ',
                    content: '123 Đường ABC, Quận XYZ, TP.HCM',
                    icon: 'fas fa-map-marker-alt',
                    link: '#'
                },
                {
                    id: 4,
                    title: 'Giờ Làm Việc',
                    content: '24/7 - Phục vụ tất cả các ngày',
                    icon: 'fas fa-clock',
                    link: '#'
                }
            ],
            floatingContacts: [
                {
                    id: 1,
                    name: 'phone',
                    title: 'Gọi ngay',
                    icon: 'fas fa-phone',
                    link: 'tel:0123456789',
                    color: '#27ae60',
                    active: true
                },
                {
                    id: 2,
                    name: 'zalo',
                    title: 'Chat Zalo',
                    icon: 'fab fa-telegram',
                    link: 'https://zalo.me/0123456789',
                    color: '#0084ff',
                    active: true
                }
            ],
            footerSections: [
                {
                    id: 1,
                    title: 'Về Chúng Tôi',
                    content: 'Công ty taxi hàng đầu với hơn 10 năm kinh nghiệm trong lĩnh vực vận tải hành khách.',
                    type: 'text'
                },
                {
                    id: 2,
                    title: 'Dịch Vụ',
                    type: 'links',
                    links: [
                        { name: 'Taxi Sân Bay', url: '#services' },
                        { name: 'Taxi Đường Dài', url: '#services' },
                        { name: 'Taxi Du Lịch', url: '#services' },
                        { name: 'Thuê Xe Theo Tháng', url: '#services' }
                    ]
                },
                {
                    id: 3,
                    title: 'Liên Hệ',
                    type: 'contact',
                    contacts: [
                        { icon: 'fas fa-phone', text: '0123 456 789', link: 'tel:0123456789' },
                        { icon: 'fas fa-envelope', text: '<EMAIL>', link: 'mailto:<EMAIL>' },
                        { icon: 'fas fa-map-marker-alt', text: '123 Đường ABC, Quận XYZ, TP.HCM', link: '#' }
                    ]
                }
            ],
            bookings: [],
            messages: []
        };
    }

    /**
     * Khởi tạo dữ liệu nếu chưa có
     */
    initializeData() {
        Object.keys(this.defaultData).forEach(key => {
            if (!this.get(key)) {
                this.set(key, this.defaultData[key]);
            }
        });
    }

    /**
     * Lấy dữ liệu từ localStorage
     */
    get(key) {
        try {
            const data = localStorage.getItem(this.storagePrefix + key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error getting data:', error);
            return null;
        }
    }

    /**
     * Lưu dữ liệu vào localStorage
     */
    set(key, value) {
        try {
            localStorage.setItem(this.storagePrefix + key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Error setting data:', error);
            return false;
        }
    }

    /**
     * Xóa dữ liệu
     */
    remove(key) {
        try {
            localStorage.removeItem(this.storagePrefix + key);
            return true;
        } catch (error) {
            console.error('Error removing data:', error);
            return false;
        }
    }

    /**
     * Lấy tất cả dữ liệu
     */
    getAll() {
        const data = {};
        Object.keys(this.defaultData).forEach(key => {
            data[key] = this.get(key);
        });
        return data;
    }

    /**
     * Reset về dữ liệu mặc định
     */
    reset() {
        Object.keys(this.defaultData).forEach(key => {
            this.set(key, this.defaultData[key]);
        });
    }

    /**
     * CRUD Operations cho Services
     */
    getServices() {
        return this.get('services') || [];
    }

    addService(service) {
        const services = this.getServices();
        service.id = Date.now();
        services.push(service);
        return this.set('services', services);
    }

    updateService(id, updatedService) {
        const services = this.getServices();
        const index = services.findIndex(s => s.id === id);
        if (index !== -1) {
            services[index] = { ...services[index], ...updatedService };
            return this.set('services', services);
        }
        return false;
    }

    deleteService(id) {
        const services = this.getServices();
        const filteredServices = services.filter(s => s.id !== id);
        return this.set('services', filteredServices);
    }

    /**
     * CRUD Operations cho Bookings
     */
    getBookings() {
        return this.get('bookings') || [];
    }

    addBooking(booking) {
        const bookings = this.getBookings();
        booking.id = Date.now();
        booking.createdAt = new Date().toISOString();
        booking.status = 'pending';
        bookings.unshift(booking);
        return this.set('bookings', bookings);
    }

    updateBooking(id, updatedBooking) {
        const bookings = this.getBookings();
        const index = bookings.findIndex(b => b.id === id);
        if (index !== -1) {
            bookings[index] = { ...bookings[index], ...updatedBooking };
            return this.set('bookings', bookings);
        }
        return false;
    }

    deleteBooking(id) {
        const bookings = this.getBookings();
        const filteredBookings = bookings.filter(b => b.id !== id);
        return this.set('bookings', filteredBookings);
    }

    /**
     * CRUD Operations cho Messages
     */
    getMessages() {
        return this.get('messages') || [];
    }

    addMessage(message) {
        const messages = this.getMessages();
        message.id = Date.now();
        message.createdAt = new Date().toISOString();
        message.status = 'unread';
        messages.unshift(message);
        return this.set('messages', messages);
    }

    updateMessage(id, updatedMessage) {
        const messages = this.getMessages();
        const index = messages.findIndex(m => m.id === id);
        if (index !== -1) {
            messages[index] = { ...messages[index], ...updatedMessage };
            return this.set('messages', messages);
        }
        return false;
    }

    deleteMessage(id) {
        const messages = this.getMessages();
        const filteredMessages = messages.filter(m => m.id !== id);
        return this.set('messages', filteredMessages);
    }

    /**
     * Settings Operations
     */
    getSettings() {
        return this.get('settings') || this.defaultData.settings;
    }

    updateSettings(newSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = { ...currentSettings, ...newSettings };
        return this.set('settings', updatedSettings);
    }

    /**
     * Export/Import Data
     */
    exportData() {
        return JSON.stringify(this.getAll(), null, 2);
    }

    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            Object.keys(data).forEach(key => {
                this.set(key, data[key]);
            });
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    /**
     * Statistics
     */
    getStats() {
        const bookings = this.getBookings();
        const messages = this.getMessages();
        const services = this.getServices();

        return {
            totalBookings: bookings.length,
            pendingBookings: bookings.filter(b => b.status === 'pending').length,
            completedBookings: bookings.filter(b => b.status === 'completed').length,
            totalMessages: messages.length,
            unreadMessages: messages.filter(m => m.status === 'unread').length,
            activeServices: services.filter(s => s.active).length,
            totalServices: services.length
        };
    }
}

// Khởi tạo DataManager global
window.dataManager = new DataManager();
