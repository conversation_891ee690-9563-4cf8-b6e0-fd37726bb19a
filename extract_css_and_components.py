#!/usr/bin/env python3
"""
Extract CSS and Create Components
Tách CSS inline ra file riêng và tạo các component để làm nhẹ index.html
"""

import os
import re
from pathlib import Path
from bs4 import BeautifulSoup

class CSSAndComponentExtractor:
    def __init__(self, html_file):
        self.html_file = Path(html_file)
        self.base_dir = self.html_file.parent
        self.css_dir = self.base_dir / 'css'
        self.components_dir = self.base_dir / 'components'
        
        # Create directories
        self.css_dir.mkdir(exist_ok=True)
        self.components_dir.mkdir(exist_ok=True)
        
        self.extracted_css = []
        self.components = {}
    
    def extract_inline_css(self, soup):
        """Extract all inline CSS from HTML"""
        print("Extracting inline CSS...")
        
        css_content = ""
        css_counter = 1
        
        # Find all style tags
        style_tags = soup.find_all('style')
        
        for style_tag in style_tags:
            if style_tag.string:
                css_id = style_tag.get('id', f'inline-css-{css_counter}')
                
                # Clean CSS content
                css_text = style_tag.string.strip()
                if css_text:
                    css_content += f"/* {css_id} */\n{css_text}\n\n"
                    css_counter += 1
                
                # Remove the style tag
                style_tag.decompose()
        
        # Save extracted CSS
        if css_content:
            css_file = self.css_dir / 'extracted-inline.css'
            with open(css_file, 'w', encoding='utf-8') as f:
                f.write(css_content)
            
            print(f"Extracted CSS saved to: {css_file}")
            return f'css/{css_file.name}'
        
        return None
    
    def create_header_component(self, soup):
        """Extract header section to component"""
        print("Creating header component...")
        
        # Find header element
        header = soup.find('header')
        if not header:
            # Try to find by class
            header = soup.find('div', class_=re.compile(r'header'))
        
        if header:
            header_html = str(header)
            
            # Save header component
            header_file = self.components_dir / 'header.html'
            with open(header_file, 'w', encoding='utf-8') as f:
                f.write(header_html)
            
            # Replace with placeholder
            placeholder = soup.new_tag('div', id='header-placeholder')
            placeholder.string = '<!-- Header Component -->'
            header.replace_with(placeholder)
            
            print(f"Header component saved to: {header_file}")
            return True
        
        return False
    
    def create_footer_component(self, soup):
        """Extract footer section to component"""
        print("Creating footer component...")
        
        # Find footer element
        footer = soup.find('footer')
        if not footer:
            # Try to find by class
            footer = soup.find('div', class_=re.compile(r'footer'))
        
        if footer:
            footer_html = str(footer)
            
            # Save footer component
            footer_file = self.components_dir / 'footer.html'
            with open(footer_file, 'w', encoding='utf-8') as f:
                f.write(footer_html)
            
            # Replace with placeholder
            placeholder = soup.new_tag('div', id='footer-placeholder')
            placeholder.string = '<!-- Footer Component -->'
            footer.replace_with(placeholder)
            
            print(f"Footer component saved to: {footer_file}")
            return True
        
        return False
    
    def create_navigation_component(self, soup):
        """Extract navigation to component"""
        print("Creating navigation component...")
        
        # Find navigation elements
        nav_elements = soup.find_all('nav')
        if not nav_elements:
            nav_elements = soup.find_all('div', class_=re.compile(r'nav|menu'))
        
        if nav_elements:
            nav_html = ""
            for nav in nav_elements:
                nav_html += str(nav) + "\n"
            
            # Save navigation component
            nav_file = self.components_dir / 'navigation.html'
            with open(nav_file, 'w', encoding='utf-8') as f:
                f.write(nav_html)
            
            # Replace with placeholders
            for i, nav in enumerate(nav_elements):
                placeholder = soup.new_tag('div', id=f'nav-placeholder-{i}')
                placeholder.string = f'<!-- Navigation Component {i} -->'
                nav.replace_with(placeholder)
            
            print(f"Navigation component saved to: {nav_file}")
            return True
        
        return False
    
    def create_contact_buttons_component(self, soup):
        """Extract floating contact buttons to component"""
        print("Creating contact buttons component...")
        
        # Find contact button elements
        contact_buttons = soup.find_all('div', class_=re.compile(r'button-contact|phone-vr|contact-float'))
        
        if contact_buttons:
            contact_html = ""
            for button in contact_buttons:
                contact_html += str(button) + "\n"
            
            # Save contact buttons component
            contact_file = self.components_dir / 'contact-buttons.html'
            with open(contact_file, 'w', encoding='utf-8') as f:
                f.write(contact_html)
            
            # Replace with placeholder
            for i, button in enumerate(contact_buttons):
                placeholder = soup.new_tag('div', id=f'contact-placeholder-{i}')
                placeholder.string = f'<!-- Contact Buttons Component {i} -->'
                button.replace_with(placeholder)
            
            print(f"Contact buttons component saved to: {contact_file}")
            return True
        
        return False
    
    def create_booking_form_component(self, soup):
        """Extract booking form to component"""
        print("Creating booking form component...")
        
        # Find form elements
        forms = soup.find_all('form')
        wpcf7_forms = soup.find_all('div', class_=re.compile(r'wpcf7'))
        
        all_forms = forms + wpcf7_forms
        
        if all_forms:
            form_html = ""
            for form in all_forms:
                form_html += str(form) + "\n"
            
            # Save booking form component
            form_file = self.components_dir / 'booking-form.html'
            with open(form_file, 'w', encoding='utf-8') as f:
                f.write(form_html)
            
            # Replace with placeholder
            for i, form in enumerate(all_forms):
                placeholder = soup.new_tag('div', id=f'form-placeholder-{i}')
                placeholder.string = f'<!-- Booking Form Component {i} -->'
                form.replace_with(placeholder)
            
            print(f"Booking form component saved to: {form_file}")
            return True
        
        return False
    
    def optimize_css_links(self, soup, extracted_css_file):
        """Optimize CSS links and add extracted CSS"""
        print("Optimizing CSS links...")
        
        # Find head tag
        head = soup.find('head')
        if not head:
            return
        
        # Add extracted CSS link
        if extracted_css_file:
            css_link = soup.new_tag('link', rel='stylesheet', href=extracted_css_file, type='text/css')
            head.append(css_link)
        
        # Group existing CSS links
        css_links = head.find_all('link', rel='stylesheet')
        external_css = []
        
        for link in css_links:
            href = link.get('href', '')
            if href.startswith('http'):
                external_css.append(href)
            elif href.startswith('css/'):
                # Keep local CSS files
                pass
        
        print(f"Found {len(css_links)} CSS links")
    
    def create_component_loader_js(self):
        """Create JavaScript to load components"""
        print("Creating component loader...")
        
        js_content = """
// Component Loader for VẠN DẶM TOUR
document.addEventListener('DOMContentLoaded', function() {
    
    // Load Header Component
    loadComponent('header-placeholder', 'components/header.html');
    
    // Load Footer Component  
    loadComponent('footer-placeholder', 'components/footer.html');
    
    // Load Navigation Components
    loadComponent('nav-placeholder-0', 'components/navigation.html');
    
    // Load Contact Buttons
    loadComponent('contact-placeholder-0', 'components/contact-buttons.html');
    
    // Load Booking Form
    loadComponent('form-placeholder-0', 'components/booking-form.html');
    
    function loadComponent(placeholderId, componentPath) {
        const placeholder = document.getElementById(placeholderId);
        if (placeholder) {
            fetch(componentPath)
                .then(response => response.text())
                .then(html => {
                    placeholder.outerHTML = html;
                })
                .catch(error => {
                    console.log('Component not found:', componentPath);
                });
        }
    }
});
"""
        
        js_file = self.base_dir / 'js' / 'component-loader.js'
        js_file.parent.mkdir(exist_ok=True)
        
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        print(f"Component loader saved to: {js_file}")
        return f'js/{js_file.name}'
    
    def process_html_file(self):
        """Main method to process HTML file"""
        print(f"Processing HTML file: {self.html_file}")
        
        # Read HTML file
        with open(self.html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # Extract inline CSS
        extracted_css_file = self.extract_inline_css(soup)
        
        # Create components
        self.create_header_component(soup)
        self.create_footer_component(soup)
        self.create_navigation_component(soup)
        self.create_contact_buttons_component(soup)
        self.create_booking_form_component(soup)
        
        # Optimize CSS
        self.optimize_css_links(soup, extracted_css_file)
        
        # Create component loader
        loader_js = self.create_component_loader_js()
        
        # Add component loader to HTML
        head = soup.find('head')
        if head:
            script_tag = soup.new_tag('script', src=loader_js, type='text/javascript')
            head.append(script_tag)
        
        # Save optimized HTML
        optimized_file = self.html_file.parent / 'index-optimized.html'
        with open(optimized_file, 'w', encoding='utf-8') as f:
            f.write(str(soup))
        
        print(f"Optimized HTML saved to: {optimized_file}")
        
        # Create summary
        self.create_summary()
        
        return optimized_file
    
    def create_summary(self):
        """Create summary of extraction"""
        summary = f"""# CSS and Component Extraction Summary

## Files Created:

### CSS Files:
- `css/extracted-inline.css` - All inline CSS extracted from HTML

### Component Files:
- `components/header.html` - Header section
- `components/footer.html` - Footer section  
- `components/navigation.html` - Navigation menus
- `components/contact-buttons.html` - Floating contact buttons
- `components/booking-form.html` - Booking forms

### JavaScript:
- `js/component-loader.js` - Loads components dynamically

### Optimized HTML:
- `index-optimized.html` - Lightweight HTML with components separated

## Benefits:
- ✅ Reduced HTML file size
- ✅ Better code organization
- ✅ Easier maintenance
- ✅ Reusable components
- ✅ Faster loading times

## Usage:
Open `index-optimized.html` instead of `index.html`
"""
        
        summary_file = self.base_dir / 'EXTRACTION_SUMMARY.md'
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        print(f"Summary saved to: {summary_file}")

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python extract_css_and_components.py <html_file>")
        print("Example: python extract_css_and_components.py vandamtour/index.html")
        sys.exit(1)
    
    html_file = sys.argv[1]
    
    if not os.path.exists(html_file):
        print(f"File not found: {html_file}")
        sys.exit(1)
    
    # Create extractor
    extractor = CSSAndComponentExtractor(html_file)
    
    # Process HTML file
    try:
        optimized_file = extractor.process_html_file()
        print(f"\n✅ Extraction completed successfully!")
        print(f"📁 Optimized file: {optimized_file}")
        print(f"🌐 Open: file://{optimized_file.absolute()}")
        
    except Exception as e:
        print(f"❌ Extraction failed: {e}")

if __name__ == "__main__":
    main()
