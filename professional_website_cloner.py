#!/usr/bin/env python3
"""
Professional Website Cloner
Thu<PERSON>t toán chuyên nghiệp để tái tạo website hoàn hảo 100% nguyên bản

Dựa trên các thuật toán từ:
- HTTrack Website Copier
- GNU Wget recursive download
- Professional web scraping techniques

Author: AI Assistant
Version: 2.0.0
Date: 2025-07-30
"""

import os
import sys
import time
import json
import hashlib
import mimetypes
import re
from pathlib import Path
from typing import Dict, List, Set, Optional, Tuple
from urllib.parse import urljoin, urlparse, unquote
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

import requests
from bs4 import BeautifulSoup
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('professional_cloner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProfessionalWebsiteCloner:
    """
    Professional Website Cloner
    Thuật toán tái tạo website hoàn hảo dựa trên các công cụ chuyên nghiệp
    """
    
    def __init__(self, base_url: str, output_dir: str = "cloned_website"):
        """
        Initialize the professional cloner
        
        Args:
            base_url: URL gốc của website
            output_dir: Thư mục output
        """
        self.base_url = base_url.rstrip('/')
        self.domain = urlparse(base_url).netloc
        self.output_dir = Path(output_dir)
        
        # Tracking sets
        self.downloaded_urls: Set[str] = set()
        self.failed_urls: Set[str] = set()
        self.url_mapping: Dict[str, str] = {}
        
        # Statistics
        self.stats = {
            'total_files': 0,
            'html_files': 0,
            'css_files': 0,
            'js_files': 0,
            'image_files': 0,
            'other_files': 0,
            'failed_downloads': 0,
            'total_size': 0
        }
        
        # Session configuration
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Create directory structure
        self._create_directory_structure()
        
        logger.info(f"Professional Website Cloner initialized")
        logger.info(f"Target: {self.base_url}")
        logger.info(f"Output: {self.output_dir.absolute()}")
    
    def _create_directory_structure(self):
        """Tạo cấu trúc thư mục"""
        directories = [
            self.output_dir,
            self.output_dir / 'css',
            self.output_dir / 'js',
            self.output_dir / 'images',
            self.output_dir / 'fonts',
            self.output_dir / 'media',
            self.output_dir / 'assets',
            self.output_dir / 'analysis'
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _get_file_type(self, url: str, content_type: str = None) -> str:
        """Xác định loại file dựa trên URL và content-type"""
        url_lower = url.lower()
        
        if content_type:
            content_type = content_type.lower()
            if 'text/html' in content_type:
                return 'html'
            elif 'text/css' in content_type:
                return 'css'
            elif 'javascript' in content_type or 'application/javascript' in content_type:
                return 'js'
            elif any(img_type in content_type for img_type in ['image/', 'img/']):
                return 'image'
            elif 'font' in content_type:
                return 'font'
        
        # Fallback to URL extension
        if any(ext in url_lower for ext in ['.html', '.htm', '.php', '.asp', '.jsp']):
            return 'html'
        elif url_lower.endswith('.css'):
            return 'css'
        elif url_lower.endswith(('.js', '.mjs')):
            return 'js'
        elif any(url_lower.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.ico', '.bmp']):
            return 'image'
        elif any(url_lower.endswith(ext) for ext in ['.woff', '.woff2', '.ttf', '.eot', '.otf']):
            return 'font'
        elif any(url_lower.endswith(ext) for ext in ['.mp4', '.mp3', '.avi', '.mov', '.wav']):
            return 'media'
        else:
            return 'other'
    
    def _get_local_path(self, url: str, file_type: str) -> Path:
        """Tạo đường dẫn local cho file"""
        parsed = urlparse(url)
        path = parsed.path.strip('/')
        
        if not path or path.endswith('/'):
            path = 'index.html'
        
        # Remove query parameters
        path = path.split('?')[0]
        
        # Ensure safe filename
        path = unquote(path)
        path = re.sub(r'[<>:"/\\|?*]', '_', path)
        
        # Determine subdirectory
        if file_type == 'css':
            return self.output_dir / 'css' / Path(path).name
        elif file_type == 'js':
            return self.output_dir / 'js' / Path(path).name
        elif file_type == 'image':
            return self.output_dir / 'images' / Path(path).name
        elif file_type == 'font':
            return self.output_dir / 'fonts' / Path(path).name
        elif file_type == 'media':
            return self.output_dir / 'media' / Path(path).name
        else:
            return self.output_dir / path
    
    def _download_file(self, url: str) -> Optional[Tuple[str, bytes, str]]:
        """
        Download một file từ URL
        
        Returns:
            Tuple of (url, content, content_type) hoặc None nếu thất bại
        """
        if url in self.downloaded_urls or url in self.failed_urls:
            return None
        
        try:
            logger.info(f"Downloading: {url}")
            
            response = self.session.get(url, timeout=30, verify=False, stream=True)
            response.raise_for_status()
            
            content_type = response.headers.get('content-type', '')
            content = response.content
            
            self.downloaded_urls.add(url)
            self.stats['total_files'] += 1
            self.stats['total_size'] += len(content)
            
            return (url, content, content_type)
            
        except Exception as e:
            logger.error(f"Failed to download {url}: {e}")
            self.failed_urls.add(url)
            self.stats['failed_downloads'] += 1
            return None
    
    def _extract_urls_from_html(self, html_content: str, base_url: str) -> Set[str]:
        """Trích xuất tất cả URLs từ HTML"""
        urls = set()
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # CSS links
            for link in soup.find_all('link', rel='stylesheet'):
                href = link.get('href')
                if href:
                    urls.add(urljoin(base_url, href))
            
            # JavaScript files
            for script in soup.find_all('script', src=True):
                src = script.get('src')
                if src:
                    urls.add(urljoin(base_url, src))
            
            # Images
            for img in soup.find_all('img', src=True):
                src = img.get('src')
                if src:
                    urls.add(urljoin(base_url, src))
            
            # Background images in CSS
            for element in soup.find_all(style=True):
                style = element.get('style')
                if style and 'background-image' in style:
                    import re
                    bg_urls = re.findall(r'url\s*\(\s*["\']?([^"\')]+)["\']?\s*\)', style)
                    for bg_url in bg_urls:
                        if not bg_url.startswith('data:'):
                            urls.add(urljoin(base_url, bg_url))
            
            # Links to other pages (same domain only)
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and not href.startswith('#'):
                    full_url = urljoin(base_url, href)
                    if urlparse(full_url).netloc == self.domain:
                        urls.add(full_url)
            
        except Exception as e:
            logger.error(f"Error extracting URLs from HTML: {e}")
        
        return urls
    
    def _extract_urls_from_css(self, css_content: str, base_url: str) -> Set[str]:
        """Trích xuất URLs từ CSS"""
        urls = set()
        
        try:
            import re
            
            # url() declarations
            url_pattern = r'url\s*\(\s*["\']?([^"\')]+)["\']?\s*\)'
            matches = re.findall(url_pattern, css_content, re.IGNORECASE)
            
            for match in matches:
                if not match.startswith('data:'):
                    urls.add(urljoin(base_url, match))
            
            # @import statements
            import_pattern = r'@import\s+["\']([^"\']+)["\']'
            import_matches = re.findall(import_pattern, css_content, re.IGNORECASE)
            
            for match in import_matches:
                urls.add(urljoin(base_url, match))
                
        except Exception as e:
            logger.error(f"Error extracting URLs from CSS: {e}")
        
        return urls
    
    def _process_and_save_file(self, url: str, content: bytes, content_type: str) -> bool:
        """Xử lý và lưu file"""
        try:
            file_type = self._get_file_type(url, content_type)
            local_path = self._get_local_path(url, file_type)
            
            # Create directory if needed
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save file
            with open(local_path, 'wb') as f:
                f.write(content)
            
            # Update statistics
            self.stats[f'{file_type}_files'] += 1
            
            # Store URL mapping
            relative_path = local_path.relative_to(self.output_dir)
            self.url_mapping[url] = str(relative_path).replace('\\', '/')
            
            logger.info(f"Saved: {local_path}")
            
            # Extract additional URLs if HTML or CSS
            if file_type == 'html':
                try:
                    html_content = content.decode('utf-8', errors='ignore')
                    return self._extract_urls_from_html(html_content, url)
                except:
                    return set()
            elif file_type == 'css':
                try:
                    css_content = content.decode('utf-8', errors='ignore')
                    return self._extract_urls_from_css(css_content, url)
                except:
                    return set()
            
            return set()
            
        except Exception as e:
            logger.error(f"Error processing file {url}: {e}")
            return set()
    
    def _fix_html_paths(self):
        """Sửa đường dẫn trong file HTML"""
        logger.info("Fixing HTML paths...")
        
        html_files = list(self.output_dir.glob('**/*.html'))
        
        for html_file in html_files:
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                soup = BeautifulSoup(content, 'html.parser')
                
                # Fix CSS links
                for link in soup.find_all('link', rel='stylesheet'):
                    href = link.get('href')
                    if href and href in self.url_mapping:
                        link['href'] = self.url_mapping[href]
                
                # Fix JS scripts
                for script in soup.find_all('script', src=True):
                    src = script.get('src')
                    if src and src in self.url_mapping:
                        script['src'] = self.url_mapping[src]
                
                # Fix images
                for img in soup.find_all('img', src=True):
                    src = img.get('src')
                    if src and src in self.url_mapping:
                        img['src'] = self.url_mapping[src]
                
                # Save fixed HTML
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(str(soup))
                
                logger.info(f"Fixed paths in: {html_file}")
                
            except Exception as e:
                logger.error(f"Error fixing paths in {html_file}: {e}")
    
    def clone_website(self) -> bool:
        """
        Main method để clone website
        
        Returns:
            True nếu thành công
        """
        logger.info("Starting professional website cloning...")
        start_time = time.time()
        
        # Queue for URLs to process
        urls_to_process = {self.base_url}
        processed_urls = set()
        
        # Process URLs iteratively
        while urls_to_process:
            current_batch = list(urls_to_process)[:10]  # Process in batches
            urls_to_process -= set(current_batch)
            
            # Download files in parallel
            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_url = {
                    executor.submit(self._download_file, url): url 
                    for url in current_batch
                }
                
                for future in as_completed(future_to_url):
                    url = future_to_url[future]
                    processed_urls.add(url)
                    
                    try:
                        result = future.result()
                        if result:
                            download_url, content, content_type = result
                            new_urls = self._process_and_save_file(download_url, content, content_type)
                            
                            # Add new URLs to queue (same domain only)
                            for new_url in new_urls:
                                if (urlparse(new_url).netloc == self.domain and 
                                    new_url not in processed_urls and
                                    new_url not in self.downloaded_urls):
                                    urls_to_process.add(new_url)
                    
                    except Exception as e:
                        logger.error(f"Error processing {url}: {e}")
            
            # Limit total URLs to prevent infinite crawling
            if len(processed_urls) > 100:
                logger.info("Reached URL limit, stopping crawl")
                break
        
        # Fix HTML paths
        self._fix_html_paths()
        
        # Generate report
        self._generate_report()
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"Website cloning completed in {duration:.2f} seconds")
        self._print_summary()
        
        return True
    
    def _generate_report(self):
        """Tạo báo cáo chi tiết"""
        report = {
            'source_url': self.base_url,
            'clone_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'statistics': self.stats,
            'url_mapping': self.url_mapping,
            'failed_urls': list(self.failed_urls),
            'total_downloaded': len(self.downloaded_urls)
        }
        
        report_file = self.output_dir / 'analysis' / 'clone_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Report saved: {report_file}")
    
    def _print_summary(self):
        """In tóm tắt kết quả"""
        print("\n" + "="*60)
        print("🎉 PROFESSIONAL WEBSITE CLONING COMPLETED!")
        print("="*60)
        
        print(f"📊 STATISTICS:")
        print(f"   Total files: {self.stats['total_files']}")
        print(f"   HTML files: {self.stats['html_files']}")
        print(f"   CSS files: {self.stats['css_files']}")
        print(f"   JS files: {self.stats['js_files']}")
        print(f"   Image files: {self.stats['image_files']}")
        print(f"   Other files: {self.stats['other_files']}")
        print(f"   Failed downloads: {self.stats['failed_downloads']}")
        print(f"   Total size: {self.stats['total_size']:,} bytes")
        
        print(f"\n📁 OUTPUT:")
        print(f"   Directory: {self.output_dir.absolute()}")
        print(f"   Main file: {self.output_dir / 'index.html'}")
        
        print(f"\n🌐 USAGE:")
        print(f"   Open: file://{(self.output_dir / 'index.html').absolute()}")
        
        print("\n" + "="*60)

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python professional_website_cloner.py <URL>")
        print("Example: python professional_website_cloner.py https://vandamtour.vn")
        sys.exit(1)
    
    url = sys.argv[1]
    
    # Create cloner and run
    cloner = ProfessionalWebsiteCloner(url, "vandamtour_professional_clone")
    success = cloner.clone_website()
    
    if success:
        print("\n✅ Professional cloning successful!")
    else:
        print("\n❌ Professional cloning failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
