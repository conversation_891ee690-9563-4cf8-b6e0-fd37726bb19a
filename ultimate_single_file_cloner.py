#!/usr/bin/env python3
"""
Ultimate Single File Website Cloner
Sử dụng Monolith và SingleFile CLI để tạo website hoàn hảo trong 1 file HTML

Dựa trên:
- Monolith (Rust CLI tool) - https://github.com/Y2Z/monolith
- SingleFile CLI (Node.js) - https://github.com/gil<PERSON>-lormeau/SingleFile

Author: AI Assistant
Version: 3.0.0
Date: 2025-07-30
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ultimate_cloner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UltimateSingleFileCloner:
    """
    Ultimate Single File Website Cloner
    Sử dụng các công cụ chuyên nghiệp nhất để tạo website hoàn hảo
    """
    
    def __init__(self, url: str, output_dir: str = "vandamtour_ultimate"):
        self.url = url
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        logger.info(f"Ultimate Single File Cloner initialized")
        logger.info(f"Target URL: {self.url}")
        logger.info(f"Output directory: {self.output_dir.absolute()}")
    
    def check_dependencies(self) -> dict:
        """Kiểm tra các dependencies có sẵn"""
        deps = {
            'monolith': False,
            'node': False,
            'npm': False,
            'single-file': False,
            'wget': False,
            'curl': False
        }
        
        # Check Monolith
        try:
            result = subprocess.run(['monolith', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['monolith'] = True
                logger.info(f"✓ Monolith found: {result.stdout.strip()}")
        except:
            logger.warning("✗ Monolith not found")
        
        # Check Node.js
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['node'] = True
                logger.info(f"✓ Node.js found: {result.stdout.strip()}")
        except:
            logger.warning("✗ Node.js not found")
        
        # Check npm
        try:
            result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['npm'] = True
                logger.info(f"✓ npm found: {result.stdout.strip()}")
        except:
            logger.warning("✗ npm not found")
        
        # Check SingleFile CLI
        try:
            result = subprocess.run(['single-file', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['single-file'] = True
                logger.info(f"✓ SingleFile CLI found: {result.stdout.strip()}")
        except:
            logger.warning("✗ SingleFile CLI not found")
        
        # Check wget
        try:
            result = subprocess.run(['wget', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['wget'] = True
                logger.info(f"✓ wget found")
        except:
            logger.warning("✗ wget not found")
        
        # Check curl
        try:
            result = subprocess.run(['curl', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                deps['curl'] = True
                logger.info(f"✓ curl found")
        except:
            logger.warning("✗ curl not found")
        
        return deps
    
    def install_monolith(self) -> bool:
        """Cài đặt Monolith"""
        logger.info("Installing Monolith...")
        
        try:
            # Try cargo install
            result = subprocess.run(['cargo', 'install', 'monolith'], 
                                  capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                logger.info("✓ Monolith installed via cargo")
                return True
        except:
            pass
        
        # Try download binary
        try:
            import requests
            import zipfile
            
            # Download for Windows
            if os.name == 'nt':
                url = "https://github.com/Y2Z/monolith/releases/latest/download/monolith-x86_64-pc-windows-gnu.exe"
                response = requests.get(url, timeout=60)
                if response.status_code == 200:
                    exe_path = self.output_dir / 'monolith.exe'
                    with open(exe_path, 'wb') as f:
                        f.write(response.content)
                    logger.info(f"✓ Monolith downloaded: {exe_path}")
                    return True
        except:
            pass
        
        logger.error("✗ Failed to install Monolith")
        return False
    
    def install_single_file_cli(self) -> bool:
        """Cài đặt SingleFile CLI"""
        logger.info("Installing SingleFile CLI...")
        
        try:
            result = subprocess.run(['npm', 'install', '-g', 'single-file-cli'], 
                                  capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                logger.info("✓ SingleFile CLI installed")
                return True
            else:
                logger.error(f"✗ Failed to install SingleFile CLI: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"✗ Failed to install SingleFile CLI: {e}")
            return False
    
    def clone_with_monolith(self) -> bool:
        """Clone website using Monolith"""
        logger.info("🔧 METHOD 1: Cloning with Monolith...")
        
        output_file = self.output_dir / 'vandamtour_monolith.html'
        
        try:
            # Monolith command with all options
            cmd = [
                'monolith',
                self.url,
                '-o', str(output_file),
                '-j',  # Include JavaScript
                '-i',  # Include images
                '-c',  # Include CSS
                '-f',  # Include fonts
                '-a',  # Include audio
                '-v',  # Include videos
                '--no-metadata',  # Don't include metadata
                '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
            
            logger.info(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                file_size = output_file.stat().st_size
                logger.info(f"✅ Monolith success! File: {output_file} ({file_size:,} bytes)")
                return True
            else:
                logger.error(f"❌ Monolith failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Monolith error: {e}")
            return False
    
    def clone_with_single_file(self) -> bool:
        """Clone website using SingleFile CLI"""
        logger.info("🔧 METHOD 2: Cloning with SingleFile CLI...")
        
        output_file = self.output_dir / 'vandamtour_singlefile.html'
        
        try:
            # SingleFile CLI command
            cmd = [
                'single-file',
                self.url,
                str(output_file),
                '--browser-executable-path', 'chrome',
                '--browser-args', '--no-sandbox --disable-setuid-sandbox',
                '--max-resource-size-enabled',
                '--max-resource-size', '50',
                '--compress-HTML',
                '--remove-unused-styles',
                '--remove-unused-fonts'
            ]
            
            logger.info(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                file_size = output_file.stat().st_size
                logger.info(f"✅ SingleFile success! File: {output_file} ({file_size:,} bytes)")
                return True
            else:
                logger.error(f"❌ SingleFile failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ SingleFile error: {e}")
            return False
    
    def clone_with_wget_single_file(self) -> bool:
        """Clone website using wget with single file option"""
        logger.info("🔧 METHOD 3: Cloning with wget (single file)...")
        
        output_file = self.output_dir / 'vandamtour_wget.html'
        
        try:
            cmd = [
                'wget',
                '--page-requisites',
                '--convert-links',
                '--adjust-extension',
                '--span-hosts',
                '--backup-converted',
                '--output-document', str(output_file),
                self.url
            ]
            
            logger.info(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                file_size = output_file.stat().st_size
                logger.info(f"✅ wget success! File: {output_file} ({file_size:,} bytes)")
                return True
            else:
                logger.error(f"❌ wget failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ wget error: {e}")
            return False
    
    def create_custom_single_file(self) -> bool:
        """Tạo single file HTML bằng cách custom"""
        logger.info("🔧 METHOD 4: Creating custom single file...")
        
        try:
            import requests
            from bs4 import BeautifulSoup
            import base64
            from urllib.parse import urljoin, urlparse
            
            # Download main HTML
            response = requests.get(self.url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Download and inline CSS
            for link in soup.find_all('link', rel='stylesheet'):
                href = link.get('href')
                if href:
                    css_url = urljoin(self.url, href)
                    try:
                        css_response = requests.get(css_url, timeout=30)
                        css_response.raise_for_status()
                        
                        # Create style tag
                        style_tag = soup.new_tag('style')
                        style_tag.string = css_response.text
                        link.replace_with(style_tag)
                        
                        logger.info(f"✓ Inlined CSS: {css_url}")
                    except:
                        logger.warning(f"✗ Failed to inline CSS: {css_url}")
            
            # Download and inline JavaScript
            for script in soup.find_all('script', src=True):
                src = script.get('src')
                if src:
                    js_url = urljoin(self.url, src)
                    try:
                        js_response = requests.get(js_url, timeout=30)
                        js_response.raise_for_status()
                        
                        # Update script tag
                        script['src'] = None
                        script.string = js_response.text
                        
                        logger.info(f"✓ Inlined JS: {js_url}")
                    except:
                        logger.warning(f"✗ Failed to inline JS: {js_url}")
            
            # Download and inline images as data URLs
            for img in soup.find_all('img', src=True):
                src = img.get('src')
                if src:
                    img_url = urljoin(self.url, src)
                    try:
                        img_response = requests.get(img_url, timeout=30)
                        img_response.raise_for_status()
                        
                        # Convert to data URL
                        content_type = img_response.headers.get('content-type', 'image/jpeg')
                        img_data = base64.b64encode(img_response.content).decode()
                        data_url = f"data:{content_type};base64,{img_data}"
                        
                        img['src'] = data_url
                        
                        logger.info(f"✓ Inlined image: {img_url}")
                    except:
                        logger.warning(f"✗ Failed to inline image: {img_url}")
            
            # Save single file
            output_file = self.output_dir / 'vandamtour_custom.html'
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(str(soup))
            
            file_size = output_file.stat().st_size
            logger.info(f"✅ Custom single file success! File: {output_file} ({file_size:,} bytes)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Custom single file error: {e}")
            return False
    
    def run_ultimate_cloning(self) -> bool:
        """Chạy tất cả phương pháp cloning"""
        logger.info("🚀 Starting Ultimate Single File Cloning...")
        
        start_time = time.time()
        
        # Check dependencies
        deps = self.check_dependencies()
        
        # Try to install missing tools
        if not deps['monolith']:
            self.install_monolith()
        
        if deps['node'] and deps['npm'] and not deps['single-file']:
            self.install_single_file_cli()
        
        # Re-check dependencies
        deps = self.check_dependencies()
        
        # Try all methods
        methods_tried = []
        successful_methods = []
        
        # Method 1: Monolith
        if deps['monolith']:
            methods_tried.append("Monolith")
            if self.clone_with_monolith():
                successful_methods.append("Monolith")
        
        # Method 2: SingleFile CLI
        if deps['single-file']:
            methods_tried.append("SingleFile CLI")
            if self.clone_with_single_file():
                successful_methods.append("SingleFile CLI")
        
        # Method 3: wget
        if deps['wget']:
            methods_tried.append("wget")
            if self.clone_with_wget_single_file():
                successful_methods.append("wget")
        
        # Method 4: Custom (always available)
        methods_tried.append("Custom")
        if self.create_custom_single_file():
            successful_methods.append("Custom")
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        self.print_summary(methods_tried, successful_methods, duration)
        
        return len(successful_methods) > 0
    
    def print_summary(self, methods_tried, successful_methods, duration):
        """In tóm tắt kết quả"""
        print("\n" + "="*60)
        print("🎉 ULTIMATE SINGLE FILE CLONING COMPLETED!")
        print("="*60)
        
        print(f"🎯 Target URL: {self.url}")
        print(f"📁 Output directory: {self.output_dir.absolute()}")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        
        print(f"\n🔧 Methods tried: {len(methods_tried)}")
        for i, method in enumerate(methods_tried, 1):
            print(f"   {i}. {method}")
        
        print(f"\n✅ Successful methods: {len(successful_methods)}")
        for i, method in enumerate(successful_methods, 1):
            print(f"   {i}. {method}")
        
        if successful_methods:
            print(f"\n📁 Generated files:")
            for file in self.output_dir.glob('*.html'):
                size = file.stat().st_size
                print(f"   - {file.name} ({size:,} bytes)")
            
            print(f"\n💡 Recommendation:")
            if "Monolith" in successful_methods:
                print("   Use vandamtour_monolith.html - Most complete and accurate")
            elif "SingleFile CLI" in successful_methods:
                print("   Use vandamtour_singlefile.html - Professional quality")
            elif "Custom" in successful_methods:
                print("   Use vandamtour_custom.html - Custom implementation")
            else:
                print(f"   Use {successful_methods[0]} result")
        
        else:
            print(f"\n❌ All methods failed!")
            print(f"   Please check network connection and dependencies")
        
        print("\n" + "="*60)

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python ultimate_single_file_cloner.py <URL>")
        print("Example: python ultimate_single_file_cloner.py https://vandamtour.vn")
        sys.exit(1)
    
    url = sys.argv[1]
    
    # Create cloner and run
    cloner = UltimateSingleFileCloner(url)
    success = cloner.run_ultimate_cloning()
    
    if success:
        print("\n🎉 Ultimate cloning successful!")
    else:
        print("\n❌ Ultimate cloning failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
