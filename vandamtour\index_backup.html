<!DOCTYPE html>
<html class="loading-site no-js" lang="vi">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="robots" content="index, follow" />
  <meta name="description"
    content="VẠN DẶM TOUR - Dịch vụ taxi uy tín, an toàn với giá cả hợp lý. Đặt xe ngay - G<PERSON>i là có, đi là thích. Hotline: 0823.141.862" />
  <meta name="keywords" content="taxi, van dam tour, đặt xe, taxi giá rẻ, taxi sân bay, taxi đường dài" />
  <meta name="author" content="VẠN DẶM TOUR" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://vandamtour.vn/" />
  <meta property="og:title" content="VẠN DẶM TOUR – G<PERSON><PERSON> là có – Đi là thích" />
  <meta property="og:description" content="Dịch vụ taxi uy tín, an toàn với giá cả hợp lý. Đặt xe ngay!" />
  <meta property="og:image" content="images/z6839990068883_fe9a5555d3e1c1c08323d80b3419de95.jpg" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://vandamtour.vn/" />
  <meta property="twitter:title" content="VẠN DẶM TOUR – Gọi là có – Đi là thích" />
  <meta property="twitter:description" content="Dịch vụ taxi uy tín, an toàn với giá cả hợp lý. Đặt xe ngay!" />
  <meta property="twitter:image" content="images/z6839990068883_fe9a5555d3e1c1c08323d80b3419de95.jpg" />

  <title>VẠN DẶM TOUR – Gọi là có – Đi là thích</title>

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="images/z6839990068883_fe9a5555d3e1c1c08323d80b3419de95.jpg" />
  <link rel="apple-touch-icon" href="images/z6839990068883_fe9a5555d3e1c1c08323d80b3419de95.jpg" />

  <!-- Preload Critical Resources -->
  <link rel="preload" href="css/style.css" as="style" />
  <link rel="preload" href="js/main.js" as="script" />
  <link rel="preload" href="images/slider-81090.jpg" as="image" />

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=K2D:wght@400;700&display=swap" rel="stylesheet" />

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />

  <!-- Stylesheets -->
  <link rel="stylesheet" href="css/style.css" />
  <link rel="stylesheet" href="css/header.css" />
  <link rel="stylesheet" href="css/main.css" />
  <link rel="stylesheet" href="css/footer.css" />
  <link rel="stylesheet" href="css/responsive.css" />

  <!-- Remove no-js class -->
  <script>
    (function (html) {
      html.className = html.className.replace(/\bno-js\b/, 'js')
    })(document.documentElement);
  </script>

  <!-- Schema.org Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "VẠN DẶM TOUR",
    "description": "Dịch vụ taxi uy tín, an toàn với giá cả hợp lý",
    "url": "https://vandamtour.vn",
    "telephone": "0823141862",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Phú Yên",
      "addressCountry": "VN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "13.0881",
      "longitude": "109.0928"
    },
    "openingHours": "Mo-Su 00:00-23:59",
    "priceRange": "$$",
    "serviceArea": {
      "@type": "GeoCircle",
      "geoMidpoint": {
        "@type": "GeoCoordinates",
        "latitude": "13.0881",
        "longitude": "109.0928"
      },
      "geoRadius": "100000"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Dịch vụ taxi",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Taxi giá rẻ",
            "description": "Dịch vụ taxi trong thành phố với giá cả hợp lý"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Taxi đường dài",
            "description": "Dịch vụ taxi liên tỉnh, đường dài"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Đưa đón sân bay",
            "description": "Dịch vụ đưa đón sân bay 24/7"
          }
        }
      ]
    }
  }
  </script>
</head>

<body class="home page-template page-template-page-blank">

  <!-- Skip Link for Accessibility -->
  <a class="skip-link screen-reader-text" href="#main">Bỏ qua nội dung</a>

  <!-- Main Wrapper -->
  <div id="wrapper">

    <!-- Header Component -->
    <div id="header-container"></div>

    <!-- Main Content Component -->
    <div id="main-container"></div>

    <!-- Footer Component -->
    <div id="footer-container"></div>

  </div>

  <!-- Loading Overlay -->
  <div id="loading-overlay" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
  ">
    <div style="text-align: center;">
      <div style="
        width: 50px;
        height: 50px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #446084;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      "></div>
      <p style="color: #446084; font-weight: bold;">Đang tải...</p>
    </div>
  </div>

  <style>
    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .screen-reader-text {
      position: absolute !important;
      clip-path: inset(50%);
      width: 1px;
      height: 1px;
      overflow: hidden;
    }

    .screen-reader-text:focus {
      background-color: #f1f1f1;
      border-radius: 3px;
      box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
      clip-path: none !important;
      color: #21759b;
      display: block;
      font-size: 14px;
      font-weight: bold;
      height: auto;
      left: 5px;
      line-height: normal;
      padding: 15px 23px 14px;
      text-decoration: none;
      top: 5px;
      width: auto;
      z-index: 100000;
    }
  </style>

  <!-- JavaScript -->
  <script src="js/components.js"></script>
  <script src="js/main.js"></script>
  <script src="js/form-handler.js"></script>

  <!-- Google Analytics (replace with your tracking ID) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'GA_TRACKING_ID');
  </script>

</body>

</html>