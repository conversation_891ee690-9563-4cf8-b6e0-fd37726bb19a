/* inline-css-1 */
img:is([sizes="auto" i], [sizes^="auto," i]) {
  contain-intrinsic-size: 3000px 1500px
}

/* wp-block-library-inline-css */
:root {
  --wp-admin-theme-color: #007cba;
  --wp-admin-theme-color--rgb: 0, 124, 186;
  --wp-admin-theme-color-darker-10: #006ba1;
  --wp-admin-theme-color-darker-10--rgb: 0, 107, 161;
  --wp-admin-theme-color-darker-20: #005a87;
  --wp-admin-theme-color-darker-20--rgb: 0, 90, 135;
  --wp-admin-border-width-focus: 2px;
  --wp-block-synced-color: #7a00df;
  --wp-block-synced-color--rgb: 122, 0, 223;
  --wp-bound-block-color: var(--wp-block-synced-color)
}

@media (min-resolution:192dpi) {
  :root {
    --wp-admin-border-width-focus: 1.5px
  }
}

.wp-element-button {
  cursor: pointer
}

:root {
  --wp--preset--font-size--normal: 16px;
  --wp--preset--font-size--huge: 42px
}

:root .has-very-light-gray-background-color {
  background-color: #eee
}

:root .has-very-dark-gray-background-color {
  background-color: #313131
}

:root .has-very-light-gray-color {
  color: #eee
}

:root .has-very-dark-gray-color {
  color: #313131
}

:root .has-vivid-green-cyan-to-vivid-cyan-blue-gradient-background {
  background: linear-gradient(135deg, #00d084, #0693e3)
}

:root .has-purple-crush-gradient-background {
  background: linear-gradient(135deg, #34e2e4, #4721fb 50%, #ab1dfe)
}

:root .has-hazy-dawn-gradient-background {
  background: linear-gradient(135deg, #faaca8, #dad0ec)
}

:root .has-subdued-olive-gradient-background {
  background: linear-gradient(135deg, #fafae1, #67a671)
}

:root .has-atomic-cream-gradient-background {
  background: linear-gradient(135deg, #fdd79a, #004a59)
}

:root .has-nightshade-gradient-background {
  background: linear-gradient(135deg, #330968, #31cdcf)
}

:root .has-midnight-gradient-background {
  background: linear-gradient(135deg, #020381, #2874fc)
}

.has-regular-font-size {
  font-size: 1em
}

.has-larger-font-size {
  font-size: 2.625em
}

.has-normal-font-size {
  font-size: var(--wp--preset--font-size--normal)
}

.has-huge-font-size {
  font-size: var(--wp--preset--font-size--huge)
}

.has-text-align-center {
  text-align: center
}

.has-text-align-left {
  text-align: left
}

.has-text-align-right {
  text-align: right
}

#end-resizable-editor-section {
  display: none
}

.aligncenter {
  clear: both
}

.items-justified-left {
  justify-content: flex-start
}

.items-justified-center {
  justify-content: center
}

.items-justified-right {
  justify-content: flex-end
}

.items-justified-space-between {
  justify-content: space-between
}

.screen-reader-text {
  border: 0;
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  word-wrap: normal !important
}

.screen-reader-text:focus {
  background-color: #ddd;
  clip-path: none;
  color: #444;
  display: block;
  font-size: 1em;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000
}

html :where(.has-border-color) {
  border-style: solid
}

html :where([style*=border-top-color]) {
  border-top-style: solid
}

html :where([style*=border-right-color]) {
  border-right-style: solid
}

html :where([style*=border-bottom-color]) {
  border-bottom-style: solid
}

html :where([style*=border-left-color]) {
  border-left-style: solid
}

html :where([style*=border-width]) {
  border-style: solid
}

html :where([style*=border-top-width]) {
  border-top-style: solid
}

html :where([style*=border-right-width]) {
  border-right-style: solid
}

html :where([style*=border-bottom-width]) {
  border-bottom-style: solid
}

html :where([style*=border-left-width]) {
  border-left-style: solid
}

html :where(img[class*=wp-image-]) {
  height: auto;
  max-width: 100%
}

:where(figure) {
  margin: 0 0 1em
}

html :where(.is-position-sticky) {
  --wp-admin--admin-bar--position-offset: var(--wp-admin--admin-bar--height, 0px)
}

@media screen and (max-width:600px) {
  html :where(.is-position-sticky) {
    --wp-admin--admin-bar--position-offset: 0px
  }
}

/* font-awesome-svg-styles-default-inline-css */
.svg-inline--fa {
  display: inline-block;
  height: 1em;
  overflow: visible;
  vertical-align: -.125em;
}

/* font-awesome-svg-styles-inline-css */
.wp-block-font-awesome-icon svg::before,
.wp-rich-text-font-awesome-icon svg::before {
  content: unset;
}

/* flatsome-main-inline-css */
@font-face {
  font-family: "fl-icons";
  font-display: block;
  src: url(https://vandamtour.vn/wp-content/themes/flatsome/assets/css/icons/fl-icons.eot?v=3.19.6);
  src:
    url(https://vandamtour.vn/wp-content/themes/flatsome/assets/css/icons/fl-icons.eot#iefix?v=3.19.6) format("embedded-opentype"),
    url(https://vandamtour.vn/wp-content/themes/flatsome/assets/css/icons/fl-icons.woff2?v=3.19.6) format("woff2"),
    url(https://vandamtour.vn/wp-content/themes/flatsome/assets/css/icons/fl-icons.ttf?v=3.19.6) format("truetype"),
    url(https://vandamtour.vn/wp-content/themes/flatsome/assets/css/icons/fl-icons.woff?v=3.19.6) format("woff"),
    url(https://vandamtour.vn/wp-content/themes/flatsome/assets/css/icons/fl-icons.svg?v=3.19.6#fl-icons) format("svg");
}

/* font-awesome-official-v4shim-inline-css */
@font-face {
  font-family: "FontAwesome";
  font-display: block;
  src: url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-brands-400.eot"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-brands-400.eot?#iefix") format("embedded-opentype"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-brands-400.woff2") format("woff2"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-brands-400.woff") format("woff"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-brands-400.ttf") format("truetype"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-brands-400.svg#fontawesome") format("svg");
}

@font-face {
  font-family: "FontAwesome";
  font-display: block;
  src: url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-solid-900.eot"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-solid-900.eot?#iefix") format("embedded-opentype"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-solid-900.woff2") format("woff2"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-solid-900.woff") format("woff"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-solid-900.ttf") format("truetype"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-solid-900.svg#fontawesome") format("svg");
}

@font-face {
  font-family: "FontAwesome";
  font-display: block;
  src: url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-regular-400.eot"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-regular-400.eot?#iefix") format("embedded-opentype"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-regular-400.woff2") format("woff2"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-regular-400.woff") format("woff"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-regular-400.ttf") format("truetype"),
    url("https://use.fontawesome.com/releases/v5.15.4/webfonts/fa-regular-400.svg#fontawesome") format("svg");
  unicode-range: U+F004-F005, U+F007, U+F017, U+F022, U+F024, U+F02E, U+F03E, U+F044, U+F057-F059, U+F06E, U+F070, U+F075, U+F07B-F07C, U+F080, U+F086, U+F089, U+F094, U+F09D, U+F0A0, U+F0A4-F0A7, U+F0C5, U+F0C7-F0C8, U+F0E0, U+F0EB, U+F0F3, U+F0F8, U+F0FE, U+F111, U+F118-F11A, U+F11C, U+F133, U+F144, U+F146, U+F14A, U+F14D-F14E, U+F150-F152, U+F15B-F15C, U+F164-F165, U+F185-F186, U+F191-F192, U+F1AD, U+F1C1-F1C9, U+F1CD, U+F1D8, U+F1E3, U+F1EA, U+F1F6, U+F1F9, U+F20A, U+F247-F249, U+F24D, U+F254-F25B, U+F25D, U+F267, U+F271-F274, U+F279, U+F28B, U+F28D, U+F2B5-F2B6, U+F2B9, U+F2BB, U+F2BD, U+F2C1-F2C2, U+F2D0, U+F2D2, U+F2DC, U+F2ED, U+F328, U+F358-F35B, U+F3A5, U+F3D1, U+F410, U+F4AD;
}

/* custom-css */
:root {
  --primary-color: #446084;
  --fs-color-primary: #446084;
  --fs-color-secondary: #d26e4b;
  --fs-color-success: #7a9c59;
  --fs-color-alert: #b20000;
  --fs-experimental-link-color: #334862;
  --fs-experimental-link-color-hover: #111;
}

.tooltipster-base {
  --tooltip-color: #fff;
  --tooltip-bg-color: #000;
}

.off-canvas-right .mfp-content,
.off-canvas-left .mfp-content {
  --drawer-width: 300px;
}

.header-main {
  height: 76px
}

#logo img {
  max-height: 76px
}

#logo {
  width: 281px;
}

.header-top {
  min-height: 30px
}

.transparent .header-main {
  height: 90px
}

.transparent #logo img {
  max-height: 90px
}

.has-transparent+.page-title:first-of-type,
.has-transparent+#main>.page-title,
.has-transparent+#main>div>.page-title,
.has-transparent+#main .page-header-wrapper:first-of-type .page-title {
  padding-top: 120px;
}

.header.show-on-scroll,
.stuck .header-main {
  height: 70px !important
}

.stuck #logo img {
  max-height: 70px !important
}

.header-bottom {
  background-color: #f1f1f1
}

@media (max-width: 549px) {
  .header-main {
    height: 70px
  }

  #logo img {
    max-height: 70px
  }
}

body {
  color: #222222
}

h1,
h2,
h3,
h4,
h5,
h6,
.heading-font {
  color: #222222;
}

body {
  font-family: K2D, sans-serif;
}

body {
  font-weight: 400;
  font-style: normal;
}

.nav>li>a {
  font-family: K2D, sans-serif;
}

.mobile-sidebar-levels-2 .nav>li>ul>li>a {
  font-family: K2D, sans-serif;
}

.nav>li>a,
.mobile-sidebar-levels-2 .nav>li>ul>li>a {
  font-weight: 700;
  font-style: normal;
}

h1,
h2,
h3,
h4,
h5,
h6,
.heading-font,
.off-canvas-center .nav-sidebar.nav-vertical>li>a {
  font-family: K2D, sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6,
.heading-font,
.banner h1,
.banner h2 {
  font-weight: 700;
  font-style: normal;
}

.alt-font {
  font-family: K2D, sans-serif;
}

.alt-font {
  font-weight: 400 !important;
  font-style: normal !important;
}

.absolute-footer,
html {
  background-color: #41bd47
}

.nav-vertical-fly-out>li+li {
  border-top-width: 1px;
  border-top-style: solid;
}

.label-new.menu-item>a:after {
  content: "Mới";
}

.label-hot.menu-item>a:after {
  content: "Nổi bật";
}

.label-sale.menu-item>a:after {
  content: "Giảm giá";
}

.label-popular.menu-item>a:after {
  content: "Phổ biến";
}

/* kirki-inline-styles */
/* thai */
@font-face {
  font-family: 'K2D';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://vandamtour.vn/wp-content/fonts/k2d/J7aTnpF2V0EjZKUsrLc.woff2) format('woff2');
  unicode-range: U+02D7, U+0303, U+0331, U+0E01-0E5B, U+200C-200D, U+25CC;
}

/* vietnamese */
@font-face {
  font-family: 'K2D';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://vandamtour.vn/wp-content/fonts/k2d/J7aTnpF2V0Ejf6UsrLc.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
  font-family: 'K2D';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://vandamtour.vn/wp-content/fonts/k2d/J7aTnpF2V0EjfqUsrLc.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
  font-family: 'K2D';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://vandamtour.vn/wp-content/fonts/k2d/J7aTnpF2V0EjcKUs.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* thai */
@font-face {
  font-family: 'K2D';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://vandamtour.vn/wp-content/fonts/k2d/J7aenpF2V0Ery4A5h5Y91po.woff2) format('woff2');
  unicode-range: U+02D7, U+0303, U+0331, U+0E01-0E5B, U+200C-200D, U+25CC;
}

/* vietnamese */
@font-face {
  font-family: 'K2D';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://vandamtour.vn/wp-content/fonts/k2d/J7aenpF2V0Ery4A5nJY91po.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
  font-family: 'K2D';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://vandamtour.vn/wp-content/fonts/k2d/J7aenpF2V0Ery4A5nZY91po.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
  font-family: 'K2D';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://vandamtour.vn/wp-content/fonts/k2d/J7aenpF2V0Ery4A5k5Y9.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* inline-css-9 */
#gap-910645468 {
  padding-top: 30px;
}

/* inline-css-10 */
#image_1216821535 {
  width: 85%;
}

/* inline-css-11 */
#section_2016408771 {
  padding-top: 30px;
  padding-bottom: 30px;
}

#section_2016408771 .section-bg-overlay {
  background-color: rgba(0, 0, 0, 0.552);
}

/* inline-css-12 */
#image_860336018 {
  width: 70%;
}

/* inline-css-13 */
#section_887156785 {
  padding-top: 30px;
  padding-bottom: 30px;
}

/* inline-css-14 */
#section_2121731442 {
  padding-top: 0px;
  padding-bottom: 0px;
}

/* inline-css-15 */
#gap-1800361928 {
  padding-top: 30px;
}

/* inline-css-16 */
#image_522138252 {
  width: 100%;
}

/* inline-css-17 */
#section_1707409217 {
  padding-top: 30px;
  padding-bottom: 30px;
}

#section_1707409217 .section-bg-overlay {
  background-color: rgba(0, 0, 0, 0.552);
}

/* inline-css-18 */
#section_1356242675 {
  padding-top: 30px;
  padding-bottom: 30px;
}

/* inline-css-19 */
#image_135176336 {
  width: 100%;
}

/* inline-css-20 */
#image_1462230209 {
  width: 100%;
}

/* inline-css-21 */
#image_1892975082 {
  width: 100%;
}

/* inline-css-22 */
#image_1896870207 {
  width: 100%;
}

/* inline-css-23 */
#banner-grid-1239210530 .grid-col-1 {
  height: 600px
}

#banner-grid-1239210530 .grid-col-1-2 {
  height: 300px
}

#banner-grid-1239210530 .grid-col-1-3 {
  height: 200px
}

#banner-grid-1239210530 .grid-col-2-3 {
  height: 400px
}

#banner-grid-1239210530 .grid-col-1-4 {
  height: 150px
}

#banner-grid-1239210530 .grid-col-3-4 {
  height: 450px
}

/* inline-css-24 */
#section_99790715 {
  padding-top: 0px;
  padding-bottom: 0px;
}

/* inline-css-25 */
#image_1343060099 {
  width: 100%;
}

/* inline-css-26 */
#image_808092707 {
  width: 100%;
}

/* inline-css-27 */
#image_182883313 {
  width: 100%;
}

/* inline-css-28 */
#image_653330975 {
  width: 100%;
}

/* inline-css-29 */
#section_1990676908 {
  padding-top: 30px;
  padding-bottom: 30px;
}

/* inline-css-30 */
#section_1333046176 {
  padding-top: 0px;
  padding-bottom: 0px;
}

/* inline-css-31 */
.phone-bar a,
#phone-vr .phone-vr-circle-fill,
#phone-vr .phone-vr-img-circle,
#phone-vr .phone-bar a {
  background-color: #dd3333;
}

#phone-vr .phone-vr-circle-fill {
  opacity: 0.7;
  box-shadow: 0 0 0 0 #dd3333;
}

.phone-bar2 a,
#phone-vr2 .phone-vr-circle-fill,
#phone-vr2 .phone-vr-img-circle,
#phone-vr2 .phone-bar a {
  background-color: ;
}

#phone-vr2 .phone-vr-circle-fill {
  opacity: 0.7;
  box-shadow: 0 0 0 0;
}

.phone-bar3 a,
#phone-vr3 .phone-vr-circle-fill,
#phone-vr3 .phone-vr-img-circle,
#phone-vr3 .phone-bar a {
  background-color: ;
}

#phone-vr3 .phone-vr-circle-fill {
  opacity: 0.7;
  box-shadow: 0 0 0 0;
}

/* inline-css-32 */
.order-1.active-1 {
  order: 2;
}

.order-2.active-2 {
  order: 1;
}

/* inline-css-33 */
.wpcf7-form input::not([type="radio"]),
.wpcf7-form select,
.wpcf7-form textarea {
  height: 42px;
  box-shadow: 0px 0px 0px;
  font-size: 15px;
  border: none;
}

.wpcf7-form input,
.wpcf7-form textarea,
.wpcf7-form select {
  border: 2px solid var(--primary-color);
  font-weight: normal;
}

.wpcf7-form input,
.wpcf7-form select,
.wpcf7-form textarea {
  border-radius: 8px;
  resize: none;
}

.wpcf7-form input[type="submit"] {
  margin: 0px;
  padding: 0px 20px;
}

.wpcf7-form .wpcf7-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

span.post_comments,
.entry-divider,
.is-divider {
  display: none;
}

.margin-bottom-0px p {
  margin-bottom: 0px;
}

.txtFooter p {
  margin-bottom: 5px;
}

.blog-wrapper .entry-header .entry-header-text {
  padding-bottom: 0px;
}

.blog-wrapper .entry-content {
  margin-top: 0px;
  padding-top: 0px;
}


/** header-nav */
:root {
  --menu-cot-mega: 6;
  --menu-sub-level-2: 14px;
  --menu-sub-level-3: 13px;
}

.header-nav li.menu-item-has-children.has-dropdown ul.nav-dropdown-full {
  padding: 10px 0px 0px 0px;
}

.header-nav li.menu-item-has-children.has-dropdown ul.nav-dropdown-full>li {
  width: calc(100%/ var(--menu-cot-mega));
}

.header-nav li.menu-item-has-children.has-dropdown ul.nav-dropdown-full li.menu-item-has-children a {
  padding: 5px 10px;
  font-size: var(--menu-sub-level-2);
}

.header-nav li.menu-item-has-children.has-dropdown ul.nav-dropdown-full li.menu-item-has-children ul.sub-menu li a {
  font-size: var(--menu-sub-level-3);
}

.nav-dropdown-simple .nav-column li>a:hover,
.nav-dropdown.nav-dropdown-simple>li>a:hover {
  background-color: var(--primary-color);
  color: white;
}

/*** css wpforms */
.FrmThietkewebchuyen.wpforms-container .wpforms-field {
  padding: 5px 0px;
}

.FrmThietkewebchuyen.wpforms-container .wpforms-field input {
  border-radius: 8px
}

.FrmThietkewebchuyen.wpforms-container .wpforms-form .wpforms-field-container .wpforms-field.wpforms-field-content {
  padding-top: 5px;
}

.FrmThietkewebchuyen.wpforms-container .wpforms-form .wpforms-submit-container {
  padding-top: 5px;
  margin: 0px;
  display: block;
  text-align: center;
  position: relative;
}

.FrmThietkewebchuyen.wpforms-container .wpforms-form .wpforms-submit-container button[type=submit] {
  border-radius: 24px;
  padding: 10px 30px;
}


/** css table */
.Thietkewebchuyen_com_homestay .tbl_ThongSoTungPhong {
  border: 1px solid #ccc;
  border-radius: var(--bo-goc);
}

.Thietkewebchuyen_com_homestay .tbl_ThongSoTungPhong tr td {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
  padding: 10px;
}

.Thietkewebchuyen_com_homestay .tbl_ThongSoTungPhong tr:last-child td {
  border-bottom: none;
}

.Thietkewebchuyen_com_homestay .tbl_ThongSoTungPhong tr.lbl td {
  font-weight: bold;
  background: #B9BBC6;
  color: #1E1F24;
}

.Thietkewebchuyen_com_homestay .tbl_ThongSoTungPhong tr.lbl td:first-child {
  border-radius: var(--bo-goc) 0px 0px 0px;
}

.Thietkewebchuyen_com_homestay .tbl_ThongSoTungPhong tr.lbl td:last-child {
  border-radius: 0px var(--bo-goc) 0px 0px;
}

.Thietkewebchuyen_com_homestay .tbl_ThongSoTungPhong tr td:last-child {
  border-right: none;
}

.grecaptcha-badge {
  display: none !important;
}

:root {
  --mau-vien-bang: #393939;
}

body:not(.woocommerce-cart):not(.woocommerce-checkout) td,
body:not(.woocommerce-cart):not(.woocommerce-checkout) th {
  font-size: inherit;
  padding: 5px 10px;
  border-top: 1px solid var(--mau-vien-bang);
  border-left: 1px solid var(--mau-vien-bang);
}

body:not(.woocommerce-cart):not(.woocommerce-checkout) td:first-child,
body:not(.woocommerce-cart):not(.woocommerce-checkout) th:first-child {
  padding-left: 10px;
}

body:not(.woocommerce-cart):not(.woocommerce-checkout) td:last-child,
body:not(.woocommerce-cart):not(.woocommerce-checkout) th:last-child {
  border-right: 1px solid var(--mau-vien-bang);
}

body:not(.woocommerce-cart):not(.woocommerce-checkout) tr:last-child td,
body:not(.woocommerce-cart):not(.woocommerce-checkout) tr:last-child td {
  border-bottom: 1px solid var(--mau-vien-bang);
}

/* inline-css-34 */
:root {
  --mau-2: ;
}

a.devvn_buy_now_style {
  display: block;
  max-width: 100%;
  margin: 10px 0px;
}

.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children,
.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children a,
.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children ul.sub-menu,
.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children>ul.sub-menu li a {
  display: block;
}

.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children {
  margin-bottom: 25px;
  border: 1px solid #ccc;
  border-radius: 0px 0px 5px 5px;
  font-size: 14px;
}

.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children button.toggle {
  display: none;
}

.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children>a {
  text-align: center;
  text-transform: uppercase;
  background: var(--primary-color);
  color: white;
  font-weight: bold;
  font-size: 13px;
}

.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children>ul.sub-menu {
  border-left: none;
  margin: 0px;
  padding-left: 0px;
}

.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children>ul.sub-menu li {
  position: relative;
}

.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children>ul.sub-menu li::before {
  content: "\f111";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 5px;
  font-weight: 400;
  font-family: "Font Awesome 6 Free";
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: var(--fa-display, inline-block);
  font-style: normal;
  font-variant: normal;
  line-height: 1;
  text-rendering: auto;
  font-size: 10px;
}

.widget_nav_menu .menu-menu-phai-container .menu li.menu-item-has-children>ul.sub-menu li a {
  padding: 5px 15px 5px 25px;
  border-bottom: 1px solid #ccc;
}

.archive.category .from_the_blog_excerpt {
  text-align: justify;
}

/* global-styles-inline-css */
:root {
  --wp--preset--aspect-ratio--square: 1;
  --wp--preset--aspect-ratio--4-3: 4/3;
  --wp--preset--aspect-ratio--3-4: 3/4;
  --wp--preset--aspect-ratio--3-2: 3/2;
  --wp--preset--aspect-ratio--2-3: 2/3;
  --wp--preset--aspect-ratio--16-9: 16/9;
  --wp--preset--aspect-ratio--9-16: 9/16;
  --wp--preset--color--black: #000000;
  --wp--preset--color--cyan-bluish-gray: #abb8c3;
  --wp--preset--color--white: #ffffff;
  --wp--preset--color--pale-pink: #f78da7;
  --wp--preset--color--vivid-red: #cf2e2e;
  --wp--preset--color--luminous-vivid-orange: #ff6900;
  --wp--preset--color--luminous-vivid-amber: #fcb900;
  --wp--preset--color--light-green-cyan: #7bdcb5;
  --wp--preset--color--vivid-green-cyan: #00d084;
  --wp--preset--color--pale-cyan-blue: #8ed1fc;
  --wp--preset--color--vivid-cyan-blue: #0693e3;
  --wp--preset--color--vivid-purple: #9b51e0;
  --wp--preset--color--primary: #446084;
  --wp--preset--color--secondary: #d26e4b;
  --wp--preset--color--success: #7a9c59;
  --wp--preset--color--alert: #b20000;
  --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
  --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
  --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
  --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
  --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
  --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
  --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
  --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
  --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
  --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
  --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
  --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
  --wp--preset--font-size--small: 13px;
  --wp--preset--font-size--medium: 20px;
  --wp--preset--font-size--large: 36px;
  --wp--preset--font-size--x-large: 42px;
  --wp--preset--spacing--20: 0.44rem;
  --wp--preset--spacing--30: 0.67rem;
  --wp--preset--spacing--40: 1rem;
  --wp--preset--spacing--50: 1.5rem;
  --wp--preset--spacing--60: 2.25rem;
  --wp--preset--spacing--70: 3.38rem;
  --wp--preset--spacing--80: 5.06rem;
  --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
  --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
  --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
  --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
  --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
}

:where(body) {
  margin: 0;
}

.wp-site-blocks>.alignleft {
  float: left;
  margin-right: 2em;
}

.wp-site-blocks>.alignright {
  float: right;
  margin-left: 2em;
}

.wp-site-blocks>.aligncenter {
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
}

:where(.is-layout-flex) {
  gap: 0.5em;
}

:where(.is-layout-grid) {
  gap: 0.5em;
}

.is-layout-flow>.alignleft {
  float: left;
  margin-inline-start: 0;
  margin-inline-end: 2em;
}

.is-layout-flow>.alignright {
  float: right;
  margin-inline-start: 2em;
  margin-inline-end: 0;
}

.is-layout-flow>.aligncenter {
  margin-left: auto !important;
  margin-right: auto !important;
}

.is-layout-constrained>.alignleft {
  float: left;
  margin-inline-start: 0;
  margin-inline-end: 2em;
}

.is-layout-constrained>.alignright {
  float: right;
  margin-inline-start: 2em;
  margin-inline-end: 0;
}

.is-layout-constrained>.aligncenter {
  margin-left: auto !important;
  margin-right: auto !important;
}

.is-layout-constrained> :where(:not(.alignleft):not(.alignright):not(.alignfull)) {
  margin-left: auto !important;
  margin-right: auto !important;
}

body .is-layout-flex {
  display: flex;
}

.is-layout-flex {
  flex-wrap: wrap;
  align-items: center;
}

.is-layout-flex> :is(*, div) {
  margin: 0;
}

body .is-layout-grid {
  display: grid;
}

.is-layout-grid> :is(*, div) {
  margin: 0;
}

body {
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

a:where(:not(.wp-element-button)) {
  text-decoration: none;
}

:root :where(.wp-element-button, .wp-block-button__link) {
  background-color: #32373c;
  border-width: 0;
  color: #fff;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  padding: calc(0.667em + 2px) calc(1.333em + 2px);
  text-decoration: none;
}

.has-black-color {
  color: var(--wp--preset--color--black) !important;
}

.has-cyan-bluish-gray-color {
  color: var(--wp--preset--color--cyan-bluish-gray) !important;
}

.has-white-color {
  color: var(--wp--preset--color--white) !important;
}

.has-pale-pink-color {
  color: var(--wp--preset--color--pale-pink) !important;
}

.has-vivid-red-color {
  color: var(--wp--preset--color--vivid-red) !important;
}

.has-luminous-vivid-orange-color {
  color: var(--wp--preset--color--luminous-vivid-orange) !important;
}

.has-luminous-vivid-amber-color {
  color: var(--wp--preset--color--luminous-vivid-amber) !important;
}

.has-light-green-cyan-color {
  color: var(--wp--preset--color--light-green-cyan) !important;
}

.has-vivid-green-cyan-color {
  color: var(--wp--preset--color--vivid-green-cyan) !important;
}

.has-pale-cyan-blue-color {
  color: var(--wp--preset--color--pale-cyan-blue) !important;
}

.has-vivid-cyan-blue-color {
  color: var(--wp--preset--color--vivid-cyan-blue) !important;
}

.has-vivid-purple-color {
  color: var(--wp--preset--color--vivid-purple) !important;
}

.has-primary-color {
  color: var(--wp--preset--color--primary) !important;
}

.has-secondary-color {
  color: var(--wp--preset--color--secondary) !important;
}

.has-success-color {
  color: var(--wp--preset--color--success) !important;
}

.has-alert-color {
  color: var(--wp--preset--color--alert) !important;
}

.has-black-background-color {
  background-color: var(--wp--preset--color--black) !important;
}

.has-cyan-bluish-gray-background-color {
  background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
}

.has-white-background-color {
  background-color: var(--wp--preset--color--white) !important;
}

.has-pale-pink-background-color {
  background-color: var(--wp--preset--color--pale-pink) !important;
}

.has-vivid-red-background-color {
  background-color: var(--wp--preset--color--vivid-red) !important;
}

.has-luminous-vivid-orange-background-color {
  background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
}

.has-luminous-vivid-amber-background-color {
  background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
}

.has-light-green-cyan-background-color {
  background-color: var(--wp--preset--color--light-green-cyan) !important;
}

.has-vivid-green-cyan-background-color {
  background-color: var(--wp--preset--color--vivid-green-cyan) !important;
}

.has-pale-cyan-blue-background-color {
  background-color: var(--wp--preset--color--pale-cyan-blue) !important;
}

.has-vivid-cyan-blue-background-color {
  background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
}

.has-vivid-purple-background-color {
  background-color: var(--wp--preset--color--vivid-purple) !important;
}

.has-primary-background-color {
  background-color: var(--wp--preset--color--primary) !important;
}

.has-secondary-background-color {
  background-color: var(--wp--preset--color--secondary) !important;
}

.has-success-background-color {
  background-color: var(--wp--preset--color--success) !important;
}

.has-alert-background-color {
  background-color: var(--wp--preset--color--alert) !important;
}

.has-black-border-color {
  border-color: var(--wp--preset--color--black) !important;
}

.has-cyan-bluish-gray-border-color {
  border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
}

.has-white-border-color {
  border-color: var(--wp--preset--color--white) !important;
}

.has-pale-pink-border-color {
  border-color: var(--wp--preset--color--pale-pink) !important;
}

.has-vivid-red-border-color {
  border-color: var(--wp--preset--color--vivid-red) !important;
}

.has-luminous-vivid-orange-border-color {
  border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
}

.has-luminous-vivid-amber-border-color {
  border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
}

.has-light-green-cyan-border-color {
  border-color: var(--wp--preset--color--light-green-cyan) !important;
}

.has-vivid-green-cyan-border-color {
  border-color: var(--wp--preset--color--vivid-green-cyan) !important;
}

.has-pale-cyan-blue-border-color {
  border-color: var(--wp--preset--color--pale-cyan-blue) !important;
}

.has-vivid-cyan-blue-border-color {
  border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
}

.has-vivid-purple-border-color {
  border-color: var(--wp--preset--color--vivid-purple) !important;
}

.has-primary-border-color {
  border-color: var(--wp--preset--color--primary) !important;
}

.has-secondary-border-color {
  border-color: var(--wp--preset--color--secondary) !important;
}

.has-success-border-color {
  border-color: var(--wp--preset--color--success) !important;
}

.has-alert-border-color {
  border-color: var(--wp--preset--color--alert) !important;
}

.has-vivid-cyan-blue-to-vivid-purple-gradient-background {
  background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
}

.has-light-green-cyan-to-vivid-green-cyan-gradient-background {
  background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
}

.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
  background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
}

.has-luminous-vivid-orange-to-vivid-red-gradient-background {
  background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
}

.has-very-light-gray-to-cyan-bluish-gray-gradient-background {
  background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
}

.has-cool-to-warm-spectrum-gradient-background {
  background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
}

.has-blush-light-purple-gradient-background {
  background: var(--wp--preset--gradient--blush-light-purple) !important;
}

.has-blush-bordeaux-gradient-background {
  background: var(--wp--preset--gradient--blush-bordeaux) !important;
}

.has-luminous-dusk-gradient-background {
  background: var(--wp--preset--gradient--luminous-dusk) !important;
}

.has-pale-ocean-gradient-background {
  background: var(--wp--preset--gradient--pale-ocean) !important;
}

.has-electric-grass-gradient-background {
  background: var(--wp--preset--gradient--electric-grass) !important;
}

.has-midnight-gradient-background {
  background: var(--wp--preset--gradient--midnight) !important;
}

.has-small-font-size {
  font-size: var(--wp--preset--font-size--small) !important;
}

.has-medium-font-size {
  font-size: var(--wp--preset--font-size--medium) !important;
}

.has-large-font-size {
  font-size: var(--wp--preset--font-size--large) !important;
}

.has-x-large-font-size {
  font-size: var(--wp--preset--font-size--x-large) !important;
}