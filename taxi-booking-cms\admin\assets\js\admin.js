/**
 * Admin JavaScript - Taxi Booking CMS Admin Panel
 */

// Global variables
let currentSection = 'dashboard';
let editingServiceId = null;

/**
 * Document Ready
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

/**
 * Initialize Admin Panel
 */
function initializeAdmin() {
    loadDashboard();
    initializeEventListeners();
    showSection('dashboard');
}

/**
 * Initialize Event Listeners
 */
function initializeEventListeners() {
    // Service form submission
    const serviceForm = document.getElementById('service-form');
    if (serviceForm) {
        serviceForm.addEventListener('submit', handleServiceSubmit);
    }

    // Settings forms
    const generalForm = document.getElementById('general-settings-form');
    const contactForm = document.getElementById('contact-settings-form');
    const heroForm = document.getElementById('hero-settings-form');
    
    if (generalForm) loadGeneralSettings();
    if (contactForm) loadContactSettings();
    if (heroForm) loadHeroSettings();
}

/**
 * Show Section
 */
function showSection(sectionName) {
    // Update navigation
    document.querySelectorAll('.admin-nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    document.querySelector(`[onclick="showSection('${sectionName}')"]`).parentElement.classList.add('active');
    
    // Update sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });
    
    document.getElementById(`${sectionName}-section`).classList.add('active');
    
    currentSection = sectionName;
    
    // Load section data
    switch (sectionName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'services':
            loadServices();
            break;
        case 'bookings':
            loadBookings();
            break;
        case 'messages':
            loadMessages();
            break;
        case 'settings':
            loadSettings();
            break;
    }
}

/**
 * Load Dashboard
 */
function loadDashboard() {
    const stats = window.dataManager.getStats();
    
    // Render stats
    const statsContainer = document.getElementById('stats-container');
    statsContainer.innerHTML = `
        <div class="stat-card">
            <i class="stat-icon fas fa-calendar-check"></i>
            <span class="stat-number">${stats.totalBookings}</span>
            <span class="stat-label">Tổng Đặt Xe</span>
        </div>
        <div class="stat-card">
            <i class="stat-icon fas fa-clock"></i>
            <span class="stat-number">${stats.pendingBookings}</span>
            <span class="stat-label">Chờ Xử Lý</span>
        </div>
        <div class="stat-card">
            <i class="stat-icon fas fa-check-circle"></i>
            <span class="stat-number">${stats.completedBookings}</span>
            <span class="stat-label">Hoàn Thành</span>
        </div>
        <div class="stat-card">
            <i class="stat-icon fas fa-envelope"></i>
            <span class="stat-number">${stats.unreadMessages}</span>
            <span class="stat-label">Tin Nhắn Mới</span>
        </div>
    `;
    
    // Load recent activities
    loadRecentBookings();
    loadRecentMessages();
}

/**
 * Load Recent Bookings
 */
function loadRecentBookings() {
    const bookings = window.dataManager.getBookings().slice(0, 5);
    const container = document.getElementById('recent-bookings');
    
    if (bookings.length === 0) {
        container.innerHTML = '<div class="empty-state"><p>Chưa có đặt xe nào</p></div>';
        return;
    }
    
    container.innerHTML = bookings.map(booking => `
        <div class="recent-item">
            <div class="recent-icon booking">
                <i class="fas fa-car"></i>
            </div>
            <div class="recent-content">
                <div class="recent-title">${booking.customerName || 'Khách hàng'}</div>
                <div class="recent-meta">${booking.pickupLocation} → ${booking.destination}</div>
            </div>
            <div class="recent-time">${formatTimeAgo(booking.createdAt)}</div>
        </div>
    `).join('');
}

/**
 * Load Recent Messages
 */
function loadRecentMessages() {
    const messages = window.dataManager.getMessages().slice(0, 5);
    const container = document.getElementById('recent-messages');
    
    if (messages.length === 0) {
        container.innerHTML = '<div class="empty-state"><p>Chưa có tin nhắn nào</p></div>';
        return;
    }
    
    container.innerHTML = messages.map(message => `
        <div class="recent-item">
            <div class="recent-icon message">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="recent-content">
                <div class="recent-title">${message.name}</div>
                <div class="recent-meta">${message.subject}</div>
            </div>
            <div class="recent-time">${formatTimeAgo(message.createdAt)}</div>
        </div>
    `).join('');
}

/**
 * Load Services
 */
function loadServices() {
    const services = window.dataManager.getServices();
    const tbody = document.getElementById('services-table-body');
    
    if (services.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="empty-state"><p>Chưa có dịch vụ nào</p></td></tr>';
        return;
    }
    
    tbody.innerHTML = services.map(service => `
        <tr>
            <td>
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <i class="${service.icon}" style="font-size: 1.5rem; color: var(--primary-color);"></i>
                    <div>
                        <div style="font-weight: 600;">${service.name}</div>
                        <div style="font-size: 0.875rem; color: var(--text-secondary);">${service.description}</div>
                    </div>
                </div>
            </td>
            <td><strong>${service.price}</strong>${service.priceUnit}</td>
            <td><span class="status-badge ${service.active ? 'active' : 'inactive'}">${service.active ? 'Hoạt động' : 'Tạm dừng'}</span></td>
            <td><span class="status-badge ${service.featured ? 'active' : 'inactive'}">${service.featured ? 'Nổi bật' : 'Thường'}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit" onclick="editService(${service.id})" title="Chỉnh sửa">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteService(${service.id})" title="Xóa">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * Load Bookings
 */
function loadBookings() {
    const bookings = window.dataManager.getBookings();
    const tbody = document.getElementById('bookings-table-body');
    
    if (bookings.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="empty-state"><p>Chưa có đặt xe nào</p></td></tr>';
        return;
    }
    
    tbody.innerHTML = bookings.map(booking => {
        const service = window.dataManager.getServices().find(s => s.id == booking.serviceType);
        const serviceName = service ? service.name : 'Không xác định';
        
        return `
            <tr>
                <td>${booking.customerName || 'N/A'}</td>
                <td><a href="tel:${booking.customerPhone}">${booking.customerPhone}</a></td>
                <td>${booking.pickupLocation}</td>
                <td>${booking.destination}</td>
                <td>${serviceName}</td>
                <td>${booking.pickupTime ? formatDateTime(booking.pickupTime) : formatDateTime(booking.createdAt)}</td>
                <td><span class="status-badge ${booking.status || 'pending'}">${getStatusText(booking.status || 'pending')}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view" onclick="viewBooking(${booking.id})" title="Xem chi tiết">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit" onclick="updateBookingStatus(${booking.id})" title="Cập nhật trạng thái">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteBooking(${booking.id})" title="Xóa">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * Load Messages
 */
function loadMessages() {
    const messages = window.dataManager.getMessages();
    const tbody = document.getElementById('messages-table-body');
    
    if (messages.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="empty-state"><p>Chưa có tin nhắn nào</p></td></tr>';
        return;
    }
    
    tbody.innerHTML = messages.map(message => `
        <tr>
            <td>${message.name}</td>
            <td><a href="mailto:${message.email}">${message.email}</a></td>
            <td>${message.subject}</td>
            <td>${formatDateTime(message.createdAt)}</td>
            <td><span class="status-badge ${message.status || 'unread'}">${getMessageStatusText(message.status || 'unread')}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewMessage(${message.id})" title="Xem tin nhắn">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="markAsRead(${message.id})" title="Đánh dấu đã đọc">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteMessage(${message.id})" title="Xóa">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * Load Settings
 */
function loadSettings() {
    loadGeneralSettings();
    loadContactSettings();
    loadHeroSettings();
    loadSocialLinks();
}

/**
 * Load General Settings
 */
function loadGeneralSettings() {
    const settings = window.dataManager.getSettings();
    
    document.getElementById('site-name').value = settings.siteName || '';
    document.getElementById('site-title-input').value = settings.siteTitle || '';
    document.getElementById('site-description').value = settings.siteDescription || '';
    document.getElementById('company-name').value = settings.companyName || '';
}

/**
 * Load Contact Settings
 */
function loadContactSettings() {
    const settings = window.dataManager.getSettings();
    
    document.getElementById('phone').value = settings.phone || '';
    document.getElementById('email').value = settings.email || '';
    document.getElementById('address').value = settings.address || '';
}

/**
 * Load Hero Settings
 */
function loadHeroSettings() {
    const settings = window.dataManager.getSettings();
    
    document.getElementById('hero-title-input').value = settings.heroTitle || '';
    document.getElementById('hero-subtitle-input').value = settings.heroSubtitle || '';
}

/**
 * Load Social Links
 */
function loadSocialLinks() {
    const settings = window.dataManager.getSettings();
    const container = document.getElementById('social-links-container');
    
    container.innerHTML = settings.socialLinks.map((link, index) => `
        <div class="social-link-item">
            <input type="text" placeholder="Tên mạng xã hội" value="${link.name}" onchange="updateSocialLink(${index}, 'name', this.value)">
            <input type="url" placeholder="URL" value="${link.url}" onchange="updateSocialLink(${index}, 'url', this.value)">
            <input type="text" placeholder="Icon class" value="${link.icon}" onchange="updateSocialLink(${index}, 'icon', this.value)">
            <button type="button" class="social-link-remove" onclick="removeSocialLink(${index})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `).join('');
}

/**
 * Show Service Modal
 */
function showServiceModal(serviceId = null) {
    editingServiceId = serviceId;
    const modal = document.getElementById('service-modal');
    const form = document.getElementById('service-form');
    const title = document.getElementById('service-modal-title');
    
    if (serviceId) {
        // Edit mode
        const service = window.dataManager.getServices().find(s => s.id === serviceId);
        if (service) {
            title.textContent = 'Chỉnh Sửa Dịch Vụ';
            document.getElementById('service-id').value = service.id;
            document.getElementById('service-name').value = service.name;
            document.getElementById('service-icon').value = service.icon;
            document.getElementById('service-description').value = service.description;
            document.getElementById('service-price').value = service.price;
            document.getElementById('service-price-unit').value = service.priceUnit;
            document.getElementById('service-image').value = service.image || '';
            document.getElementById('service-features').value = service.features.join('\n');
            document.getElementById('service-active').checked = service.active;
            document.getElementById('service-featured').checked = service.featured;
        }
    } else {
        // Add mode
        title.textContent = 'Thêm Dịch Vụ Mới';
        form.reset();
        document.getElementById('service-active').checked = true;
    }
    
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

/**
 * Close Service Modal
 */
function closeServiceModal() {
    const modal = document.getElementById('service-modal');
    modal.classList.remove('active');
    document.body.style.overflow = '';
    editingServiceId = null;
}

/**
 * Handle Service Form Submit
 */
function handleServiceSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const serviceData = {
        name: formData.get('name'),
        icon: formData.get('icon') || 'fas fa-car',
        description: formData.get('description'),
        price: formData.get('price'),
        priceUnit: formData.get('priceUnit') || '/chuyến',
        image: formData.get('image'),
        features: formData.get('features').split('\n').filter(f => f.trim()),
        active: formData.has('active'),
        featured: formData.has('featured')
    };
    
    let success = false;
    
    if (editingServiceId) {
        success = window.dataManager.updateService(editingServiceId, serviceData);
    } else {
        success = window.dataManager.addService(serviceData);
    }
    
    if (success) {
        showNotification('Lưu dịch vụ thành công!', 'success');
        closeServiceModal();
        loadServices();
    } else {
        showNotification('Có lỗi xảy ra, vui lòng thử lại', 'error');
    }
}

/**
 * Edit Service
 */
function editService(serviceId) {
    showServiceModal(serviceId);
}

/**
 * Delete Service
 */
function deleteService(serviceId) {
    if (confirm('Bạn có chắc chắn muốn xóa dịch vụ này?')) {
        const success = window.dataManager.deleteService(serviceId);
        if (success) {
            showNotification('Xóa dịch vụ thành công!', 'success');
            loadServices();
        } else {
            showNotification('Có lỗi xảy ra, vui lòng thử lại', 'error');
        }
    }
}

/**
 * Save Settings
 */
function saveSettings() {
    const generalData = new FormData(document.getElementById('general-settings-form'));
    const contactData = new FormData(document.getElementById('contact-settings-form'));
    const heroData = new FormData(document.getElementById('hero-settings-form'));
    
    const settings = {
        siteName: generalData.get('siteName'),
        siteTitle: generalData.get('siteTitle'),
        siteDescription: generalData.get('siteDescription'),
        companyName: generalData.get('companyName'),
        phone: contactData.get('phone'),
        email: contactData.get('email'),
        address: contactData.get('address'),
        heroTitle: heroData.get('heroTitle'),
        heroSubtitle: heroData.get('heroSubtitle')
    };
    
    const success = window.dataManager.updateSettings(settings);
    
    if (success) {
        showNotification('Lưu cài đặt thành công!', 'success');
    } else {
        showNotification('Có lỗi xảy ra, vui lòng thử lại', 'error');
    }
}

/**
 * Utility Functions
 */

function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 60) return `${minutes} phút trước`;
    if (hours < 24) return `${hours} giờ trước`;
    return `${days} ngày trước`;
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getStatusText(status) {
    const statusMap = {
        pending: 'Chờ xử lý',
        confirmed: 'Đã xác nhận',
        completed: 'Hoàn thành',
        cancelled: 'Đã hủy'
    };
    return statusMap[status] || status;
}

function getMessageStatusText(status) {
    const statusMap = {
        unread: 'Chưa đọc',
        read: 'Đã đọc',
        replied: 'Đã trả lời'
    };
    return statusMap[status] || status;
}

function showNotification(message, type = 'info') {
    const notification = document.getElementById('admin-notification');
    const icon = notification.querySelector('.notification-icon');
    const messageEl = notification.querySelector('.notification-message');

    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    icon.className = `notification-icon ${icons[type]}`;
    messageEl.textContent = message;
    notification.className = `notification ${type}`;
    notification.classList.add('show');

    setTimeout(() => {
        notification.classList.remove('show');
    }, 5000);
}

/**
 * External Functions
 */

function viewWebsite() {
    window.open('../index.html', '_blank');
}

function exportData() {
    const data = window.dataManager.exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `taxi-cms-backup-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    showNotification('Export dữ liệu thành công!', 'success');
}

function showImportModal() {
    document.getElementById('import-modal').classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeImportModal() {
    document.getElementById('import-modal').classList.remove('active');
    document.body.style.overflow = '';
}

function importData() {
    const data = document.getElementById('import-data').value;
    
    if (!data.trim()) {
        showNotification('Vui lòng nhập dữ liệu JSON', 'warning');
        return;
    }
    
    const success = window.dataManager.importData(data);
    
    if (success) {
        showNotification('Import dữ liệu thành công!', 'success');
        closeImportModal();
        location.reload();
    } else {
        showNotification('Dữ liệu không hợp lệ', 'error');
    }
}
