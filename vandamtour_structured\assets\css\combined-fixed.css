/* VẠN DẶM TOUR - Combined CSS for Structured Website */
/* Generated by CSS Fixer */

/* Reset and base styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Container and layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

/* Header styles */
#header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

#top-bar {
    background: linear-gradient(90deg, #61c73f, #29b94c);
    color: white;
    padding: 10px 0;
    font-size: 14px;
}

#top-bar a {
    color: #e4ff00;
    text-decoration: none;
}

.header-main {
    padding: 15px 0;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

/* Navigation */
.nav-main {
    background: #f8f9fa;
    padding: 10px 0;
}

.nav-main ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

.nav-main li {
    margin-right: 30px;
}

.nav-main a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 10px 0;
    transition: color 0.3s;
}

.nav-main a:hover {
    color: #29b94c;
}

/* Slider/Banner */
.slider-wrapper {
    position: relative;
    overflow: hidden;
}

.slider img {
    width: 100%;
    height: auto;
    display: block;
}

/* Services section */
.services-section {
    padding: 60px 0;
    background: #f8f9fa;
}

.service-item {
    text-align: center;
    padding: 30px 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    transition: transform 0.3s;
}

.service-item:hover {
    transform: translateY(-5px);
}

.service-item img {
    max-width: 100%;
    height: auto;
    margin-bottom: 20px;
}

.service-item h3 {
    color: #333;
    margin-bottom: 15px;
}

/* Contact buttons */
.button-contact {
    position: fixed;
    z-index: 9999;
}

.button-contact.phone {
    bottom: 120px;
    right: 20px;
}

.button-contact.zalo {
    bottom: 60px;
    right: 20px;
}

.button-contact img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
    transition: transform 0.3s;
}

.button-contact:hover img {
    transform: scale(1.1);
}

/* Forms */
.wpcf7-form {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.wpcf7-form input,
.wpcf7-form textarea,
.wpcf7-form select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 15px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.wpcf7-form input:focus,
.wpcf7-form textarea:focus,
.wpcf7-form select:focus {
    border-color: #29b94c;
    outline: none;
    box-shadow: 0 0 5px rgba(41, 185, 76, 0.3);
}

.wpcf7-form input[type="submit"] {
    background: linear-gradient(90deg, #61c73f, #29b94c);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: transform 0.3s;
}

.wpcf7-form input[type="submit"]:hover {
    transform: translateY(-2px);
}

/* Footer */
#footer {
    background: #333;
    color: white;
    padding: 40px 0 20px;
    margin-top: 60px;
}

#footer h3 {
    color: #29b94c;
    margin-bottom: 20px;
}

#footer a {
    color: #ccc;
    text-decoration: none;
}

#footer a:hover {
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .nav-main ul {
        flex-direction: column;
    }
    
    .nav-main li {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .service-item {
        margin-bottom: 20px;
    }
    
    .button-contact.phone {
        bottom: 100px;
        right: 15px;
    }
    
    .button-contact.zalo {
        bottom: 50px;
        right: 15px;
    }
    
    .button-contact img {
        width: 45px;
        height: 45px;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }

/* Print styles */
@media print {
    .button-contact,
    .no-print {
        display: none !important;
    }
}
