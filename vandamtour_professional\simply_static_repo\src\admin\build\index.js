(()=>{var e,t,n,a,l={744:e=>{"use strict";var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===n}(e)}(e)},n="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function a(e,t){return!1!==t.clone&&t.isMergeableObject(e)?o((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function l(e,t,n){return e.concat(t).map((function(e){return a(e,n)}))}function r(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function i(e,t){try{return t in e}catch(e){return!1}}function o(e,n,s){(s=s||{}).arrayMerge=s.arrayMerge||l,s.isMergeableObject=s.isMergeableObject||t,s.cloneUnlessOtherwiseSpecified=a;var c=Array.isArray(n);return c===Array.isArray(e)?c?s.arrayMerge(e,n,s):function(e,t,n){var l={};return n.isMergeableObject(e)&&r(e).forEach((function(t){l[t]=a(e[t],n)})),r(t).forEach((function(r){(function(e,t){return i(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,r)||(i(e,r)&&n.isMergeableObject(t[r])?l[r]=function(e,t){if(!t.customMerge)return o;var n=t.customMerge(e);return"function"==typeof n?n:o}(r,n)(e[r],t[r],n):l[r]=a(t[r],n))})),l}(e,n,s):a(n,s)}o.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,n){return o(e,n,t)}),{})};var s=o;e.exports=s},147:e=>{function t(e,t){e.onload=function(){this.onerror=this.onload=null,t(null,e)},e.onerror=function(){this.onerror=this.onload=null,t(new Error("Failed to load "+this.src),e)}}function n(e,t){e.onreadystatechange=function(){"complete"!=this.readyState&&"loaded"!=this.readyState||(this.onreadystatechange=null,t(null,e))}}e.exports=function(e,a,l){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("script");"function"==typeof a&&(l=a,a={}),a=a||{},l=l||function(){},i.type=a.type||"text/javascript",i.charset=a.charset||"utf8",i.async=!("async"in a)||!!a.async,i.src=e,a.attrs&&function(e,t){for(var n in t)e.setAttribute(n,t[n])}(i,a.attrs),a.text&&(i.text=""+a.text),("onload"in i?t:n)(i,l),i.onload||t(i,l),r.appendChild(i)}},811:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});var a=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function l(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!((l=e[n])===(r=t[n])||a(l)&&a(r)))return!1;var l,r;return!0}const r=function(e,t){var n;void 0===t&&(t=l);var a,r=[],i=!1;return function(){for(var l=[],o=0;o<arguments.length;o++)l[o]=arguments[o];return i&&n===this&&t(l,r)||(a=e.apply(this,l),i=!0,n=this,r=l),a}}},694:(e,t,n)=>{"use strict";var a=n(925);function l(){}function r(){}r.resetWarningCache=l,e.exports=function(){function e(e,t,n,l,r,i){if(i!==a){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:r,resetWarningCache:l};return n.PropTypes=n,n}},556:(e,t,n)=>{e.exports=n(694)()},925:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},757:(e,t,n)=>{"use strict";var a=n(609),l=n(510);function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i,o=function(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}(a),s=r(a),c=r(l);function u(e,t){return e[t]}function p(e=[],t,n=0){return[...e.slice(0,n),t,...e.slice(n)]}function d(e=[],t,n="id"){const a=e.slice(),l=u(t,n);return l?a.splice(a.findIndex((e=>u(e,n)===l)),1):a.splice(a.findIndex((e=>e===t)),1),a}function m(e){return e.map(((e,t)=>{const n=Object.assign(Object.assign({},e),{sortable:e.sortable||!!e.sortFunction||void 0});return e.id||(n.id=t+1),n}))}function h(e,t){return Math.ceil(e/t)}function g(e,t){return Math.min(e,t)}!function(e){e.ASC="asc",e.DESC="desc"}(i||(i={}));const y=()=>null;function f(e,t=[],n=[]){let a={},l=[...n];return t.length&&t.forEach((t=>{if(!t.when||"function"!=typeof t.when)throw new Error('"when" must be defined in the conditional style object and must be function');t.when(e)&&(a=t.style||{},t.classNames&&(l=[...l,...t.classNames]),"function"==typeof t.style&&(a=t.style(e)||{}))})),{conditionalStyle:a,classNames:l.join(" ")}}function b(e,t=[],n="id"){const a=u(e,n);return a?t.some((e=>u(e,n)===a)):t.some((t=>t===e))}function E(e,t){return t?e.findIndex((e=>v(e.id,t))):-1}function v(e,t){return e==t}function w(e,t){const n=!e.toggleOnSelectedRowsChange;switch(t.type){case"SELECT_ALL_ROWS":{const{keyField:n,rows:a,rowCount:l,mergeSelections:r}=t,i=!e.allSelected,o=!e.toggleOnSelectedRowsChange;if(r){const t=i?[...e.selectedRows,...a.filter((t=>!b(t,e.selectedRows,n)))]:e.selectedRows.filter((e=>!b(e,a,n)));return Object.assign(Object.assign({},e),{allSelected:i,selectedCount:t.length,selectedRows:t,toggleOnSelectedRowsChange:o})}return Object.assign(Object.assign({},e),{allSelected:i,selectedCount:i?l:0,selectedRows:i?a:[],toggleOnSelectedRowsChange:o})}case"SELECT_SINGLE_ROW":{const{keyField:a,row:l,isSelected:r,rowCount:i,singleSelect:o}=t;return o?r?Object.assign(Object.assign({},e),{selectedCount:0,allSelected:!1,selectedRows:[],toggleOnSelectedRowsChange:n}):Object.assign(Object.assign({},e),{selectedCount:1,allSelected:!1,selectedRows:[l],toggleOnSelectedRowsChange:n}):r?Object.assign(Object.assign({},e),{selectedCount:e.selectedRows.length>0?e.selectedRows.length-1:0,allSelected:!1,selectedRows:d(e.selectedRows,l,a),toggleOnSelectedRowsChange:n}):Object.assign(Object.assign({},e),{selectedCount:e.selectedRows.length+1,allSelected:e.selectedRows.length+1===i,selectedRows:p(e.selectedRows,l),toggleOnSelectedRowsChange:n})}case"SELECT_MULTIPLE_ROWS":{const{keyField:a,selectedRows:l,totalRows:r,mergeSelections:i}=t;if(i){const t=[...e.selectedRows,...l.filter((t=>!b(t,e.selectedRows,a)))];return Object.assign(Object.assign({},e),{selectedCount:t.length,allSelected:!1,selectedRows:t,toggleOnSelectedRowsChange:n})}return Object.assign(Object.assign({},e),{selectedCount:l.length,allSelected:l.length===r,selectedRows:l,toggleOnSelectedRowsChange:n})}case"CLEAR_SELECTED_ROWS":{const{selectedRowsFlag:n}=t;return Object.assign(Object.assign({},e),{allSelected:!1,selectedCount:0,selectedRows:[],selectedRowsFlag:n})}case"SORT_CHANGE":{const{sortDirection:a,selectedColumn:l,clearSelectedOnSort:r}=t;return Object.assign(Object.assign(Object.assign({},e),{selectedColumn:l,sortDirection:a,currentPage:1}),r&&{allSelected:!1,selectedCount:0,selectedRows:[],toggleOnSelectedRowsChange:n})}case"CHANGE_PAGE":{const{page:a,paginationServer:l,visibleOnly:r,persistSelectedOnPageChange:i}=t,o=l&&i,s=l&&!i||r;return Object.assign(Object.assign(Object.assign(Object.assign({},e),{currentPage:a}),o&&{allSelected:!1}),s&&{allSelected:!1,selectedCount:0,selectedRows:[],toggleOnSelectedRowsChange:n})}case"CHANGE_ROWS_PER_PAGE":{const{rowsPerPage:n,page:a}=t;return Object.assign(Object.assign({},e),{currentPage:a,rowsPerPage:n})}}}const _=l.css`
	pointer-events: none;
	opacity: 0.4;
`,S=c.default.div`
	position: relative;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	max-width: 100%;
	${({disabled:e})=>e&&_};
	${({theme:e})=>e.table.style};
`,C=l.css`
	position: sticky;
	position: -webkit-sticky; /* Safari */
	top: 0;
	z-index: 1;
`,x=c.default.div`
	display: flex;
	width: 100%;
	${({$fixedHeader:e})=>e&&C};
	${({theme:e})=>e.head.style};
`,P=c.default.div`
	display: flex;
	align-items: stretch;
	width: 100%;
	${({theme:e})=>e.headRow.style};
	${({$dense:e,theme:t})=>e&&t.headRow.denseStyle};
`,k=(e,...t)=>l.css`
		@media screen and (max-width: ${599}px) {
			${l.css(e,...t)}
		}
	`,R=(e,...t)=>l.css`
		@media screen and (max-width: ${959}px) {
			${l.css(e,...t)}
		}
	`,O=(e,...t)=>l.css`
		@media screen and (max-width: ${1280}px) {
			${l.css(e,...t)}
		}
	`,T=c.default.div`
	position: relative;
	display: flex;
	align-items: center;
	box-sizing: border-box;
	line-height: normal;
	${({theme:e,$headCell:t})=>e[t?"headCells":"cells"].style};
	${({$noPadding:e})=>e&&"padding: 0"};
`,I=c.default(T)`
	flex-grow: ${({button:e,grow:t})=>0===t||e?0:t||1};
	flex-shrink: 0;
	flex-basis: 0;
	max-width: ${({maxWidth:e})=>e||"100%"};
	min-width: ${({minWidth:e})=>e||"100px"};
	${({width:e})=>e&&l.css`
			min-width: ${e};
			max-width: ${e};
		`};
	${({right:e})=>e&&"justify-content: flex-end"};
	${({button:e,center:t})=>(t||e)&&"justify-content: center"};
	${({compact:e,button:t})=>(e||t)&&"padding: 0"};

	/* handle hiding cells */
	${({hide:e})=>e&&"sm"===e&&k`
    display: none;
  `};
	${({hide:e})=>e&&"md"===e&&R`
    display: none;
  `};
	${({hide:e})=>e&&"lg"===e&&O`
    display: none;
  `};
	${({hide:e})=>e&&Number.isInteger(e)&&(e=>(t,...n)=>l.css`
			@media screen and (max-width: ${e}px) {
				${l.css(t,...n)}
			}
		`)(e)`
    display: none;
  `};
`,D=l.css`
	div:first-child {
		white-space: ${({$wrapCell:e})=>e?"normal":"nowrap"};
		overflow: ${({$allowOverflow:e})=>e?"visible":"hidden"};
		text-overflow: ellipsis;
	}
`,A=c.default(I).attrs((e=>({style:e.style})))`
	${({$renderAsCell:e})=>!e&&D};
	${({theme:e,$isDragging:t})=>t&&e.cells.draggingStyle};
	${({$cellStyle:e})=>e};
`;var N=o.memo((function({id:e,column:t,row:n,rowIndex:a,dataTag:l,isDragging:r,onDragStart:i,onDragOver:s,onDragEnd:c,onDragEnter:u,onDragLeave:p}){const{conditionalStyle:d,classNames:m}=f(n,t.conditionalCellStyles,["rdt_TableCell"]);return o.createElement(A,{id:e,"data-column-id":t.id,role:"cell",className:m,"data-tag":l,$cellStyle:t.style,$renderAsCell:!!t.cell,$allowOverflow:t.allowOverflow,button:t.button,center:t.center,compact:t.compact,grow:t.grow,hide:t.hide,maxWidth:t.maxWidth,minWidth:t.minWidth,right:t.right,width:t.width,$wrapCell:t.wrap,style:d,$isDragging:r,onDragStart:i,onDragOver:s,onDragEnd:c,onDragEnter:u,onDragLeave:p},!t.cell&&o.createElement("div",{"data-tag":l},function(e,t,n,a){return t?n&&"function"==typeof n?n(e,a):t(e,a):null}(n,t.selector,t.format,a)),t.cell&&t.cell(n,a,t,e))}));const F="input";var j=o.memo((function({name:e,component:t=F,componentOptions:n={style:{}},indeterminate:a=!1,checked:l=!1,disabled:r=!1,onClick:i=y}){const s=t,c=s!==F?n.style:(e=>Object.assign(Object.assign({fontSize:"18px"},!e&&{cursor:"pointer"}),{padding:0,marginTop:"1px",verticalAlign:"middle",position:"relative"}))(r),u=o.useMemo((()=>function(e,...t){let n;return Object.keys(e).map((t=>e[t])).forEach(((a,l)=>{const r=e;"function"==typeof a&&(n=Object.assign(Object.assign({},r),{[Object.keys(e)[l]]:a(...t)}))})),n||e}(n,a)),[n,a]);return o.createElement(s,Object.assign({type:"checkbox",ref:e=>{e&&(e.indeterminate=a)},style:c,onClick:r?y:i,name:e,"aria-label":e,checked:l,disabled:r},u,{onChange:y}))}));const $=c.default(T)`
	flex: 0 0 48px;
	min-width: 48px;
	justify-content: center;
	align-items: center;
	user-select: none;
	white-space: nowrap;
`;function L({name:e,keyField:t,row:n,rowCount:a,selected:l,selectableRowsComponent:r,selectableRowsComponentProps:i,selectableRowsSingle:s,selectableRowDisabled:c,onSelectedRow:u}){const p=!(!c||!c(n));return o.createElement($,{onClick:e=>e.stopPropagation(),className:"rdt_TableCell",$noPadding:!0},o.createElement(j,{name:e,component:r,componentOptions:i,checked:l,"aria-checked":l,onClick:()=>{u({type:"SELECT_SINGLE_ROW",row:n,isSelected:l,keyField:t,rowCount:a,singleSelect:s})},disabled:p}))}const H=c.default.button`
	display: inline-flex;
	align-items: center;
	user-select: none;
	white-space: nowrap;
	border: none;
	background-color: transparent;
	${({theme:e})=>e.expanderButton.style};
`;function M({disabled:e=!1,expanded:t=!1,expandableIcon:n,id:a,row:l,onToggled:r}){const i=t?n.expanded:n.collapsed;return o.createElement(H,{"aria-disabled":e,onClick:()=>r&&r(l),"data-testid":`expander-button-${a}`,disabled:e,"aria-label":t?"Collapse Row":"Expand Row",role:"button",type:"button"},i)}const B=c.default(T)`
	white-space: nowrap;
	font-weight: 400;
	min-width: 48px;
	${({theme:e})=>e.expanderCell.style};
`;function z({row:e,expanded:t=!1,expandableIcon:n,id:a,onToggled:l,disabled:r=!1}){return o.createElement(B,{onClick:e=>e.stopPropagation(),$noPadding:!0},o.createElement(M,{id:a,row:e,expanded:t,expandableIcon:n,disabled:r,onToggled:l}))}const U=c.default.div`
	width: 100%;
	box-sizing: border-box;
	${({theme:e})=>e.expanderRow.style};
	${({$extendedRowStyle:e})=>e};
`;var W=o.memo((function({data:e,ExpanderComponent:t,expanderComponentProps:n,extendedRowStyle:a,extendedClassNames:l}){const r=["rdt_ExpanderRow",...l.split(" ").filter((e=>"rdt_TableRow"!==e))].join(" ");return o.createElement(U,{className:r,$extendedRowStyle:a},o.createElement(t,Object.assign({data:e},n)))}));const G="allowRowEvents";var V,q,Y;t.OP=void 0,(V=t.OP||(t.OP={})).LTR="ltr",V.RTL="rtl",V.AUTO="auto",t.C1=void 0,(q=t.C1||(t.C1={})).LEFT="left",q.RIGHT="right",q.CENTER="center",t.$U=void 0,(Y=t.$U||(t.$U={})).SM="sm",Y.MD="md",Y.LG="lg";const J=l.css`
	&:hover {
		${({$highlightOnHover:e,theme:t})=>e&&t.rows.highlightOnHoverStyle};
	}
`,K=l.css`
	&:hover {
		cursor: pointer;
	}
`,Z=c.default.div.attrs((e=>({style:e.style})))`
	display: flex;
	align-items: stretch;
	align-content: stretch;
	width: 100%;
	box-sizing: border-box;
	${({theme:e})=>e.rows.style};
	${({$dense:e,theme:t})=>e&&t.rows.denseStyle};
	${({$striped:e,theme:t})=>e&&t.rows.stripedStyle};
	${({$highlightOnHover:e})=>e&&J};
	${({$pointerOnHover:e})=>e&&K};
	${({$selected:e,theme:t})=>e&&t.rows.selectedHighlightStyle};
	${({$conditionalStyle:e})=>e};
`;function X({columns:e=[],conditionalRowStyles:t=[],defaultExpanded:n=!1,defaultExpanderDisabled:a=!1,dense:l=!1,expandableIcon:r,expandableRows:i=!1,expandableRowsComponent:s,expandableRowsComponentProps:c,expandableRowsHideExpander:p,expandOnRowClicked:d=!1,expandOnRowDoubleClicked:m=!1,highlightOnHover:h=!1,id:g,expandableInheritConditionalStyles:b,keyField:E,onRowClicked:w=y,onRowDoubleClicked:_=y,onRowMouseEnter:S=y,onRowMouseLeave:C=y,onRowExpandToggled:x=y,onSelectedRow:P=y,pointerOnHover:k=!1,row:R,rowCount:O,rowIndex:T,selectableRowDisabled:I=null,selectableRows:D=!1,selectableRowsComponent:A,selectableRowsComponentProps:F,selectableRowsHighlight:j=!1,selectableRowsSingle:$=!1,selected:H,striped:M=!1,draggingColumnId:B,onDragStart:U,onDragOver:V,onDragEnd:q,onDragEnter:Y,onDragLeave:J}){const[K,X]=o.useState(n);o.useEffect((()=>{X(n)}),[n]);const Q=o.useCallback((()=>{X(!K),x(!K,R)}),[K,x,R]),ee=k||i&&(d||m),te=o.useCallback((e=>{e.target.getAttribute("data-tag")===G&&(w(R,e),!a&&i&&d&&Q())}),[a,d,i,Q,w,R]),ne=o.useCallback((e=>{e.target.getAttribute("data-tag")===G&&(_(R,e),!a&&i&&m&&Q())}),[a,m,i,Q,_,R]),ae=o.useCallback((e=>{S(R,e)}),[S,R]),le=o.useCallback((e=>{C(R,e)}),[C,R]),re=u(R,E),{conditionalStyle:ie,classNames:oe}=f(R,t,["rdt_TableRow"]),se=j&&H,ce=b?ie:{},ue=M&&T%2==0;return o.createElement(o.Fragment,null,o.createElement(Z,{id:`row-${g}`,role:"row",$striped:ue,$highlightOnHover:h,$pointerOnHover:!a&&ee,$dense:l,onClick:te,onDoubleClick:ne,onMouseEnter:ae,onMouseLeave:le,className:oe,$selected:se,$conditionalStyle:ie},D&&o.createElement(L,{name:`select-row-${re}`,keyField:E,row:R,rowCount:O,selected:H,selectableRowsComponent:A,selectableRowsComponentProps:F,selectableRowDisabled:I,selectableRowsSingle:$,onSelectedRow:P}),i&&!p&&o.createElement(z,{id:re,expandableIcon:r,expanded:K,row:R,onToggled:Q,disabled:a}),e.map((e=>e.omit?null:o.createElement(N,{id:`cell-${e.id}-${re}`,key:`cell-${e.id}-${re}`,dataTag:e.ignoreRowClick||e.button?null:G,column:e,row:R,rowIndex:T,isDragging:v(B,e.id),onDragStart:U,onDragOver:V,onDragEnd:q,onDragEnter:Y,onDragLeave:J})))),i&&K&&o.createElement(W,{key:`expander-${re}`,data:R,extendedRowStyle:ce,extendedClassNames:oe,ExpanderComponent:s,expanderComponentProps:c}))}const Q=c.default.span`
	padding: 2px;
	color: inherit;
	flex-grow: 0;
	flex-shrink: 0;
	${({$sortActive:e})=>e?"opacity: 1":"opacity: 0"};
	${({$sortDirection:e})=>"desc"===e&&"transform: rotate(180deg)"};
`,ee=({sortActive:e,sortDirection:t})=>s.default.createElement(Q,{$sortActive:e,$sortDirection:t},"▲"),te=c.default(I)`
	${({button:e})=>e&&"text-align: center"};
	${({theme:e,$isDragging:t})=>t&&e.headCells.draggingStyle};
`,ne=l.css`
	cursor: pointer;
	span.__rdt_custom_sort_icon__ {
		i,
		svg {
			transform: 'translate3d(0, 0, 0)';
			${({$sortActive:e})=>e?"opacity: 1":"opacity: 0"};
			color: inherit;
			font-size: 18px;
			height: 18px;
			width: 18px;
			backface-visibility: hidden;
			transform-style: preserve-3d;
			transition-duration: 95ms;
			transition-property: transform;
		}

		&.asc i,
		&.asc svg {
			transform: rotate(180deg);
		}
	}

	${({$sortActive:e})=>!e&&l.css`
			&:hover,
			&:focus {
				opacity: 0.7;

				span,
				span.__rdt_custom_sort_icon__ * {
					opacity: 0.7;
				}
			}
		`};
`,ae=c.default.div`
	display: inline-flex;
	align-items: center;
	justify-content: inherit;
	height: 100%;
	width: 100%;
	outline: none;
	user-select: none;
	overflow: hidden;
	${({disabled:e})=>!e&&ne};
`,le=c.default.div`
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
`;var re=o.memo((function({column:e,disabled:t,draggingColumnId:n,selectedColumn:a={},sortDirection:l,sortIcon:r,sortServer:s,pagination:c,paginationServer:u,persistSelectedOnSort:p,selectableRowsVisibleOnly:d,onSort:m,onDragStart:h,onDragOver:g,onDragEnd:y,onDragEnter:f,onDragLeave:b}){o.useEffect((()=>{"string"==typeof e.selector&&console.error(`Warning: ${e.selector} is a string based column selector which has been deprecated as of v7 and will be removed in v8. Instead, use a selector function e.g. row => row[field]...`)}),[]);const[E,w]=o.useState(!1),_=o.useRef(null);if(o.useEffect((()=>{_.current&&w(_.current.scrollWidth>_.current.clientWidth)}),[E]),e.omit)return null;const S=()=>{if(!e.sortable&&!e.selector)return;let t=l;v(a.id,e.id)&&(t=l===i.ASC?i.DESC:i.ASC),m({type:"SORT_CHANGE",sortDirection:t,selectedColumn:e,clearSelectedOnSort:c&&u&&!p||s||d})},C=e=>o.createElement(ee,{sortActive:e,sortDirection:l}),x=()=>o.createElement("span",{className:[l,"__rdt_custom_sort_icon__"].join(" ")},r),P=!(!e.sortable||!v(a.id,e.id)),k=!e.sortable||t,R=e.sortable&&!r&&!e.right,O=e.sortable&&!r&&e.right,T=e.sortable&&r&&!e.right,I=e.sortable&&r&&e.right;return o.createElement(te,{"data-column-id":e.id,className:"rdt_TableCol",$headCell:!0,allowOverflow:e.allowOverflow,button:e.button,compact:e.compact,grow:e.grow,hide:e.hide,maxWidth:e.maxWidth,minWidth:e.minWidth,right:e.right,center:e.center,width:e.width,draggable:e.reorder,$isDragging:v(e.id,n),onDragStart:h,onDragOver:g,onDragEnd:y,onDragEnter:f,onDragLeave:b},e.name&&o.createElement(ae,{"data-column-id":e.id,"data-sort-id":e.id,role:"columnheader",tabIndex:0,className:"rdt_TableCol_Sortable",onClick:k?void 0:S,onKeyPress:k?void 0:e=>{"Enter"===e.key&&S()},$sortActive:!k&&P,disabled:k},!k&&I&&x(),!k&&O&&C(P),"string"==typeof e.name?o.createElement(le,{title:E?e.name:void 0,ref:_,"data-column-id":e.id},e.name):e.name,!k&&T&&x(),!k&&R&&C(P)))}));const ie=c.default(T)`
	flex: 0 0 48px;
	justify-content: center;
	align-items: center;
	user-select: none;
	white-space: nowrap;
	font-size: unset;
`;function oe({headCell:e=!0,rowData:t,keyField:n,allSelected:a,mergeSelections:l,selectedRows:r,selectableRowsComponent:i,selectableRowsComponentProps:s,selectableRowDisabled:c,onSelectAllRows:u}){const p=r.length>0&&!a,d=c?t.filter((e=>!c(e))):t,m=0===d.length,h=Math.min(t.length,d.length);return o.createElement(ie,{className:"rdt_TableCol",$headCell:e,$noPadding:!0},o.createElement(j,{name:"select-all-rows",component:i,componentOptions:s,onClick:()=>{u({type:"SELECT_ALL_ROWS",rows:d,rowCount:h,mergeSelections:l,keyField:n})},checked:a,indeterminate:p,disabled:m}))}function se(e=t.OP.AUTO){const n="object"==typeof window,[a,l]=o.useState(!1);return o.useEffect((()=>{if(n)if("auto"!==e)l("rtl"===e);else{const e=!(!window.document||!window.document.createElement),t=document.getElementsByTagName("BODY")[0],n=document.getElementsByTagName("HTML")[0],a="rtl"===t.dir||"rtl"===n.dir;l(e&&a)}}),[e,n]),a}const ce=c.default.div`
	display: flex;
	align-items: center;
	flex: 1 0 auto;
	height: 100%;
	color: ${({theme:e})=>e.contextMenu.fontColor};
	font-size: ${({theme:e})=>e.contextMenu.fontSize};
	font-weight: 400;
`,ue=c.default.div`
	display: flex;
	align-items: center;
	justify-content: flex-end;
	flex-wrap: wrap;
`,pe=c.default.div`
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	box-sizing: inherit;
	z-index: 1;
	align-items: center;
	justify-content: space-between;
	display: flex;
	${({$rtl:e})=>e&&"direction: rtl"};
	${({theme:e})=>e.contextMenu.style};
	${({theme:e,$visible:t})=>t&&e.contextMenu.activeStyle};
`;function de({contextMessage:e,contextActions:t,contextComponent:n,selectedCount:a,direction:l}){const r=se(l),i=a>0;return n?o.createElement(pe,{$visible:i},o.cloneElement(n,{selectedCount:a})):o.createElement(pe,{$visible:i,$rtl:r},o.createElement(ce,null,((e,t,n)=>{if(0===t)return null;const a=1===t?e.singular:e.plural;return n?`${t} ${e.message||""} ${a}`:`${t} ${a} ${e.message||""}`})(e,a,r)),o.createElement(ue,null,t))}const me=c.default.div`
	position: relative;
	box-sizing: border-box;
	overflow: hidden;
	display: flex;
	flex: 1 1 auto;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	flex-wrap: wrap;
	${({theme:e})=>e.header.style}
`,he=c.default.div`
	flex: 1 0 auto;
	color: ${({theme:e})=>e.header.fontColor};
	font-size: ${({theme:e})=>e.header.fontSize};
	font-weight: 400;
`,ge=c.default.div`
	flex: 1 0 auto;
	display: flex;
	align-items: center;
	justify-content: flex-end;

	> * {
		margin-left: 5px;
	}
`,ye=({title:e,actions:t=null,contextMessage:n,contextActions:a,contextComponent:l,selectedCount:r,direction:i,showMenu:s=!0})=>o.createElement(me,{className:"rdt_TableHeader",role:"heading","aria-level":1},o.createElement(he,null,e),t&&o.createElement(ge,null,t),s&&o.createElement(de,{contextMessage:n,contextActions:a,contextComponent:l,direction:i,selectedCount:r}));function fe(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(n[a[l]]=e[a[l]])}return n}"function"==typeof SuppressedError&&SuppressedError;const be={left:"flex-start",right:"flex-end",center:"center"},Ee=c.default.header`
	position: relative;
	display: flex;
	flex: 1 1 auto;
	box-sizing: border-box;
	align-items: center;
	padding: 4px 16px 4px 24px;
	width: 100%;
	justify-content: ${({align:e})=>be[e]};
	flex-wrap: ${({$wrapContent:e})=>e?"wrap":"nowrap"};
	${({theme:e})=>e.subHeader.style}
`,ve=e=>{var{align:t="right",wrapContent:n=!0}=e,a=fe(e,["align","wrapContent"]);return o.createElement(Ee,Object.assign({align:t,$wrapContent:n},a))},we=c.default.div`
	display: flex;
	flex-direction: column;
`,_e=c.default.div`
	position: relative;
	width: 100%;
	border-radius: inherit;
	${({$responsive:e,$fixedHeader:t})=>e&&l.css`
			overflow-x: auto;

			// hidden prevents vertical scrolling in firefox when fixedHeader is disabled
			overflow-y: ${t?"auto":"hidden"};
			min-height: 0;
		`};

	${({$fixedHeader:e=!1,$fixedHeaderScrollHeight:t="100vh"})=>e&&l.css`
			max-height: ${t};
			-webkit-overflow-scrolling: touch;
		`};

	${({theme:e})=>e.responsiveWrapper.style};
`,Se=c.default.div`
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	${e=>e.theme.progress.style};
`,Ce=c.default.div`
	position: relative;
	width: 100%;
	${({theme:e})=>e.tableWrapper.style};
`,xe=c.default(T)`
	white-space: nowrap;
	${({theme:e})=>e.expanderCell.style};
`,Pe=c.default.div`
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	${({theme:e})=>e.noData.style};
`,ke=()=>s.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},s.default.createElement("path",{d:"M7 10l5 5 5-5z"}),s.default.createElement("path",{d:"M0 0h24v24H0z",fill:"none"})),Re=c.default.select`
	cursor: pointer;
	height: 24px;
	max-width: 100%;
	user-select: none;
	padding-left: 8px;
	padding-right: 24px;
	box-sizing: content-box;
	font-size: inherit;
	color: inherit;
	border: none;
	background-color: transparent;
	appearance: none;
	direction: ltr;
	flex-shrink: 0;

	&::-ms-expand {
		display: none;
	}

	&:disabled::-ms-expand {
		background: #f60;
	}

	option {
		color: initial;
	}
`,Oe=c.default.div`
	position: relative;
	flex-shrink: 0;
	font-size: inherit;
	color: inherit;
	margin-top: 1px;

	svg {
		top: 0;
		right: 0;
		color: inherit;
		position: absolute;
		fill: currentColor;
		width: 24px;
		height: 24px;
		display: inline-block;
		user-select: none;
		pointer-events: none;
	}
`,Te=e=>{var{defaultValue:t,onChange:n}=e,a=fe(e,["defaultValue","onChange"]);return o.createElement(Oe,null,o.createElement(Re,Object.assign({onChange:n,defaultValue:t},a)),o.createElement(ke,null))},Ie={columns:[],data:[],title:"",keyField:"id",selectableRows:!1,selectableRowsHighlight:!1,selectableRowsNoSelectAll:!1,selectableRowSelected:null,selectableRowDisabled:null,selectableRowsComponent:"input",selectableRowsComponentProps:{},selectableRowsVisibleOnly:!1,selectableRowsSingle:!1,clearSelectedRows:!1,expandableRows:!1,expandableRowDisabled:null,expandableRowExpanded:null,expandOnRowClicked:!1,expandableRowsHideExpander:!1,expandOnRowDoubleClicked:!1,expandableInheritConditionalStyles:!1,expandableRowsComponent:function(){return s.default.createElement("div",null,"To add an expander pass in a component instance via ",s.default.createElement("strong",null,"expandableRowsComponent"),". You can then access props.data from this component.")},expandableIcon:{collapsed:s.default.createElement((()=>s.default.createElement("svg",{fill:"currentColor",height:"24",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg"},s.default.createElement("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}),s.default.createElement("path",{d:"M0-.25h24v24H0z",fill:"none"}))),null),expanded:s.default.createElement((()=>s.default.createElement("svg",{fill:"currentColor",height:"24",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg"},s.default.createElement("path",{d:"M7.41 7.84L12 12.42l4.59-4.58L18 9.25l-6 6-6-6z"}),s.default.createElement("path",{d:"M0-.75h24v24H0z",fill:"none"}))),null)},expandableRowsComponentProps:{},progressPending:!1,progressComponent:s.default.createElement("div",{style:{fontSize:"24px",fontWeight:700,padding:"24px"}},"Loading..."),persistTableHead:!1,sortIcon:null,sortFunction:null,sortServer:!1,striped:!1,highlightOnHover:!1,pointerOnHover:!1,noContextMenu:!1,contextMessage:{singular:"item",plural:"items",message:"selected"},actions:null,contextActions:null,contextComponent:null,defaultSortFieldId:null,defaultSortAsc:!0,responsive:!0,noDataComponent:s.default.createElement("div",{style:{padding:"24px"}},"There are no records to display"),disabled:!1,noTableHead:!1,noHeader:!1,subHeader:!1,subHeaderAlign:t.C1.RIGHT,subHeaderWrap:!0,subHeaderComponent:null,fixedHeader:!1,fixedHeaderScrollHeight:"100vh",pagination:!1,paginationServer:!1,paginationServerOptions:{persistSelectedOnSort:!1,persistSelectedOnPageChange:!1},paginationDefaultPage:1,paginationResetDefaultPage:!1,paginationTotalRows:0,paginationPerPage:10,paginationRowsPerPageOptions:[10,15,20,25,30],paginationComponent:null,paginationComponentOptions:{},paginationIconFirstPage:s.default.createElement((()=>s.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24","aria-hidden":"true",role:"presentation"},s.default.createElement("path",{d:"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"}),s.default.createElement("path",{fill:"none",d:"M24 24H0V0h24v24z"}))),null),paginationIconLastPage:s.default.createElement((()=>s.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24","aria-hidden":"true",role:"presentation"},s.default.createElement("path",{d:"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"}),s.default.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}))),null),paginationIconNext:s.default.createElement((()=>s.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24","aria-hidden":"true",role:"presentation"},s.default.createElement("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"}),s.default.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}))),null),paginationIconPrevious:s.default.createElement((()=>s.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24","aria-hidden":"true",role:"presentation"},s.default.createElement("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"}),s.default.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}))),null),dense:!1,conditionalRowStyles:[],theme:"default",customStyles:{},direction:t.OP.AUTO,onChangePage:y,onChangeRowsPerPage:y,onRowClicked:y,onRowDoubleClicked:y,onRowMouseEnter:y,onRowMouseLeave:y,onRowExpandToggled:y,onSelectedRowsChange:y,onSort:y,onColumnOrderChange:y},De={rowsPerPageText:"Rows per page:",rangeSeparatorText:"of",noRowsPerPage:!1,selectAllRowsItem:!1,selectAllRowsItemText:"All"},Ae=c.default.nav`
	display: flex;
	flex: 1 1 auto;
	justify-content: flex-end;
	align-items: center;
	box-sizing: border-box;
	padding-right: 8px;
	padding-left: 8px;
	width: 100%;
	${({theme:e})=>e.pagination.style};
`,Ne=c.default.button`
	position: relative;
	display: block;
	user-select: none;
	border: none;
	${({theme:e})=>e.pagination.pageButtonsStyle};
	${({$isRTL:e})=>e&&"transform: scale(-1, -1)"};
`,Fe=c.default.div`
	display: flex;
	align-items: center;
	border-radius: 4px;
	white-space: nowrap;
	${k`
    width: 100%;
    justify-content: space-around;
  `};
`,je=c.default.span`
	flex-shrink: 1;
	user-select: none;
`,$e=c.default(je)`
	margin: 0 24px;
`,Le=c.default(je)`
	margin: 0 4px;
`;var He=o.memo((function({rowsPerPage:e,rowCount:t,currentPage:n,direction:a=Ie.direction,paginationRowsPerPageOptions:l=Ie.paginationRowsPerPageOptions,paginationIconLastPage:r=Ie.paginationIconLastPage,paginationIconFirstPage:i=Ie.paginationIconFirstPage,paginationIconNext:s=Ie.paginationIconNext,paginationIconPrevious:c=Ie.paginationIconPrevious,paginationComponentOptions:u=Ie.paginationComponentOptions,onChangeRowsPerPage:p=Ie.onChangeRowsPerPage,onChangePage:d=Ie.onChangePage}){const m=(()=>{const e="object"==typeof window;function t(){return{width:e?window.innerWidth:void 0,height:e?window.innerHeight:void 0}}const[n,a]=o.useState(t);return o.useEffect((()=>{if(!e)return()=>null;function n(){a(t())}return window.addEventListener("resize",n),()=>window.removeEventListener("resize",n)}),[]),n})(),g=se(a),y=m.width&&m.width>599,f=h(t,e),b=n*e,E=b-e+1,v=1===n,w=n===f,_=Object.assign(Object.assign({},De),u),S=n===f?`${E}-${t} ${_.rangeSeparatorText} ${t}`:`${E}-${b} ${_.rangeSeparatorText} ${t}`,C=o.useCallback((()=>d(n-1)),[n,d]),x=o.useCallback((()=>d(n+1)),[n,d]),P=o.useCallback((()=>d(1)),[d]),k=o.useCallback((()=>d(h(t,e))),[d,t,e]),R=o.useCallback((e=>p(Number(e.target.value),n)),[n,p]),O=l.map((e=>o.createElement("option",{key:e,value:e},e)));_.selectAllRowsItem&&O.push(o.createElement("option",{key:-1,value:t},_.selectAllRowsItemText));const T=o.createElement(Te,{onChange:R,defaultValue:e,"aria-label":_.rowsPerPageText},O);return o.createElement(Ae,{className:"rdt_Pagination"},!_.noRowsPerPage&&y&&o.createElement(o.Fragment,null,o.createElement(Le,null,_.rowsPerPageText),T),y&&o.createElement($e,null,S),o.createElement(Fe,null,o.createElement(Ne,{id:"pagination-first-page",type:"button","aria-label":"First Page","aria-disabled":v,onClick:P,disabled:v,$isRTL:g},i),o.createElement(Ne,{id:"pagination-previous-page",type:"button","aria-label":"Previous Page","aria-disabled":v,onClick:C,disabled:v,$isRTL:g},c),!_.noRowsPerPage&&!y&&T,o.createElement(Ne,{id:"pagination-next-page",type:"button","aria-label":"Next Page","aria-disabled":w,onClick:x,disabled:w,$isRTL:g},s),o.createElement(Ne,{id:"pagination-last-page",type:"button","aria-label":"Last Page","aria-disabled":w,onClick:k,disabled:w,$isRTL:g},r)))}));const Me=(e,t)=>{const n=o.useRef(!0);o.useEffect((()=>{n.current?n.current=!1:e()}),t)};var Be=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===ze}(e)}(e)},ze="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function Ue(e,t){return!1!==t.clone&&t.isMergeableObject(e)?qe((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function We(e,t,n){return e.concat(t).map((function(e){return Ue(e,n)}))}function Ge(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function Ve(e,t){try{return t in e}catch(e){return!1}}function qe(e,t,n){(n=n||{}).arrayMerge=n.arrayMerge||We,n.isMergeableObject=n.isMergeableObject||Be,n.cloneUnlessOtherwiseSpecified=Ue;var a=Array.isArray(t);return a===Array.isArray(e)?a?n.arrayMerge(e,t,n):function(e,t,n){var a={};return n.isMergeableObject(e)&&Ge(e).forEach((function(t){a[t]=Ue(e[t],n)})),Ge(t).forEach((function(l){(function(e,t){return Ve(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,l)||(Ve(e,l)&&n.isMergeableObject(t[l])?a[l]=function(e,t){if(!t.customMerge)return qe;var n=t.customMerge(e);return"function"==typeof n?n:qe}(l,n)(e[l],t[l],n):a[l]=Ue(t[l],n))})),a}(e,t,n):Ue(t,n)}qe.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,n){return qe(e,n,t)}),{})};var Ye=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(qe);const Je={text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.54)",disabled:"rgba(0, 0, 0, 0.38)"},background:{default:"#FFFFFF"},context:{background:"#e3f2fd",text:"rgba(0, 0, 0, 0.87)"},divider:{default:"rgba(0,0,0,.12)"},button:{default:"rgba(0,0,0,.54)",focus:"rgba(0,0,0,.12)",hover:"rgba(0,0,0,.12)",disabled:"rgba(0, 0, 0, .18)"},selected:{default:"#e3f2fd",text:"rgba(0, 0, 0, 0.87)"},highlightOnHover:{default:"#EEEEEE",text:"rgba(0, 0, 0, 0.87)"},striped:{default:"#FAFAFA",text:"rgba(0, 0, 0, 0.87)"}},Ke={default:Je,light:Je,dark:{text:{primary:"#FFFFFF",secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(0,0,0,.12)"},background:{default:"#424242"},context:{background:"#E91E63",text:"#FFFFFF"},divider:{default:"rgba(81, 81, 81, 1)"},button:{default:"#FFFFFF",focus:"rgba(255, 255, 255, .54)",hover:"rgba(255, 255, 255, .12)",disabled:"rgba(255, 255, 255, .18)"},selected:{default:"rgba(0, 0, 0, .7)",text:"#FFFFFF"},highlightOnHover:{default:"rgba(0, 0, 0, .7)",text:"#FFFFFF"},striped:{default:"rgba(0, 0, 0, .87)",text:"#FFFFFF"}}};function Ze(e,t,n,a){const[l,r]=o.useState((()=>m(e))),[s,c]=o.useState(""),u=o.useRef("");Me((()=>{r(m(e))}),[e]);const p=o.useCallback((e=>{var t,n,a;const{attributes:r}=e.target,i=null===(t=r.getNamedItem("data-column-id"))||void 0===t?void 0:t.value;i&&(u.current=(null===(a=null===(n=l[E(l,i)])||void 0===n?void 0:n.id)||void 0===a?void 0:a.toString())||"",c(u.current))}),[l]),d=o.useCallback((e=>{var n;const{attributes:a}=e.target,i=null===(n=a.getNamedItem("data-column-id"))||void 0===n?void 0:n.value;if(i&&u.current&&i!==u.current){const e=E(l,u.current),n=E(l,i),a=[...l];a[e]=l[n],a[n]=l[e],r(a),t(a)}}),[t,l]),h=o.useCallback((e=>{e.preventDefault()}),[]),g=o.useCallback((e=>{e.preventDefault()}),[]),y=o.useCallback((e=>{e.preventDefault(),u.current="",c("")}),[]),f=function(e=!1){return e?i.ASC:i.DESC}(a),b=o.useMemo((()=>l[E(l,null==n?void 0:n.toString())]||{}),[n,l]);return{tableColumns:l,draggingColumnId:s,handleDragStart:p,handleDragEnter:d,handleDragOver:h,handleDragLeave:g,handleDragEnd:y,defaultSortDirection:f,defaultSortColumn:b}}var Xe=o.memo((function(e){const{data:t=Ie.data,columns:n=Ie.columns,title:a=Ie.title,actions:r=Ie.actions,keyField:s=Ie.keyField,striped:c=Ie.striped,highlightOnHover:p=Ie.highlightOnHover,pointerOnHover:d=Ie.pointerOnHover,dense:m=Ie.dense,selectableRows:y=Ie.selectableRows,selectableRowsSingle:f=Ie.selectableRowsSingle,selectableRowsHighlight:E=Ie.selectableRowsHighlight,selectableRowsNoSelectAll:v=Ie.selectableRowsNoSelectAll,selectableRowsVisibleOnly:_=Ie.selectableRowsVisibleOnly,selectableRowSelected:C=Ie.selectableRowSelected,selectableRowDisabled:k=Ie.selectableRowDisabled,selectableRowsComponent:R=Ie.selectableRowsComponent,selectableRowsComponentProps:O=Ie.selectableRowsComponentProps,onRowExpandToggled:I=Ie.onRowExpandToggled,onSelectedRowsChange:D=Ie.onSelectedRowsChange,expandableIcon:A=Ie.expandableIcon,onChangeRowsPerPage:N=Ie.onChangeRowsPerPage,onChangePage:F=Ie.onChangePage,paginationServer:j=Ie.paginationServer,paginationServerOptions:$=Ie.paginationServerOptions,paginationTotalRows:L=Ie.paginationTotalRows,paginationDefaultPage:H=Ie.paginationDefaultPage,paginationResetDefaultPage:M=Ie.paginationResetDefaultPage,paginationPerPage:B=Ie.paginationPerPage,paginationRowsPerPageOptions:z=Ie.paginationRowsPerPageOptions,paginationIconLastPage:U=Ie.paginationIconLastPage,paginationIconFirstPage:W=Ie.paginationIconFirstPage,paginationIconNext:G=Ie.paginationIconNext,paginationIconPrevious:V=Ie.paginationIconPrevious,paginationComponent:q=Ie.paginationComponent,paginationComponentOptions:Y=Ie.paginationComponentOptions,responsive:J=Ie.responsive,progressPending:K=Ie.progressPending,progressComponent:Z=Ie.progressComponent,persistTableHead:Q=Ie.persistTableHead,noDataComponent:ee=Ie.noDataComponent,disabled:te=Ie.disabled,noTableHead:ne=Ie.noTableHead,noHeader:ae=Ie.noHeader,fixedHeader:le=Ie.fixedHeader,fixedHeaderScrollHeight:ie=Ie.fixedHeaderScrollHeight,pagination:se=Ie.pagination,subHeader:ce=Ie.subHeader,subHeaderAlign:ue=Ie.subHeaderAlign,subHeaderWrap:pe=Ie.subHeaderWrap,subHeaderComponent:de=Ie.subHeaderComponent,noContextMenu:me=Ie.noContextMenu,contextMessage:he=Ie.contextMessage,contextActions:ge=Ie.contextActions,contextComponent:fe=Ie.contextComponent,expandableRows:be=Ie.expandableRows,onRowClicked:Ee=Ie.onRowClicked,onRowDoubleClicked:ke=Ie.onRowDoubleClicked,onRowMouseEnter:Re=Ie.onRowMouseEnter,onRowMouseLeave:Oe=Ie.onRowMouseLeave,sortIcon:Te=Ie.sortIcon,onSort:De=Ie.onSort,sortFunction:Ae=Ie.sortFunction,sortServer:Ne=Ie.sortServer,expandableRowsComponent:Fe=Ie.expandableRowsComponent,expandableRowsComponentProps:je=Ie.expandableRowsComponentProps,expandableRowDisabled:$e=Ie.expandableRowDisabled,expandableRowsHideExpander:Le=Ie.expandableRowsHideExpander,expandOnRowClicked:Be=Ie.expandOnRowClicked,expandOnRowDoubleClicked:ze=Ie.expandOnRowDoubleClicked,expandableRowExpanded:Ue=Ie.expandableRowExpanded,expandableInheritConditionalStyles:We=Ie.expandableInheritConditionalStyles,defaultSortFieldId:Ge=Ie.defaultSortFieldId,defaultSortAsc:Ve=Ie.defaultSortAsc,clearSelectedRows:qe=Ie.clearSelectedRows,conditionalRowStyles:Je=Ie.conditionalRowStyles,theme:Xe=Ie.theme,customStyles:Qe=Ie.customStyles,direction:et=Ie.direction,onColumnOrderChange:tt=Ie.onColumnOrderChange,className:nt}=e,{tableColumns:at,draggingColumnId:lt,handleDragStart:rt,handleDragEnter:it,handleDragOver:ot,handleDragLeave:st,handleDragEnd:ct,defaultSortDirection:ut,defaultSortColumn:pt}=Ze(n,tt,Ge,Ve),[{rowsPerPage:dt,currentPage:mt,selectedRows:ht,allSelected:gt,selectedCount:yt,selectedColumn:ft,sortDirection:bt,toggleOnSelectedRowsChange:Et},vt]=o.useReducer(w,{allSelected:!1,selectedCount:0,selectedRows:[],selectedColumn:pt,toggleOnSelectedRowsChange:!1,sortDirection:ut,currentPage:H,rowsPerPage:B,selectedRowsFlag:!1,contextMessage:Ie.contextMessage}),{persistSelectedOnSort:wt=!1,persistSelectedOnPageChange:_t=!1}=$,St=!(!j||!_t&&!wt),Ct=se&&!K&&t.length>0,xt=q||He,Pt=o.useMemo((()=>((e={},t="default",n="default")=>{return Ye({table:{style:{color:(a=Ke[Ke[t]?t:n]).text.primary,backgroundColor:a.background.default}},tableWrapper:{style:{display:"table"}},responsiveWrapper:{style:{}},header:{style:{fontSize:"22px",color:a.text.primary,backgroundColor:a.background.default,minHeight:"56px",paddingLeft:"16px",paddingRight:"8px"}},subHeader:{style:{backgroundColor:a.background.default,minHeight:"52px"}},head:{style:{color:a.text.primary,fontSize:"12px",fontWeight:500}},headRow:{style:{backgroundColor:a.background.default,minHeight:"52px",borderBottomWidth:"1px",borderBottomColor:a.divider.default,borderBottomStyle:"solid"},denseStyle:{minHeight:"32px"}},headCells:{style:{paddingLeft:"16px",paddingRight:"16px"},draggingStyle:{cursor:"move"}},contextMenu:{style:{backgroundColor:a.context.background,fontSize:"18px",fontWeight:400,color:a.context.text,paddingLeft:"16px",paddingRight:"8px",transform:"translate3d(0, -100%, 0)",transitionDuration:"125ms",transitionTimingFunction:"cubic-bezier(0, 0, 0.2, 1)",willChange:"transform"},activeStyle:{transform:"translate3d(0, 0, 0)"}},cells:{style:{paddingLeft:"16px",paddingRight:"16px",wordBreak:"break-word"},draggingStyle:{}},rows:{style:{fontSize:"13px",fontWeight:400,color:a.text.primary,backgroundColor:a.background.default,minHeight:"48px","&:not(:last-of-type)":{borderBottomStyle:"solid",borderBottomWidth:"1px",borderBottomColor:a.divider.default}},denseStyle:{minHeight:"32px"},selectedHighlightStyle:{"&:nth-of-type(n)":{color:a.selected.text,backgroundColor:a.selected.default,borderBottomColor:a.background.default}},highlightOnHoverStyle:{color:a.highlightOnHover.text,backgroundColor:a.highlightOnHover.default,transitionDuration:"0.15s",transitionProperty:"background-color",borderBottomColor:a.background.default,outlineStyle:"solid",outlineWidth:"1px",outlineColor:a.background.default},stripedStyle:{color:a.striped.text,backgroundColor:a.striped.default}},expanderRow:{style:{color:a.text.primary,backgroundColor:a.background.default}},expanderCell:{style:{flex:"0 0 48px"}},expanderButton:{style:{color:a.button.default,fill:a.button.default,backgroundColor:"transparent",borderRadius:"2px",transition:"0.25s",height:"100%",width:"100%","&:hover:enabled":{cursor:"pointer"},"&:disabled":{color:a.button.disabled},"&:hover:not(:disabled)":{cursor:"pointer",backgroundColor:a.button.hover},"&:focus":{outline:"none",backgroundColor:a.button.focus},svg:{margin:"auto"}}},pagination:{style:{color:a.text.secondary,fontSize:"13px",minHeight:"56px",backgroundColor:a.background.default,borderTopStyle:"solid",borderTopWidth:"1px",borderTopColor:a.divider.default},pageButtonsStyle:{borderRadius:"50%",height:"40px",width:"40px",padding:"8px",margin:"px",cursor:"pointer",transition:"0.4s",color:a.button.default,fill:a.button.default,backgroundColor:"transparent","&:disabled":{cursor:"unset",color:a.button.disabled,fill:a.button.disabled},"&:hover:not(:disabled)":{backgroundColor:a.button.hover},"&:focus":{outline:"none",backgroundColor:a.button.focus}}},noData:{style:{display:"flex",alignItems:"center",justifyContent:"center",color:a.text.primary,backgroundColor:a.background.default}},progress:{style:{display:"flex",alignItems:"center",justifyContent:"center",color:a.text.primary,backgroundColor:a.background.default}}},e);var a})(Qe,Xe)),[Qe,Xe]),kt=o.useMemo((()=>Object.assign({},"auto"!==et&&{dir:et})),[et]),Rt=o.useMemo((()=>{if(Ne)return t;if((null==ft?void 0:ft.sortFunction)&&"function"==typeof ft.sortFunction){const e=ft.sortFunction,n=bt===i.ASC?e:(t,n)=>-1*e(t,n);return[...t].sort(n)}return function(e,t,n,a){return t?a&&"function"==typeof a?a(e.slice(0),t,n):e.slice(0).sort(((e,a)=>{const l=t(e),r=t(a);if("asc"===n){if(l<r)return-1;if(l>r)return 1}if("desc"===n){if(l>r)return-1;if(l<r)return 1}return 0})):e}(t,null==ft?void 0:ft.selector,bt,Ae)}),[Ne,ft,bt,t,Ae]),Ot=o.useMemo((()=>{if(se&&!j){const e=mt*dt,t=e-dt;return Rt.slice(t,e)}return Rt}),[mt,se,j,dt,Rt]),Tt=o.useCallback((e=>{vt(e)}),[]),It=o.useCallback((e=>{vt(e)}),[]),Dt=o.useCallback((e=>{vt(e)}),[]),At=o.useCallback(((e,t)=>Ee(e,t)),[Ee]),Nt=o.useCallback(((e,t)=>ke(e,t)),[ke]),Ft=o.useCallback(((e,t)=>Re(e,t)),[Re]),jt=o.useCallback(((e,t)=>Oe(e,t)),[Oe]),$t=o.useCallback((e=>vt({type:"CHANGE_PAGE",page:e,paginationServer:j,visibleOnly:_,persistSelectedOnPageChange:_t})),[j,_t,_]),Lt=o.useCallback((e=>{const t=h(L||Ot.length,e),n=g(mt,t);j||$t(n),vt({type:"CHANGE_ROWS_PER_PAGE",page:n,rowsPerPage:e})}),[mt,$t,j,L,Ot.length]);if(se&&!j&&Rt.length>0&&0===Ot.length){const e=h(Rt.length,dt),t=g(mt,e);$t(t)}Me((()=>{D({allSelected:gt,selectedCount:yt,selectedRows:ht.slice(0)})}),[Et]),Me((()=>{De(ft,bt,Rt.slice(0))}),[ft,bt]),Me((()=>{F(mt,L||Rt.length)}),[mt]),Me((()=>{N(dt,mt)}),[dt]),Me((()=>{$t(H)}),[H,M]),Me((()=>{if(se&&j&&L>0){const e=h(L,dt),t=g(mt,e);mt!==t&&$t(t)}}),[L]),o.useEffect((()=>{vt({type:"CLEAR_SELECTED_ROWS",selectedRowsFlag:qe})}),[f,qe]),o.useEffect((()=>{if(!C)return;const e=Rt.filter((e=>C(e))),t=f?e.slice(0,1):e;vt({type:"SELECT_MULTIPLE_ROWS",keyField:s,selectedRows:t,totalRows:Rt.length,mergeSelections:St})}),[t,C]);const Ht=_?Ot:Rt,Mt=_t||f||v;return o.createElement(l.ThemeProvider,{theme:Pt},!ae&&(!!a||!!r)&&o.createElement(ye,{title:a,actions:r,showMenu:!me,selectedCount:yt,direction:et,contextActions:ge,contextComponent:fe,contextMessage:he}),ce&&o.createElement(ve,{align:ue,wrapContent:pe},de),o.createElement(_e,Object.assign({$responsive:J,$fixedHeader:le,$fixedHeaderScrollHeight:ie,className:nt},kt),o.createElement(Ce,null,K&&!Q&&o.createElement(Se,null,Z),o.createElement(S,{disabled:te,className:"rdt_Table",role:"table"},!ne&&(!!Q||Rt.length>0&&!K)&&o.createElement(x,{className:"rdt_TableHead",role:"rowgroup",$fixedHeader:le},o.createElement(P,{className:"rdt_TableHeadRow",role:"row",$dense:m},y&&(Mt?o.createElement(T,{style:{flex:"0 0 48px"}}):o.createElement(oe,{allSelected:gt,selectedRows:ht,selectableRowsComponent:R,selectableRowsComponentProps:O,selectableRowDisabled:k,rowData:Ht,keyField:s,mergeSelections:St,onSelectAllRows:It})),be&&!Le&&o.createElement(xe,null),at.map((e=>o.createElement(re,{key:e.id,column:e,selectedColumn:ft,disabled:K||0===Rt.length,pagination:se,paginationServer:j,persistSelectedOnSort:wt,selectableRowsVisibleOnly:_,sortDirection:bt,sortIcon:Te,sortServer:Ne,onSort:Tt,onDragStart:rt,onDragOver:ot,onDragEnd:ct,onDragEnter:it,onDragLeave:st,draggingColumnId:lt}))))),!Rt.length&&!K&&o.createElement(Pe,null,ee),K&&Q&&o.createElement(Se,null,Z),!K&&Rt.length>0&&o.createElement(we,{className:"rdt_TableBody",role:"rowgroup"},Ot.map(((e,t)=>{const n=u(e,s),a=function(e=""){return"number"!=typeof e&&(!e||0===e.length)}(n)?t:n,l=b(e,ht,s),r=!!(be&&Ue&&Ue(e)),i=!!(be&&$e&&$e(e));return o.createElement(X,{id:a,key:a,keyField:s,"data-row-id":a,columns:at,row:e,rowCount:Rt.length,rowIndex:t,selectableRows:y,expandableRows:be,expandableIcon:A,highlightOnHover:p,pointerOnHover:d,dense:m,expandOnRowClicked:Be,expandOnRowDoubleClicked:ze,expandableRowsComponent:Fe,expandableRowsComponentProps:je,expandableRowsHideExpander:Le,defaultExpanderDisabled:i,defaultExpanded:r,expandableInheritConditionalStyles:We,conditionalRowStyles:Je,selected:l,selectableRowsHighlight:E,selectableRowsComponent:R,selectableRowsComponentProps:O,selectableRowDisabled:k,selectableRowsSingle:f,striped:c,onRowExpandToggled:I,onRowClicked:At,onRowDoubleClicked:Nt,onRowMouseEnter:Ft,onRowMouseLeave:jt,onSelectedRow:Dt,draggingColumnId:lt,onDragStart:rt,onDragOver:ot,onDragEnd:ct,onDragEnter:it,onDragLeave:st})})))))),Ct&&o.createElement("div",null,o.createElement(xt,{onChangePage:$t,onChangeRowsPerPage:Lt,rowCount:L||Rt.length,currentPage:mt,rowsPerPage:dt,direction:et,paginationRowsPerPageOptions:z,paginationIconLastPage:U,paginationIconFirstPage:W,paginationIconNext:G,paginationIconPrevious:V,paginationComponentOptions:Y})))}));t.Ay=Xe},115:e=>{var t="undefined"!=typeof Element,n="function"==typeof Map,a="function"==typeof Set,l="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function r(e,i){if(e===i)return!0;if(e&&i&&"object"==typeof e&&"object"==typeof i){if(e.constructor!==i.constructor)return!1;var o,s,c,u;if(Array.isArray(e)){if((o=e.length)!=i.length)return!1;for(s=o;0!=s--;)if(!r(e[s],i[s]))return!1;return!0}if(n&&e instanceof Map&&i instanceof Map){if(e.size!==i.size)return!1;for(u=e.entries();!(s=u.next()).done;)if(!i.has(s.value[0]))return!1;for(u=e.entries();!(s=u.next()).done;)if(!r(s.value[1],i.get(s.value[0])))return!1;return!0}if(a&&e instanceof Set&&i instanceof Set){if(e.size!==i.size)return!1;for(u=e.entries();!(s=u.next()).done;)if(!i.has(s.value[0]))return!1;return!0}if(l&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(i)){if((o=e.length)!=i.length)return!1;for(s=o;0!=s--;)if(e[s]!==i[s])return!1;return!0}if(e.constructor===RegExp)return e.source===i.source&&e.flags===i.flags;if(e.valueOf!==Object.prototype.valueOf&&"function"==typeof e.valueOf&&"function"==typeof i.valueOf)return e.valueOf()===i.valueOf();if(e.toString!==Object.prototype.toString&&"function"==typeof e.toString&&"function"==typeof i.toString)return e.toString()===i.toString();if((o=(c=Object.keys(e)).length)!==Object.keys(i).length)return!1;for(s=o;0!=s--;)if(!Object.prototype.hasOwnProperty.call(i,c[s]))return!1;if(t&&e instanceof Element)return!1;for(s=o;0!=s--;)if(("_owner"!==c[s]&&"__v"!==c[s]&&"__o"!==c[s]||!e.$$typeof)&&!r(e[c[s]],i[c[s]]))return!1;return!0}return e!=e&&i!=i}e.exports=function(e,t){try{return r(e,t)}catch(e){if((e.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw e}}},21:(e,t,n)=>{var a,l=Object.create,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,u=(e,t,n,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of o(t))c.call(e,l)||l===n||r(e,l,{get:()=>t[l],enumerable:!(a=i(t,l))||a.enumerable});return e},p=(e,t,n)=>(n=null!=e?l(s(e)):{},u(!t&&e&&e.__esModule?n:r(n,"default",{value:e,enumerable:!0}),e)),d=(e,t,n)=>(((e,t,n)=>{t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n),m={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(m,{default:()=>b}),e.exports=(a=m,u(r({},"__esModule",{value:!0}),a));var h=p(n(609)),g=p(n(115)),y=n(604),f=n(635);class b extends h.Component{constructor(){super(...arguments),d(this,"mounted",!1),d(this,"isReady",!1),d(this,"isPlaying",!1),d(this,"isLoading",!0),d(this,"loadOnReady",null),d(this,"startOnPlay",!0),d(this,"seekOnPlay",null),d(this,"onDurationCalled",!1),d(this,"handlePlayerMount",(e=>{this.player||(this.player=e,this.player.load(this.props.url)),this.progress()})),d(this,"getInternalPlayer",(e=>this.player?this.player[e]:null)),d(this,"progress",(()=>{if(this.props.url&&this.player&&this.isReady){const e=this.getCurrentTime()||0,t=this.getSecondsLoaded(),n=this.getDuration();if(n){const a={playedSeconds:e,played:e/n};null!==t&&(a.loadedSeconds=t,a.loaded=t/n),a.playedSeconds===this.prevPlayed&&a.loadedSeconds===this.prevLoaded||this.props.onProgress(a),this.prevPlayed=a.playedSeconds,this.prevLoaded=a.loadedSeconds}}this.progressTimeout=setTimeout(this.progress,this.props.progressFrequency||this.props.progressInterval)})),d(this,"handleReady",(()=>{if(!this.mounted)return;this.isReady=!0,this.isLoading=!1;const{onReady:e,playing:t,volume:n,muted:a}=this.props;e(),a||null===n||this.player.setVolume(n),this.loadOnReady?(this.player.load(this.loadOnReady,!0),this.loadOnReady=null):t&&this.player.play(),this.handleDurationCheck()})),d(this,"handlePlay",(()=>{this.isPlaying=!0,this.isLoading=!1;const{onStart:e,onPlay:t,playbackRate:n}=this.props;this.startOnPlay&&(this.player.setPlaybackRate&&1!==n&&this.player.setPlaybackRate(n),e(),this.startOnPlay=!1),t(),this.seekOnPlay&&(this.seekTo(this.seekOnPlay),this.seekOnPlay=null),this.handleDurationCheck()})),d(this,"handlePause",(e=>{this.isPlaying=!1,this.isLoading||this.props.onPause(e)})),d(this,"handleEnded",(()=>{const{activePlayer:e,loop:t,onEnded:n}=this.props;e.loopOnEnded&&t&&this.seekTo(0),t||(this.isPlaying=!1,n())})),d(this,"handleError",((...e)=>{this.isLoading=!1,this.props.onError(...e)})),d(this,"handleDurationCheck",(()=>{clearTimeout(this.durationCheckTimeout);const e=this.getDuration();e?this.onDurationCalled||(this.props.onDuration(e),this.onDurationCalled=!0):this.durationCheckTimeout=setTimeout(this.handleDurationCheck,100)})),d(this,"handleLoaded",(()=>{this.isLoading=!1}))}componentDidMount(){this.mounted=!0}componentWillUnmount(){clearTimeout(this.progressTimeout),clearTimeout(this.durationCheckTimeout),this.isReady&&this.props.stopOnUnmount&&(this.player.stop(),this.player.disablePIP&&this.player.disablePIP()),this.mounted=!1}componentDidUpdate(e){if(!this.player)return;const{url:t,playing:n,volume:a,muted:l,playbackRate:r,pip:i,loop:o,activePlayer:s,disableDeferredLoading:c}=this.props;if(!(0,g.default)(e.url,t)){if(this.isLoading&&!s.forceLoad&&!c&&!(0,f.isMediaStream)(t))return console.warn(`ReactPlayer: the attempt to load ${t} is being deferred until the player has loaded`),void(this.loadOnReady=t);this.isLoading=!0,this.startOnPlay=!0,this.onDurationCalled=!1,this.player.load(t,this.isReady)}e.playing||!n||this.isPlaying||this.player.play(),e.playing&&!n&&this.isPlaying&&this.player.pause(),!e.pip&&i&&this.player.enablePIP&&this.player.enablePIP(),e.pip&&!i&&this.player.disablePIP&&this.player.disablePIP(),e.volume!==a&&null!==a&&this.player.setVolume(a),e.muted!==l&&(l?this.player.mute():(this.player.unmute(),null!==a&&setTimeout((()=>this.player.setVolume(a))))),e.playbackRate!==r&&this.player.setPlaybackRate&&this.player.setPlaybackRate(r),e.loop!==o&&this.player.setLoop&&this.player.setLoop(o)}getDuration(){return this.isReady?this.player.getDuration():null}getCurrentTime(){return this.isReady?this.player.getCurrentTime():null}getSecondsLoaded(){return this.isReady?this.player.getSecondsLoaded():null}seekTo(e,t,n){if(this.isReady){if(t?"fraction"===t:e>0&&e<1){const t=this.player.getDuration();return t?void this.player.seekTo(t*e,n):void console.warn("ReactPlayer: could not seek using fraction – duration not yet available")}this.player.seekTo(e,n)}else 0!==e&&(this.seekOnPlay=e,setTimeout((()=>{this.seekOnPlay=null}),5e3))}render(){const e=this.props.activePlayer;return e?h.default.createElement(e,{...this.props,onMount:this.handlePlayerMount,onReady:this.handleReady,onPlay:this.handlePlay,onPause:this.handlePause,onEnded:this.handleEnded,onLoaded:this.handleLoaded,onError:this.handleError}):null}}d(b,"displayName","Player"),d(b,"propTypes",y.propTypes),d(b,"defaultProps",y.defaultProps)},580:(e,t,n)=>{var a,l=Object.create,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,u=(e,t,n,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of o(t))c.call(e,l)||l===n||r(e,l,{get:()=>t[l],enumerable:!(a=i(t,l))||a.enumerable});return e},p=(e,t,n)=>(n=null!=e?l(s(e)):{},u(!t&&e&&e.__esModule?n:r(n,"default",{value:e,enumerable:!0}),e)),d=(e,t,n)=>(((e,t,n)=>{t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n),m={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(m,{createReactPlayer:()=>k}),e.exports=(a=m,u(r({},"__esModule",{value:!0}),a));var h=p(n(609)),g=p(n(744)),y=p(n(811)),f=p(n(115)),b=n(604),E=n(635),v=p(n(21));const w=(0,E.lazy)((()=>n.e(353).then(n.t.bind(n,734,23)))),_="undefined"!=typeof window&&window.document&&"undefined"!=typeof document,S=void 0!==n.g&&n.g.window&&n.g.window.document,C=Object.keys(b.propTypes),x=_||S?h.Suspense:()=>null,P=[],k=(e,t)=>{var n;return n=class extends h.Component{constructor(){super(...arguments),d(this,"state",{showPreview:!!this.props.light}),d(this,"references",{wrapper:e=>{this.wrapper=e},player:e=>{this.player=e}}),d(this,"handleClickPreview",(e=>{this.setState({showPreview:!1}),this.props.onClickPreview(e)})),d(this,"showPreview",(()=>{this.setState({showPreview:!0})})),d(this,"getDuration",(()=>this.player?this.player.getDuration():null)),d(this,"getCurrentTime",(()=>this.player?this.player.getCurrentTime():null)),d(this,"getSecondsLoaded",(()=>this.player?this.player.getSecondsLoaded():null)),d(this,"getInternalPlayer",((e="player")=>this.player?this.player.getInternalPlayer(e):null)),d(this,"seekTo",((e,t,n)=>{if(!this.player)return null;this.player.seekTo(e,t,n)})),d(this,"handleReady",(()=>{this.props.onReady(this)})),d(this,"getActivePlayer",(0,y.default)((n=>{for(const t of[...P,...e])if(t.canPlay(n))return t;return t||null}))),d(this,"getConfig",(0,y.default)(((e,t)=>{const{config:n}=this.props;return g.default.all([b.defaultProps.config,b.defaultProps.config[t]||{},n,n[t]||{}])}))),d(this,"getAttributes",(0,y.default)((e=>(0,E.omit)(this.props,C)))),d(this,"renderActivePlayer",(e=>{if(!e)return null;const t=this.getActivePlayer(e);if(!t)return null;const n=this.getConfig(e,t.key);return h.default.createElement(v.default,{...this.props,key:t.key,ref:this.references.player,config:n,activePlayer:t.lazyPlayer||t,onReady:this.handleReady})}))}shouldComponentUpdate(e,t){return!(0,f.default)(this.props,e)||!(0,f.default)(this.state,t)}componentDidUpdate(e){const{light:t}=this.props;!e.light&&t&&this.setState({showPreview:!0}),e.light&&!t&&this.setState({showPreview:!1})}renderPreview(e){if(!e)return null;const{light:t,playIcon:n,previewTabIndex:a,oEmbedUrl:l,previewAriaLabel:r}=this.props;return h.default.createElement(w,{url:e,light:t,playIcon:n,previewTabIndex:a,previewAriaLabel:r,oEmbedUrl:l,onClick:this.handleClickPreview})}render(){const{url:e,style:t,width:n,height:a,fallback:l,wrapper:r}=this.props,{showPreview:i}=this.state,o=this.getAttributes(e),s="string"==typeof r?this.references.wrapper:void 0;return h.default.createElement(r,{ref:s,style:{...t,width:n,height:a},...o},h.default.createElement(x,{fallback:l},i?this.renderPreview(e):this.renderActivePlayer(e)))}},d(n,"displayName","ReactPlayer"),d(n,"propTypes",b.propTypes),d(n,"defaultProps",b.defaultProps),d(n,"addCustomPlayer",(e=>{P.push(e)})),d(n,"removeCustomPlayers",(()=>{P.length=0})),d(n,"canPlay",(t=>{for(const n of[...P,...e])if(n.canPlay(t))return!0;return!1})),d(n,"canEnablePIP",(t=>{for(const n of[...P,...e])if(n.canEnablePIP&&n.canEnablePIP(t))return!0;return!1})),n}},554:(e,t,n)=>{var a,l=Object.create,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,u=(e,t,n,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of o(t))c.call(e,l)||l===n||r(e,l,{get:()=>t[l],enumerable:!(a=i(t,l))||a.enumerable});return e},p={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(p,{default:()=>g}),e.exports=(a=p,u(r({},"__esModule",{value:!0}),a));var d=((e,t,n)=>(n=null!=e?l(s(e)):{},u(e&&e.__esModule?n:r(n,"default",{value:e,enumerable:!0}),e)))(n(15)),m=n(580);const h=d.default[d.default.length-1];var g=(0,m.createReactPlayer)(d.default,h)},327:(e,t,n)=>{var a,l=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,s={};((e,t)=>{for(var n in t)l(e,n,{get:t[n],enumerable:!0})})(s,{AUDIO_EXTENSIONS:()=>C,DASH_EXTENSIONS:()=>k,FLV_EXTENSIONS:()=>R,HLS_EXTENSIONS:()=>P,MATCH_URL_DAILYMOTION:()=>v,MATCH_URL_FACEBOOK:()=>h,MATCH_URL_FACEBOOK_WATCH:()=>g,MATCH_URL_KALTURA:()=>S,MATCH_URL_MIXCLOUD:()=>w,MATCH_URL_MUX:()=>m,MATCH_URL_SOUNDCLOUD:()=>p,MATCH_URL_STREAMABLE:()=>y,MATCH_URL_TWITCH_CHANNEL:()=>E,MATCH_URL_TWITCH_VIDEO:()=>b,MATCH_URL_VIDYARD:()=>_,MATCH_URL_VIMEO:()=>d,MATCH_URL_WISTIA:()=>f,MATCH_URL_YOUTUBE:()=>u,VIDEO_EXTENSIONS:()=>x,canPlay:()=>T}),e.exports=(a=s,((e,t,n,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of i(t))o.call(e,n)||undefined===n||l(e,n,{get:()=>t[n],enumerable:!(a=r(t,n))||a.enumerable});return e})(l({},"__esModule",{value:!0}),a));var c=n(635);const u=/(?:youtu\.be\/|youtube(?:-nocookie|education)?\.com\/(?:embed\/|v\/|watch\/|watch\?v=|watch\?.+&v=|shorts\/|live\/))((\w|-){11})|youtube\.com\/playlist\?list=|youtube\.com\/user\//,p=/(?:soundcloud\.com|snd\.sc)\/[^.]+$/,d=/vimeo\.com\/(?!progressive_redirect).+/,m=/stream\.mux\.com\/(?!\w+\.m3u8)(\w+)/,h=/^https?:\/\/(www\.)?facebook\.com.*\/(video(s)?|watch|story)(\.php?|\/).+$/,g=/^https?:\/\/fb\.watch\/.+$/,y=/streamable\.com\/([a-z0-9]+)$/,f=/(?:wistia\.(?:com|net)|wi\.st)\/(?:medias|embed)\/(?:iframe\/)?([^?]+)/,b=/(?:www\.|go\.)?twitch\.tv\/videos\/(\d+)($|\?)/,E=/(?:www\.|go\.)?twitch\.tv\/([a-zA-Z0-9_]+)($|\?)/,v=/^(?:(?:https?):)?(?:\/\/)?(?:www\.)?(?:(?:dailymotion\.com(?:\/embed)?\/video)|dai\.ly)\/([a-zA-Z0-9]+)(?:_[\w_-]+)?(?:[\w.#_-]+)?/,w=/mixcloud\.com\/([^/]+\/[^/]+)/,_=/vidyard.com\/(?:watch\/)?([a-zA-Z0-9-_]+)/,S=/^https?:\/\/[a-zA-Z]+\.kaltura.(com|org)\/p\/([0-9]+)\/sp\/([0-9]+)00\/embedIframeJs\/uiconf_id\/([0-9]+)\/partner_id\/([0-9]+)(.*)entry_id.([a-zA-Z0-9-_].*)$/,C=/\.(m4a|m4b|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\?)/i,x=/\.(mp4|og[gv]|webm|mov|m4v)(#t=[,\d+]+)?($|\?)/i,P=/\.(m3u8)($|\?)/i,k=/\.(mpd)($|\?)/i,R=/\.(flv)($|\?)/i,O=e=>{if(e instanceof Array){for(const t of e){if("string"==typeof t&&O(t))return!0;if(O(t.src))return!0}return!1}return!(!(0,c.isMediaStream)(e)&&!(0,c.isBlobUrl)(e))||C.test(e)||x.test(e)||P.test(e)||k.test(e)||R.test(e)},T={youtube:e=>e instanceof Array?e.every((e=>u.test(e))):u.test(e),soundcloud:e=>p.test(e)&&!C.test(e),vimeo:e=>d.test(e)&&!x.test(e)&&!P.test(e),mux:e=>m.test(e),facebook:e=>h.test(e)||g.test(e),streamable:e=>y.test(e),wistia:e=>f.test(e),twitch:e=>b.test(e)||E.test(e),dailymotion:e=>v.test(e),mixcloud:e=>w.test(e),vidyard:e=>_.test(e),kaltura:e=>S.test(e),file:O}},15:(e,t,n)=>{Object.create;var a,l=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,o=(Object.getPrototypeOf,Object.prototype.hasOwnProperty),s={};((e,t)=>{for(var n in t)l(e,n,{get:t[n],enumerable:!0})})(s,{default:()=>p}),e.exports=(a=s,((e,t,n,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of i(t))o.call(e,s)||s===n||l(e,s,{get:()=>t[s],enumerable:!(a=r(t,s))||a.enumerable});return e})(l({},"__esModule",{value:!0}),a));var c=n(635),u=n(327),p=[{key:"youtube",name:"YouTube",canPlay:u.canPlay.youtube,lazyPlayer:(0,c.lazy)((()=>n.e(446).then(n.t.bind(n,910,23))))},{key:"soundcloud",name:"SoundCloud",canPlay:u.canPlay.soundcloud,lazyPlayer:(0,c.lazy)((()=>n.e(979).then(n.t.bind(n,127,23))))},{key:"vimeo",name:"Vimeo",canPlay:u.canPlay.vimeo,lazyPlayer:(0,c.lazy)((()=>n.e(173).then(n.t.bind(n,423,23))))},{key:"mux",name:"Mux",canPlay:u.canPlay.mux,lazyPlayer:(0,c.lazy)((()=>n.e(723).then(n.t.bind(n,553,23))))},{key:"facebook",name:"Facebook",canPlay:u.canPlay.facebook,lazyPlayer:(0,c.lazy)((()=>n.e(887).then(n.t.bind(n,343,23))))},{key:"streamable",name:"Streamable",canPlay:u.canPlay.streamable,lazyPlayer:(0,c.lazy)((()=>n.e(627).then(n.t.bind(n,643,23))))},{key:"wistia",name:"Wistia",canPlay:u.canPlay.wistia,lazyPlayer:(0,c.lazy)((()=>n.e(340).then(n.t.bind(n,330,23))))},{key:"twitch",name:"Twitch",canPlay:u.canPlay.twitch,lazyPlayer:(0,c.lazy)((()=>n.e(42).then(n.t.bind(n,400,23))))},{key:"dailymotion",name:"DailyMotion",canPlay:u.canPlay.dailymotion,lazyPlayer:(0,c.lazy)((()=>n.e(328).then(n.t.bind(n,348,23))))},{key:"mixcloud",name:"Mixcloud",canPlay:u.canPlay.mixcloud,lazyPlayer:(0,c.lazy)((()=>n.e(570).then(n.t.bind(n,276,23))))},{key:"vidyard",name:"Vidyard",canPlay:u.canPlay.vidyard,lazyPlayer:(0,c.lazy)((()=>n.e(392).then(n.t.bind(n,552,23))))},{key:"kaltura",name:"Kaltura",canPlay:u.canPlay.kaltura,lazyPlayer:(0,c.lazy)((()=>n.e(463).then(n.t.bind(n,945,23))))},{key:"file",name:"FilePlayer",canPlay:u.canPlay.file,canEnablePIP:e=>u.canPlay.file(e)&&(document.pictureInPictureEnabled||(0,c.supportsWebKitPresentationMode)())&&!u.AUDIO_EXTENSIONS.test(e),lazyPlayer:(0,c.lazy)((()=>n.e(458).then(n.t.bind(n,688,23))))}]},604:(e,t,n)=>{var a,l=Object.create,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,u=(e,t,n,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of o(t))c.call(e,l)||l===n||r(e,l,{get:()=>t[l],enumerable:!(a=i(t,l))||a.enumerable});return e},p={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(p,{defaultProps:()=>C,propTypes:()=>_}),e.exports=(a=p,u(r({},"__esModule",{value:!0}),a));var d=((e,t,n)=>(n=null!=e?l(s(e)):{},u(e&&e.__esModule?n:r(n,"default",{value:e,enumerable:!0}),e)))(n(556));const{string:m,bool:h,number:g,array:y,oneOfType:f,shape:b,object:E,func:v,node:w}=d.default,_={url:f([m,y,E]),playing:h,loop:h,controls:h,volume:g,muted:h,playbackRate:g,width:f([m,g]),height:f([m,g]),style:E,progressInterval:g,playsinline:h,pip:h,stopOnUnmount:h,light:f([h,m,E]),playIcon:w,previewTabIndex:g,previewAriaLabel:m,fallback:w,oEmbedUrl:m,wrapper:f([m,v,b({render:v.isRequired})]),config:b({soundcloud:b({options:E}),youtube:b({playerVars:E,embedOptions:E,onUnstarted:v}),facebook:b({appId:m,version:m,playerId:m,attributes:E}),dailymotion:b({params:E}),vimeo:b({playerOptions:E,title:m}),mux:b({attributes:E,version:m}),file:b({attributes:E,tracks:y,forceVideo:h,forceAudio:h,forceHLS:h,forceSafariHLS:h,forceDisableHls:h,forceDASH:h,forceFLV:h,hlsOptions:E,hlsVersion:m,dashVersion:m,flvVersion:m}),wistia:b({options:E,playerId:m,customControls:y}),mixcloud:b({options:E}),twitch:b({options:E,playerId:m}),vidyard:b({options:E})}),onReady:v,onStart:v,onPlay:v,onPause:v,onBuffer:v,onBufferEnd:v,onEnded:v,onError:v,onDuration:v,onSeek:v,onPlaybackRateChange:v,onPlaybackQualityChange:v,onProgress:v,onClickPreview:v,onEnablePIP:v,onDisablePIP:v},S=()=>{},C={playing:!1,loop:!1,controls:!1,volume:null,muted:!1,playbackRate:1,width:"640px",height:"360px",style:{},progressInterval:1e3,playsinline:!1,pip:!1,stopOnUnmount:!0,light:!1,fallback:null,wrapper:"div",previewTabIndex:0,previewAriaLabel:"",oEmbedUrl:"https://noembed.com/embed?url={url}",config:{soundcloud:{options:{visual:!0,buying:!1,liking:!1,download:!1,sharing:!1,show_comments:!1,show_playcount:!1}},youtube:{playerVars:{playsinline:1,showinfo:0,rel:0,iv_load_policy:3,modestbranding:1},embedOptions:{},onUnstarted:S},facebook:{appId:"1309697205772819",version:"v3.3",playerId:null,attributes:{}},dailymotion:{params:{api:1,"endscreen-enable":!1}},vimeo:{playerOptions:{autopause:!1,byline:!1,portrait:!1,title:!1},title:null},mux:{attributes:{},version:"2"},file:{attributes:{},tracks:[],forceVideo:!1,forceAudio:!1,forceHLS:!1,forceDASH:!1,forceFLV:!1,hlsOptions:{},hlsVersion:"1.1.4",dashVersion:"3.1.3",flvVersion:"1.5.0",forceDisableHls:!1},wistia:{options:{},playerId:null,customControls:null},mixcloud:{options:{hide_cover:1}},twitch:{options:{},playerId:null},vidyard:{options:{}}},onReady:S,onStart:S,onPlay:S,onPause:S,onBuffer:S,onBufferEnd:S,onEnded:S,onError:S,onDuration:S,onSeek:S,onPlaybackRateChange:S,onPlaybackQualityChange:S,onProgress:S,onClickPreview:S,onEnablePIP:S,onDisablePIP:S}},635:(e,t,n)=>{var a,l=Object.create,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,u=(e,t,n,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of o(t))c.call(e,l)||l===n||r(e,l,{get:()=>t[l],enumerable:!(a=i(t,l))||a.enumerable});return e},p=(e,t,n)=>(n=null!=e?l(s(e)):{},u(!t&&e&&e.__esModule?n:r(n,"default",{value:e,enumerable:!0}),e)),d={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(d,{callPlayer:()=>I,getConfig:()=>O,getSDK:()=>R,isBlobUrl:()=>A,isMediaStream:()=>D,lazy:()=>y,omit:()=>T,parseEndTime:()=>S,parseStartTime:()=>_,queryString:()=>x,randomString:()=>C,supportsWebKitPresentationMode:()=>N}),e.exports=(a=d,u(r({},"__esModule",{value:!0}),a));var m=p(n(609)),h=p(n(147)),g=p(n(744));const y=e=>m.default.lazy((async()=>{const t=await e();return"function"==typeof t.default?t:t.default})),f=/[?&#](?:start|t)=([0-9hms]+)/,b=/[?&#]end=([0-9hms]+)/,E=/(\d+)(h|m|s)/g,v=/^\d+$/;function w(e,t){if(e instanceof Array)return;const n=e.match(t);if(n){const e=n[1];if(e.match(E))return function(e){let t=0,n=E.exec(e);for(;null!==n;){const[,a,l]=n;"h"===l&&(t+=60*parseInt(a,10)*60),"m"===l&&(t+=60*parseInt(a,10)),"s"===l&&(t+=parseInt(a,10)),n=E.exec(e)}return t}(e);if(v.test(e))return parseInt(e)}}function _(e){return w(e,f)}function S(e){return w(e,b)}function C(){return Math.random().toString(36).substr(2,5)}function x(e){return Object.keys(e).map((t=>`${t}=${e[t]}`)).join("&")}function P(e){return window[e]?window[e]:window.exports&&window.exports[e]?window.exports[e]:window.module&&window.module.exports&&window.module.exports[e]?window.module.exports[e]:null}const k={},R=function(e,t,n=null,a=()=>!0,l=h.default){const r=P(t);return r&&a(r)?Promise.resolve(r):new Promise(((a,r)=>{if(k[e])return void k[e].push({resolve:a,reject:r});k[e]=[{resolve:a,reject:r}];const i=t=>{k[e].forEach((e=>e.resolve(t)))};if(n){const e=window[n];window[n]=function(){e&&e(),i(P(t))}}l(e,(a=>{a?(k[e].forEach((e=>e.reject(a))),k[e]=null):n||i(P(t))}))}))};function O(e,t){return(0,g.default)(t.config,e.config)}function T(e,...t){const n=[].concat(...t),a={},l=Object.keys(e);for(const t of l)-1===n.indexOf(t)&&(a[t]=e[t]);return a}function I(e,...t){if(!this.player||!this.player[e]){let t=`ReactPlayer: ${this.constructor.displayName} player could not call %c${e}%c – `;return this.player?this.player[e]||(t+="The method was not available"):t+="The player was not available",console.warn(t,"font-weight: bold",""),null}return this.player[e](...t)}function D(e){return"undefined"!=typeof window&&void 0!==window.MediaStream&&e instanceof window.MediaStream}function A(e){return/^blob:/.test(e)}function N(e=document.createElement("video")){const t=!1===/iPhone|iPod/.test(navigator.userAgent);return e.webkitSupportsPresentationMode&&"function"==typeof e.webkitSetPresentationMode&&t}},833:e=>{e.exports=function(e,t,n,a){var l=n?n.call(a,e,t):void 0;if(void 0!==l)return!!l;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var r=Object.keys(e),i=Object.keys(t);if(r.length!==i.length)return!1;for(var o=Object.prototype.hasOwnProperty.bind(t),s=0;s<r.length;s++){var c=r[s];if(!o(c))return!1;var u=e[c],p=t[c];if(!1===(l=n?n.call(a,u,p,c):void 0)||void 0===l&&u!==p)return!1}return!0}},510:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ServerStyleSheet:()=>ln,StyleSheetConsumer:()=>Ot,StyleSheetContext:()=>Rt,StyleSheetManager:()=>Dt,ThemeConsumer:()=>Ut,ThemeContext:()=>zt,ThemeProvider:()=>Gt,__PRIVATE__:()=>rn,createGlobalStyle:()=>tn,css:()=>Kt,default:()=>Qt,isStyledComponent:()=>Ke,keyframes:()=>nn,styled:()=>Qt,useTheme:()=>Wt,version:()=>pe,withTheme:()=>an});var a=function(){return a=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var l in t=arguments[n])Object.prototype.hasOwnProperty.call(t,l)&&(e[l]=t[l]);return e},a.apply(this,arguments)};function l(e,t,n){if(n||2===arguments.length)for(var a,l=0,r=t.length;l<r;l++)!a&&l in t||(a||(a=Array.prototype.slice.call(t,0,l)),a[l]=t[l]);return e.concat(a||Array.prototype.slice.call(t))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var r=n(609),i=n.n(r),o=n(833),s=n.n(o),c="-ms-",u="-moz-",p="-webkit-",d="comm",m="rule",h="decl",g="@import",y="@keyframes",f="@layer",b=Math.abs,E=String.fromCharCode,v=Object.assign;function w(e){return e.trim()}function _(e,t){return(e=t.exec(e))?e[0]:e}function S(e,t,n){return e.replace(t,n)}function C(e,t,n){return e.indexOf(t,n)}function x(e,t){return 0|e.charCodeAt(t)}function P(e,t,n){return e.slice(t,n)}function k(e){return e.length}function R(e){return e.length}function O(e,t){return t.push(e),e}function T(e,t){return e.filter((function(e){return!_(e,t)}))}var I=1,D=1,A=0,N=0,F=0,j="";function $(e,t,n,a,l,r,i,o){return{value:e,root:t,parent:n,type:a,props:l,children:r,line:I,column:D,length:i,return:"",siblings:o}}function L(e,t){return v($("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function H(e){for(;e.root;)e=L(e.root,{children:[e]});O(e,e.siblings)}function M(){return F=N>0?x(j,--N):0,D--,10===F&&(D=1,I--),F}function B(){return F=N<A?x(j,N++):0,D++,10===F&&(D=1,I++),F}function z(){return x(j,N)}function U(){return N}function W(e,t){return P(j,e,t)}function G(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function V(e){return w(W(N-1,J(91===e?e+2:40===e?e+1:e)))}function q(e){for(;(F=z())&&F<33;)B();return G(e)>2||G(F)>3?"":" "}function Y(e,t){for(;--t&&B()&&!(F<48||F>102||F>57&&F<65||F>70&&F<97););return W(e,U()+(t<6&&32==z()&&32==B()))}function J(e){for(;B();)switch(F){case e:return N;case 34:case 39:34!==e&&39!==e&&J(F);break;case 40:41===e&&J(e);break;case 92:B()}return N}function K(e,t){for(;B()&&e+F!==57&&(e+F!==84||47!==z()););return"/*"+W(t,N-1)+"*"+E(47===e?e:B())}function Z(e){for(;!G(z());)B();return W(e,N)}function X(e,t){for(var n="",a=0;a<e.length;a++)n+=t(e[a],a,e,t)||"";return n}function Q(e,t,n,a){switch(e.type){case f:if(e.children.length)break;case g:case h:return e.return=e.return||e.value;case d:return"";case y:return e.return=e.value+"{"+X(e.children,a)+"}";case m:if(!k(e.value=e.props.join(",")))return""}return k(n=X(e.children,a))?e.return=e.value+"{"+n+"}":""}function ee(e,t,n){switch(function(e,t){return 45^x(e,0)?(((t<<2^x(e,0))<<2^x(e,1))<<2^x(e,2))<<2^x(e,3):0}(e,t)){case 5103:return p+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return p+e+e;case 4789:return u+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return p+e+u+e+c+e+e;case 5936:switch(x(e,t+11)){case 114:return p+e+c+S(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return p+e+c+S(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return p+e+c+S(e,/[svh]\w+-[tblr]{2}/,"lr")+e}case 6828:case 4268:case 2903:return p+e+c+e+e;case 6165:return p+e+c+"flex-"+e+e;case 5187:return p+e+S(e,/(\w+).+(:[^]+)/,p+"box-$1$2"+c+"flex-$1$2")+e;case 5443:return p+e+c+"flex-item-"+S(e,/flex-|-self/g,"")+(_(e,/flex-|baseline/)?"":c+"grid-row-"+S(e,/flex-|-self/g,""))+e;case 4675:return p+e+c+"flex-line-pack"+S(e,/align-content|flex-|-self/g,"")+e;case 5548:return p+e+c+S(e,"shrink","negative")+e;case 5292:return p+e+c+S(e,"basis","preferred-size")+e;case 6060:return p+"box-"+S(e,"-grow","")+p+e+c+S(e,"grow","positive")+e;case 4554:return p+S(e,/([^-])(transform)/g,"$1"+p+"$2")+e;case 6187:return S(S(S(e,/(zoom-|grab)/,p+"$1"),/(image-set)/,p+"$1"),e,"")+e;case 5495:case 3959:return S(e,/(image-set\([^]*)/,p+"$1$`$1");case 4968:return S(S(e,/(.+:)(flex-)?(.*)/,p+"box-pack:$3"+c+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+p+e+e;case 4200:if(!_(e,/flex-|baseline/))return c+"grid-column-align"+P(e,t)+e;break;case 2592:case 3360:return c+S(e,"template-","")+e;case 4384:case 3616:return n&&n.some((function(e,n){return t=n,_(e.props,/grid-\w+-end/)}))?~C(e+(n=n[t].value),"span",0)?e:c+S(e,"-start","")+e+c+"grid-row-span:"+(~C(n,"span",0)?_(n,/\d+/):+_(n,/\d+/)-+_(e,/\d+/))+";":c+S(e,"-start","")+e;case 4896:case 4128:return n&&n.some((function(e){return _(e.props,/grid-\w+-start/)}))?e:c+S(S(e,"-end","-span"),"span ","")+e;case 4095:case 3583:case 4068:case 2532:return S(e,/(.+)-inline(.+)/,p+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(k(e)-1-t>6)switch(x(e,t+1)){case 109:if(45!==x(e,t+4))break;case 102:return S(e,/(.+:)(.+)-([^]+)/,"$1"+p+"$2-$3$1"+u+(108==x(e,t+3)?"$3":"$2-$3"))+e;case 115:return~C(e,"stretch",0)?ee(S(e,"stretch","fill-available"),t,n)+e:e}break;case 5152:case 5920:return S(e,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,(function(t,n,a,l,r,i,o){return c+n+":"+a+o+(l?c+n+"-span:"+(r?i:+i-+a)+o:"")+e}));case 4949:if(121===x(e,t+6))return S(e,":",":"+p)+e;break;case 6444:switch(x(e,45===x(e,14)?18:11)){case 120:return S(e,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+p+(45===x(e,14)?"inline-":"")+"box$3$1"+p+"$2$3$1"+c+"$2box$3")+e;case 100:return S(e,":",":"+c)+e}break;case 5719:case 2647:case 2135:case 3927:case 2391:return S(e,"scroll-","scroll-snap-")+e}return e}function te(e,t,n,a){if(e.length>-1&&!e.return)switch(e.type){case h:return void(e.return=ee(e.value,e.length,n));case y:return X([L(e,{value:S(e.value,"@","@"+p)})],a);case m:if(e.length)return function(e,t){return e.map(t).join("")}(n=e.props,(function(t){switch(_(t,a=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":H(L(e,{props:[S(t,/:(read-\w+)/,":"+u+"$1")]})),H(L(e,{props:[t]})),v(e,{props:T(n,a)});break;case"::placeholder":H(L(e,{props:[S(t,/:(plac\w+)/,":"+p+"input-$1")]})),H(L(e,{props:[S(t,/:(plac\w+)/,":"+u+"$1")]})),H(L(e,{props:[S(t,/:(plac\w+)/,c+"input-$1")]})),H(L(e,{props:[t]})),v(e,{props:T(n,a)})}return""}))}}function ne(e){return function(e){return j="",e}(ae("",null,null,null,[""],e=function(e){return I=D=1,A=k(j=e),N=0,[]}(e),0,[0],e))}function ae(e,t,n,a,l,r,i,o,s){for(var c=0,u=0,p=i,d=0,m=0,h=0,g=1,y=1,f=1,v=0,w="",_=l,P=r,R=a,T=w;y;)switch(h=v,v=B()){case 40:if(108!=h&&58==x(T,p-1)){-1!=C(T+=S(V(v),"&","&\f"),"&\f",b(c?o[c-1]:0))&&(f=-1);break}case 34:case 39:case 91:T+=V(v);break;case 9:case 10:case 13:case 32:T+=q(h);break;case 92:T+=Y(U()-1,7);continue;case 47:switch(z()){case 42:case 47:O(re(K(B(),U()),t,n,s),s);break;default:T+="/"}break;case 123*g:o[c++]=k(T)*f;case 125*g:case 59:case 0:switch(v){case 0:case 125:y=0;case 59+u:-1==f&&(T=S(T,/\f/g,"")),m>0&&k(T)-p&&O(m>32?ie(T+";",a,n,p-1,s):ie(S(T," ","")+";",a,n,p-2,s),s);break;case 59:T+=";";default:if(O(R=le(T,t,n,c,u,l,o,w,_=[],P=[],p,r),r),123===v)if(0===u)ae(T,t,R,R,_,r,p,o,P);else switch(99===d&&110===x(T,3)?100:d){case 100:case 108:case 109:case 115:ae(e,R,R,a&&O(le(e,R,R,0,0,l,o,w,l,_=[],p,P),P),l,P,p,o,a?_:P);break;default:ae(T,R,R,R,[""],P,0,o,P)}}c=u=m=0,g=f=1,w=T="",p=i;break;case 58:p=1+k(T),m=h;default:if(g<1)if(123==v)--g;else if(125==v&&0==g++&&125==M())continue;switch(T+=E(v),v*g){case 38:f=u>0?1:(T+="\f",-1);break;case 44:o[c++]=(k(T)-1)*f,f=1;break;case 64:45===z()&&(T+=V(B())),d=z(),u=p=k(w=T+=Z(U())),v++;break;case 45:45===h&&2==k(T)&&(g=0)}}return r}function le(e,t,n,a,l,r,i,o,s,c,u,p){for(var d=l-1,h=0===l?r:[""],g=R(h),y=0,f=0,E=0;y<a;++y)for(var v=0,_=P(e,d+1,d=b(f=i[y])),C=e;v<g;++v)(C=w(f>0?h[v]+" "+_:S(_,/&\f/g,h[v])))&&(s[E++]=C);return $(e,t,n,0===l?m:o,s,c,u,p)}function re(e,t,n,a){return $(e,t,n,d,E(F),P(e,2,-2),0,a)}function ie(e,t,n,a,l){return $(e,t,n,h,P(e,0,a),P(e,a+1,-1),a,l)}var oe={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},se="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",ce="active",ue="data-styled-version",pe="6.1.12",de="/*!sc*/\n",me="undefined"!=typeof window&&"HTMLElement"in window,he=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&"false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY),ge={},ye=(new Set,Object.freeze([])),fe=Object.freeze({});function be(e,t,n){return void 0===n&&(n=fe),e.theme!==n.theme&&e.theme||t||n.theme}var Ee=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),ve=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,we=/(^-|-$)/g;function _e(e){return e.replace(ve,"-").replace(we,"")}var Se=/(a)(d)/gi,Ce=52,xe=function(e){return String.fromCharCode(e+(e>25?39:97))};function Pe(e){var t,n="";for(t=Math.abs(e);t>Ce;t=t/Ce|0)n=xe(t%Ce)+n;return(xe(t%Ce)+n).replace(Se,"$1-$2")}var ke,Re=5381,Oe=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},Te=function(e){return Oe(Re,e)};function Ie(e){return Pe(Te(e)>>>0)}function De(e){return e.displayName||e.name||"Component"}function Ae(e){return"string"==typeof e&&!0}var Ne="function"==typeof Symbol&&Symbol.for,Fe=Ne?Symbol.for("react.memo"):60115,je=Ne?Symbol.for("react.forward_ref"):60112,$e={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Le={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},He={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Me=((ke={})[je]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},ke[Fe]=He,ke);function Be(e){return("type"in(t=e)&&t.type.$$typeof)===Fe?He:"$$typeof"in e?Me[e.$$typeof]:$e;var t}var ze=Object.defineProperty,Ue=Object.getOwnPropertyNames,We=Object.getOwnPropertySymbols,Ge=Object.getOwnPropertyDescriptor,Ve=Object.getPrototypeOf,qe=Object.prototype;function Ye(e,t,n){if("string"!=typeof t){if(qe){var a=Ve(t);a&&a!==qe&&Ye(e,a,n)}var l=Ue(t);We&&(l=l.concat(We(t)));for(var r=Be(e),i=Be(t),o=0;o<l.length;++o){var s=l[o];if(!(s in Le||n&&n[s]||i&&s in i||r&&s in r)){var c=Ge(t,s);try{ze(e,s,c)}catch(e){}}}}return e}function Je(e){return"function"==typeof e}function Ke(e){return"object"==typeof e&&"styledComponentId"in e}function Ze(e,t){return e&&t?"".concat(e," ").concat(t):e||t||""}function Xe(e,t){if(0===e.length)return"";for(var n=e[0],a=1;a<e.length;a++)n+=t?t+e[a]:e[a];return n}function Qe(e){return null!==e&&"object"==typeof e&&e.constructor.name===Object.name&&!("props"in e&&e.$$typeof)}function et(e,t,n){if(void 0===n&&(n=!1),!n&&!Qe(e)&&!Array.isArray(e))return t;if(Array.isArray(t))for(var a=0;a<t.length;a++)e[a]=et(e[a],t[a]);else if(Qe(t))for(var a in t)e[a]=et(e[a],t[a]);return e}function tt(e,t){Object.defineProperty(e,"toString",{value:t})}function nt(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(e," for more information.").concat(t.length>0?" Args: ".concat(t.join(", ")):""))}var at=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}return e.prototype.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},e.prototype.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,a=n.length,l=a;e>=l;)if((l<<=1)<0)throw nt(16,"".concat(e));this.groupSizes=new Uint32Array(l),this.groupSizes.set(n),this.length=l;for(var r=a;r<l;r++)this.groupSizes[r]=0}for(var i=this.indexOfGroup(e+1),o=(r=0,t.length);r<o;r++)this.tag.insertRule(i,t[r])&&(this.groupSizes[e]++,i++)},e.prototype.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),a=n+t;this.groupSizes[e]=0;for(var l=n;l<a;l++)this.tag.deleteRule(n)}},e.prototype.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],a=this.indexOfGroup(e),l=a+n,r=a;r<l;r++)t+="".concat(this.tag.getRule(r)).concat(de);return t},e}(),lt=new Map,rt=new Map,it=1,ot=function(e){if(lt.has(e))return lt.get(e);for(;rt.has(it);)it++;var t=it++;return lt.set(e,t),rt.set(t,e),t},st=function(e,t){it=t+1,lt.set(e,t),rt.set(t,e)},ct="style[".concat(se,"][").concat(ue,'="').concat(pe,'"]'),ut=new RegExp("^".concat(se,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),pt=function(e,t,n){for(var a,l=n.split(","),r=0,i=l.length;r<i;r++)(a=l[r])&&e.registerName(t,a)},dt=function(e,t){for(var n,a=(null!==(n=t.textContent)&&void 0!==n?n:"").split(de),l=[],r=0,i=a.length;r<i;r++){var o=a[r].trim();if(o){var s=o.match(ut);if(s){var c=0|parseInt(s[1],10),u=s[2];0!==c&&(st(u,c),pt(e,u,s[3]),e.getTag().insertRules(c,l)),l.length=0}else l.push(o)}}},mt=function(e){for(var t=document.querySelectorAll(ct),n=0,a=t.length;n<a;n++){var l=t[n];l&&l.getAttribute(se)!==ce&&(dt(e,l),l.parentNode&&l.parentNode.removeChild(l))}};function ht(){return n.nc}var gt=function(e){var t=document.head,n=e||t,a=document.createElement("style"),l=function(e){var t=Array.from(e.querySelectorAll("style[".concat(se,"]")));return t[t.length-1]}(n),r=void 0!==l?l.nextSibling:null;a.setAttribute(se,ce),a.setAttribute(ue,pe);var i=ht();return i&&a.setAttribute("nonce",i),n.insertBefore(a,r),a},yt=function(){function e(e){this.element=gt(e),this.element.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,a=t.length;n<a;n++){var l=t[n];if(l.ownerNode===e)return l}throw nt(17)}(this.element),this.length=0}return e.prototype.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},e.prototype.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},e.prototype.getRule=function(e){var t=this.sheet.cssRules[e];return t&&t.cssText?t.cssText:""},e}(),ft=function(){function e(e){this.element=gt(e),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t);return this.element.insertBefore(n,this.nodes[e]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},e.prototype.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),bt=function(){function e(e){this.rules=[],this.length=0}return e.prototype.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},e.prototype.deleteRule=function(e){this.rules.splice(e,1),this.length--},e.prototype.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),Et=me,vt={isServer:!me,useCSSOMInjection:!he},wt=function(){function e(e,t,n){void 0===e&&(e=fe),void 0===t&&(t={});var l=this;this.options=a(a({},vt),e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&me&&Et&&(Et=!1,mt(this)),tt(this,(function(){return function(e){for(var t=e.getTag(),n=t.length,a="",l=function(n){var l=function(e){return rt.get(e)}(n);if(void 0===l)return"continue";var r=e.names.get(l),i=t.getGroup(n);if(void 0===r||!r.size||0===i.length)return"continue";var o="".concat(se,".g").concat(n,'[id="').concat(l,'"]'),s="";void 0!==r&&r.forEach((function(e){e.length>0&&(s+="".concat(e,","))})),a+="".concat(i).concat(o,'{content:"').concat(s,'"}').concat(de)},r=0;r<n;r++)l(r);return a}(l)}))}return e.registerId=function(e){return ot(e)},e.prototype.rehydrate=function(){!this.server&&me&&mt(this)},e.prototype.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(a(a({},this.options),t),this.gs,n&&this.names||void 0)},e.prototype.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(e=function(e){var t=e.useCSSOMInjection,n=e.target;return e.isServer?new bt(n):t?new yt(n):new ft(n)}(this.options),new at(e)));var e},e.prototype.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},e.prototype.registerName=function(e,t){if(ot(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},e.prototype.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(ot(e),n)},e.prototype.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},e.prototype.clearRules=function(e){this.getTag().clearGroup(ot(e)),this.clearNames(e)},e.prototype.clearTag=function(){this.tag=void 0},e}(),_t=/&/g,St=/^\s*\/\/.*$/gm;function Ct(e,t){return e.map((function(e){return"rule"===e.type&&(e.value="".concat(t," ").concat(e.value),e.value=e.value.replaceAll(",",",".concat(t," ")),e.props=e.props.map((function(e){return"".concat(t," ").concat(e)}))),Array.isArray(e.children)&&"@keyframes"!==e.type&&(e.children=Ct(e.children,t)),e}))}function xt(e){var t,n,a,l=void 0===e?fe:e,r=l.options,i=void 0===r?fe:r,o=l.plugins,s=void 0===o?ye:o,c=function(e,a,l){return l.startsWith(n)&&l.endsWith(n)&&l.replaceAll(n,"").length>0?".".concat(t):e},u=s.slice();u.push((function(e){e.type===m&&e.value.includes("&")&&(e.props[0]=e.props[0].replace(_t,n).replace(a,c))})),i.prefix&&u.push(te),u.push(Q);var p=function(e,l,r,o){void 0===l&&(l=""),void 0===r&&(r=""),void 0===o&&(o="&"),t=o,n=l,a=new RegExp("\\".concat(n,"\\b"),"g");var s=e.replace(St,""),c=ne(r||l?"".concat(r," ").concat(l," { ").concat(s," }"):s);i.namespace&&(c=Ct(c,i.namespace));var p,d,m,h=[];return X(c,(p=u.concat((m=function(e){return h.push(e)},function(e){e.root||(e=e.return)&&m(e)})),d=R(p),function(e,t,n,a){for(var l="",r=0;r<d;r++)l+=p[r](e,t,n,a)||"";return l})),h};return p.hash=s.length?s.reduce((function(e,t){return t.name||nt(15),Oe(e,t.name)}),Re).toString():"",p}var Pt=new wt,kt=xt(),Rt=i().createContext({shouldForwardProp:void 0,styleSheet:Pt,stylis:kt}),Ot=Rt.Consumer,Tt=i().createContext(void 0);function It(){return(0,r.useContext)(Rt)}function Dt(e){var t=(0,r.useState)(e.stylisPlugins),n=t[0],a=t[1],l=It().styleSheet,o=(0,r.useMemo)((function(){var t=l;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t}),[e.disableCSSOMInjection,e.sheet,e.target,l]),c=(0,r.useMemo)((function(){return xt({options:{namespace:e.namespace,prefix:e.enableVendorPrefixes},plugins:n})}),[e.enableVendorPrefixes,e.namespace,n]);(0,r.useEffect)((function(){s()(n,e.stylisPlugins)||a(e.stylisPlugins)}),[e.stylisPlugins]);var u=(0,r.useMemo)((function(){return{shouldForwardProp:e.shouldForwardProp,styleSheet:o,stylis:c}}),[e.shouldForwardProp,o,c]);return i().createElement(Rt.Provider,{value:u},i().createElement(Tt.Provider,{value:c},e.children))}var At=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=kt);var a=n.name+t.hash;e.hasNameForId(n.id,a)||e.insertRules(n.id,a,t(n.rules,a,"@keyframes"))},this.name=e,this.id="sc-keyframes-".concat(e),this.rules=t,tt(this,(function(){throw nt(12,String(n.name))}))}return e.prototype.getName=function(e){return void 0===e&&(e=kt),this.name+e.hash},e}(),Nt=function(e){return e>="A"&&e<="Z"};function Ft(e){for(var t="",n=0;n<e.length;n++){var a=e[n];if(1===n&&"-"===a&&"-"===e[0])return e;Nt(a)?t+="-"+a.toLowerCase():t+=a}return t.startsWith("ms-")?"-"+t:t}var jt=function(e){return null==e||!1===e||""===e},$t=function(e){var t,n,a=[];for(var r in e){var i=e[r];e.hasOwnProperty(r)&&!jt(i)&&(Array.isArray(i)&&i.isCss||Je(i)?a.push("".concat(Ft(r),":"),i,";"):Qe(i)?a.push.apply(a,l(l(["".concat(r," {")],$t(i),!1),["}"],!1)):a.push("".concat(Ft(r),": ").concat((t=r,null==(n=i)||"boolean"==typeof n||""===n?"":"number"!=typeof n||0===n||t in oe||t.startsWith("--")?String(n).trim():"".concat(n,"px")),";")))}return a};function Lt(e,t,n,a){return jt(e)?[]:Ke(e)?[".".concat(e.styledComponentId)]:Je(e)?!Je(l=e)||l.prototype&&l.prototype.isReactComponent||!t?[e]:Lt(e(t),t,n,a):e instanceof At?n?(e.inject(n,a),[e.getName(a)]):[e]:Qe(e)?$t(e):Array.isArray(e)?Array.prototype.concat.apply(ye,e.map((function(e){return Lt(e,t,n,a)}))):[e.toString()];var l}function Ht(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(Je(n)&&!Ke(n))return!1}return!0}var Mt=Te(pe),Bt=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&Ht(e),this.componentId=t,this.baseHash=Oe(Mt,t),this.baseStyle=n,wt.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var a=this.baseStyle?this.baseStyle.generateAndInjectStyles(e,t,n):"";if(this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(this.componentId,this.staticRulesId))a=Ze(a,this.staticRulesId);else{var l=Xe(Lt(this.rules,e,t,n)),r=Pe(Oe(this.baseHash,l)>>>0);if(!t.hasNameForId(this.componentId,r)){var i=n(l,".".concat(r),void 0,this.componentId);t.insertRules(this.componentId,r,i)}a=Ze(a,r),this.staticRulesId=r}else{for(var o=Oe(this.baseHash,n.hash),s="",c=0;c<this.rules.length;c++){var u=this.rules[c];if("string"==typeof u)s+=u;else if(u){var p=Xe(Lt(u,e,t,n));o=Oe(o,p+c),s+=p}}if(s){var d=Pe(o>>>0);t.hasNameForId(this.componentId,d)||t.insertRules(this.componentId,d,n(s,".".concat(d),void 0,this.componentId)),a=Ze(a,d)}}return a},e}(),zt=i().createContext(void 0),Ut=zt.Consumer;function Wt(){var e=(0,r.useContext)(zt);if(!e)throw nt(18);return e}function Gt(e){var t=i().useContext(zt),n=(0,r.useMemo)((function(){return function(e,t){if(!e)throw nt(14);if(Je(e))return e(t);if(Array.isArray(e)||"object"!=typeof e)throw nt(8);return t?a(a({},t),e):e}(e.theme,t)}),[e.theme,t]);return e.children?i().createElement(zt.Provider,{value:n},e.children):null}var Vt={};function qt(e,t,n){var l=Ke(e),o=e,s=!Ae(e),c=t.attrs,u=void 0===c?ye:c,p=t.componentId,d=void 0===p?function(e,t){var n="string"!=typeof e?"sc":_e(e);Vt[n]=(Vt[n]||0)+1;var a="".concat(n,"-").concat(Ie(pe+n+Vt[n]));return t?"".concat(t,"-").concat(a):a}(t.displayName,t.parentComponentId):p,m=t.displayName,h=void 0===m?function(e){return Ae(e)?"styled.".concat(e):"Styled(".concat(De(e),")")}(e):m,g=t.displayName&&t.componentId?"".concat(_e(t.displayName),"-").concat(t.componentId):t.componentId||d,y=l&&o.attrs?o.attrs.concat(u).filter(Boolean):u,f=t.shouldForwardProp;if(l&&o.shouldForwardProp){var b=o.shouldForwardProp;if(t.shouldForwardProp){var E=t.shouldForwardProp;f=function(e,t){return b(e,t)&&E(e,t)}}else f=b}var v=new Bt(n,g,l?o.componentStyle:void 0);function w(e,t){return function(e,t,n){var l=e.attrs,o=e.componentStyle,s=e.defaultProps,c=e.foldedComponentIds,u=e.styledComponentId,p=e.target,d=i().useContext(zt),m=It(),h=e.shouldForwardProp||m.shouldForwardProp,g=be(t,d,s)||fe,y=function(e,t,n){for(var l,r=a(a({},t),{className:void 0,theme:n}),i=0;i<e.length;i+=1){var o=Je(l=e[i])?l(r):l;for(var s in o)r[s]="className"===s?Ze(r[s],o[s]):"style"===s?a(a({},r[s]),o[s]):o[s]}return t.className&&(r.className=Ze(r.className,t.className)),r}(l,t,g),f=y.as||p,b={};for(var E in y)void 0===y[E]||"$"===E[0]||"as"===E||"theme"===E&&y.theme===g||("forwardedAs"===E?b.as=y.forwardedAs:h&&!h(E,f)||(b[E]=y[E]));var v=function(e,t){var n=It();return e.generateAndInjectStyles(t,n.styleSheet,n.stylis)}(o,y),w=Ze(c,u);return v&&(w+=" "+v),y.className&&(w+=" "+y.className),b[Ae(f)&&!Ee.has(f)?"class":"className"]=w,b.ref=n,(0,r.createElement)(f,b)}(_,e,t)}w.displayName=h;var _=i().forwardRef(w);return _.attrs=y,_.componentStyle=v,_.displayName=h,_.shouldForwardProp=f,_.foldedComponentIds=l?Ze(o.foldedComponentIds,o.styledComponentId):"",_.styledComponentId=g,_.target=l?o.target:e,Object.defineProperty(_,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(e){this._foldedDefaultProps=l?function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var a=0,l=t;a<l.length;a++)et(e,l[a],!0);return e}({},o.defaultProps,e):e}}),tt(_,(function(){return".".concat(_.styledComponentId)})),s&&Ye(_,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),_}function Yt(e,t){for(var n=[e[0]],a=0,l=t.length;a<l;a+=1)n.push(t[a],e[a+1]);return n}new Set;var Jt=function(e){return Object.assign(e,{isCss:!0})};function Kt(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(Je(e)||Qe(e))return Jt(Lt(Yt(ye,l([e],t,!0))));var a=e;return 0===t.length&&1===a.length&&"string"==typeof a[0]?Lt(a):Jt(Lt(Yt(a,t)))}function Zt(e,t,n){if(void 0===n&&(n=fe),!t)throw nt(1,t);var r=function(a){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return e(t,n,Kt.apply(void 0,l([a],r,!1)))};return r.attrs=function(l){return Zt(e,t,a(a({},n),{attrs:Array.prototype.concat(n.attrs,l).filter(Boolean)}))},r.withConfig=function(l){return Zt(e,t,a(a({},n),l))},r}var Xt=function(e){return Zt(qt,e)},Qt=Xt;Ee.forEach((function(e){Qt[e]=Xt(e)}));var en=function(){function e(e,t){this.rules=e,this.componentId=t,this.isStatic=Ht(e),wt.registerId(this.componentId+1)}return e.prototype.createStyles=function(e,t,n,a){var l=a(Xe(Lt(this.rules,t,n,a)),""),r=this.componentId+e;n.insertRules(r,r,l)},e.prototype.removeStyles=function(e,t){t.clearRules(this.componentId+e)},e.prototype.renderStyles=function(e,t,n,a){e>2&&wt.registerId(this.componentId+e),this.removeStyles(e,n),this.createStyles(e,t,n,a)},e}();function tn(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=Kt.apply(void 0,l([e],t,!1)),o="sc-global-".concat(Ie(JSON.stringify(r))),s=new en(r,o),c=function(e){var t=It(),n=i().useContext(zt),a=i().useRef(t.styleSheet.allocateGSInstance(o)).current;return t.styleSheet.server&&u(a,e,t.styleSheet,n,t.stylis),i().useLayoutEffect((function(){if(!t.styleSheet.server)return u(a,e,t.styleSheet,n,t.stylis),function(){return s.removeStyles(a,t.styleSheet)}}),[a,e,t.styleSheet,n,t.stylis]),null};function u(e,t,n,l,r){if(s.isStatic)s.renderStyles(e,ge,n,r);else{var i=a(a({},t),{theme:be(t,l,c.defaultProps)});s.renderStyles(e,i,n,r)}}return i().memo(c)}function nn(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var a=Xe(Kt.apply(void 0,l([e],t,!1))),r=Ie(a);return new At(r,a)}function an(e){var t=i().forwardRef((function(t,n){var l=be(t,i().useContext(zt),e.defaultProps);return i().createElement(e,a({},t,{theme:l,ref:n}))}));return t.displayName="WithTheme(".concat(De(e),")"),Ye(t,e)}var ln=function(){function e(){var e=this;this._emitSheetCSS=function(){var t=e.instance.toString();if(!t)return"";var n=ht(),a=Xe([n&&'nonce="'.concat(n,'"'),"".concat(se,'="true"'),"".concat(ue,'="').concat(pe,'"')].filter(Boolean)," ");return"<style ".concat(a,">").concat(t,"</style>")},this.getStyleTags=function(){if(e.sealed)throw nt(2);return e._emitSheetCSS()},this.getStyleElement=function(){var t;if(e.sealed)throw nt(2);var n=e.instance.toString();if(!n)return[];var l=((t={})[se]="",t[ue]=pe,t.dangerouslySetInnerHTML={__html:n},t),r=ht();return r&&(l.nonce=r),[i().createElement("style",a({},l,{key:"sc-0-0"}))]},this.seal=function(){e.sealed=!0},this.instance=new wt({isServer:!0}),this.sealed=!1}return e.prototype.collectStyles=function(e){if(this.sealed)throw nt(2);return i().createElement(Dt,{sheet:this.instance},e)},e.prototype.interleaveWithNodeStream=function(e){throw nt(3)},e}(),rn={StyleSheet:wt,mainSheet:Pt};"__sc-".concat(se,"__")},609:e=>{"use strict";e.exports=window.React}},r={};function i(e){var t=r[e];if(void 0!==t)return t.exports;var n=r[e]={exports:{}};return l[e](n,n.exports,i),n.exports}i.m=l,i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(n,a){if(1&a&&(n=this(n)),8&a)return n;if("object"==typeof n&&n){if(4&a&&n.__esModule)return n;if(16&a&&"function"==typeof n.then)return n}var l=Object.create(null);i.r(l);var r={};e=e||[null,t({}),t([]),t(t)];for(var o=2&a&&n;"object"==typeof o&&!~e.indexOf(o);o=t(o))Object.getOwnPropertyNames(o).forEach((e=>r[e]=()=>n[e]));return r.default=()=>n,i.d(l,r),l},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,n)=>(i.f[n](e,t),t)),[])),i.u=e=>({42:"reactPlayerTwitch",173:"reactPlayerVimeo",328:"reactPlayerDailyMotion",340:"reactPlayerWistia",353:"reactPlayerPreview",392:"reactPlayerVidyard",446:"reactPlayerYouTube",458:"reactPlayerFilePlayer",463:"reactPlayerKaltura",570:"reactPlayerMixcloud",627:"reactPlayerStreamable",723:"reactPlayerMux",887:"reactPlayerFacebook",979:"reactPlayerSoundCloud"}[e]+".js"),i.miniCssF=e=>{},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n={},a="simplystatic-settings:",i.l=(e,t,l,r)=>{if(n[e])n[e].push(t);else{var o,s;if(void 0!==l)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var p=c[u];if(p.getAttribute("src")==e||p.getAttribute("data-webpack")==a+l){o=p;break}}o||(s=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,i.nc&&o.setAttribute("nonce",i.nc),o.setAttribute("data-webpack",a+l),o.src=e),n[e]=[t];var d=(t,a)=>{o.onerror=o.onload=null,clearTimeout(m);var l=n[e];if(delete n[e],o.parentNode&&o.parentNode.removeChild(o),l&&l.forEach((e=>e(a))),t)return t(a)},m=setTimeout(d.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=d.bind(null,o.onerror),o.onload=d.bind(null,o.onload),s&&document.head.appendChild(o)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;i.g.importScripts&&(e=i.g.location+"");var t=i.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");if(n.length)for(var a=n.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=n[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=e})(),(()=>{var e={57:0};i.f.j=(t,n)=>{var a=i.o(e,t)?e[t]:void 0;if(0!==a)if(a)n.push(a[2]);else{var l=new Promise(((n,l)=>a=e[t]=[n,l]));n.push(a[2]=l);var r=i.p+i.u(t),o=new Error;i.l(r,(n=>{if(i.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var l=n&&("load"===n.type?"missing":n.type),r=n&&n.target&&n.target.src;o.message="Loading chunk "+t+" failed.\n("+l+": "+r+")",o.name="ChunkLoadError",o.type=l,o.request=r,a[1](o)}}),"chunk-"+t,t)}};var t=(t,n)=>{var a,l,[r,o,s]=n,c=0;if(r.some((t=>0!==e[t]))){for(a in o)i.o(o,a)&&(i.m[a]=o[a]);s&&s(i)}for(t&&t(n);c<r.length;c++)l=r[c],i.o(e,l)&&e[l]&&e[l][0](),e[l]=0},n=globalThis.webpackChunksimplystatic_settings=globalThis.webpackChunksimplystatic_settings||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),i.nc=void 0,(()=>{"use strict";var e=i(609),t=i.n(e);const n=window.wp.element,a=window.wp.apiFetch;var l=i.n(a);function r(t,n){const a=(0,e.useRef)();(0,e.useEffect)((()=>{a.current=t}),[t]),(0,e.useEffect)((()=>{if(null!==n){let e=setInterval((function(){a.current()}),n);return()=>clearInterval(e)}}),[n])}const{__}=wp.i18n,o=(0,n.createContext)(),s=function(t){const a={destination_scheme:"https://",destination_host:"",temp_files_dir:"",additional_urls:"",additional_files:"",urls_to_exclude:"",delivery_method:"zip",local_dir:"",relative_path:"",destination_url_type:"relative",debugging_mode:!0,server_cron:!1,whitelist_plugins:"",http_basic_auth_username:"",http_basic_auth_password:"",http_basic_auth_on:!1,origin_url:"",version:options.version,force_replace_url:!0,clear_directory_before_export:!1,iframe_urls:"",iframe_custom_css:"",tiiny_email:options.admin_email,tiiny_subdomain:"",tiiny_domain_suffix:"tiiny.site",tiiny_password:"",cdn_api_key:"",cdn_storage_host:"storage.bunnycdn.com",cdn_access_key:"",cdn_pull_zone:"",cdn_storage_zone:"",cdn_directory:"",github_account_type:"personal",github_user:"",github_email:"",github_personal_access_token:"",github_repository:"",github_repository_visibility:"public",github_branch:"main",github_webhook_url:"",github_folder_path:"",github_throttle_requests:!1,github_batch_size:100,aws_region:"us-east-2",aws_auth_method:"aws-iam-key",aws_access_key:"",aws_access_secret:"",aws_bucket:"",aws_subdirectory:"",aws_distribution_id:"",aws_webhook_url:"",aws_empty:!1,s3_access_key:"",s3_base_url:"",s3_access_secret:"",s3_bucket:"",s3_subdirectory:"",fix_cors:"allowed_http_origins",static_url:"",use_forms:!1,use_comments:!1,comment_redirect:"",use_search:!1,search_type:"fuse",search_index_title:"title",search_index_content:"body",search_index_excerpt:".entry-content",search_excludable:"",search_metadata:"",fuse_selector:".search-field",fuse_threshold:.1,algolia_app_id:"",algolia_admin_api_key:"",algolia_search_api_key:"",algolia_index:"simply_static",algolia_selector:".search-field",use_minify:!1,minify_html:!1,minify_html_leave_quotes:!1,minify_css:!1,minify_inline_css:!1,minify_css_exclude:"",minify_js_exclude:"",minify_js:!1,minify_inline_js:!1,generate_404:!1,add_feeds:!1,add_rest_api:!1,wp_content_directory:"",wp_includes_directory:"",wp_uploads_directory:"",wp_plugins_directory:"",wp_themes_directory:"",theme_style_name:"style",author_url:"",hide_comments:!1,hide_version:!1,hide_generator:!1,hide_prefetch:!1,hide_rsd:!1,hide_emotes:!1,disable_xmlrpc:!1,disable_embed:!1,disable_db_debug:!1,disable_wlw_manifest:!1,incremental_export:!1,sftp_host:"",sftp_user:"",sftp_pass:"",sftp_private_key:"",sftp_folder:"",sftp_port:22,shortpixel_enabled:!1,shortpixel_api_key:"",shortpixel_backup_enabled:!1,scan_themes_plugins_dir:!1,integrations:!1},[i,s]=(0,n.useState)(!1),[c,u]=(0,n.useState)(0),[p,d]=(0,n.useState)(!1),[m,h]=(0,n.useState)(!1),[g,y]=(0,n.useState)(!1),[f,b]=(0,n.useState)(a),[E,v]=(0,n.useState)({}),[w,_]=(0,n.useState)("yes"),[S,C]=(0,n.useState)(1),[x,P]=(0,n.useState)([]),[k,R]=(0,n.useState)(!0),O=()=>{l()({path:"/simplystatic/v1/settings"}).then((e=>{b(e)}))},T=()=>{l()({path:"/simplystatic/v1/is-running",method:"GET"}).then((e=>{var t=JSON.parse(e);s(t.running),d(t.paused),t.delayed&&u(t.delayed_until)}))},I=e=>["environments"].indexOf(e)>=0,D=e=>x.indexOf(e)>=0,A=e=>{let t=f.integrations;return!1!==t&&t.indexOf(e)>=0};return r((()=>{u(c-1)}),c>0?1e3:null),r((()=>{T()}),i||c?5e3:null),(0,n.useEffect)((()=>{O(),l()({path:"/simplystatic/v1/system-status"}).then((e=>{v(e),l()({path:"/simplystatic/v1/system-status/passed"}).then((e=>{let t=JSON.parse(e);_(t.passed)}))})),T(),C(options.blog_id)}),[]),(0,e.createElement)(o.Provider,{value:{settings:f,configs:E,passedChecks:w,settingsSaved:g,setSettingsSaved:y,updateSetting:(e,t)=>{b({...f,[e]:t})},setSettings:b,saveSettings:()=>{l()({path:"/simplystatic/v1/settings",method:"POST",data:f}).then((e=>{P([])}))},resetSettings:()=>{b(a),l()({path:"/simplystatic/v1/settings/reset",method:"POST",data:a})},resetDatabase:()=>{l()({path:"/simplystatic/v1/settings/reset-database",method:"POST"})},getSettings:O,updateFromNetwork:e=>{l()({path:"/simplystatic/v1/update-from-network",method:"POST",data:{blog_id:e}})},importSettings:e=>{b(e),l()({path:"/simplystatic/v1/settings",method:"POST",data:e})},migrateSettings:()=>{l()({path:"/simplystatic/v1/migrate",method:"POST",migrate:!0})},resetDiagnostics:()=>{l()({path:"/simplystatic/v1/reset-diagnostics",method:"POST"})},isRunning:i,setIsRunning:s,isPaused:p,setIsPaused:d,setIsResumed:h,isResumed:m,blogId:S,setBlogId:C,isPro:()=>!!options.is_multisite||!!options.connect&&!!options.connect.is_connected,isStudio:()=>!!(options.home.includes("static.studio")||options.home.includes("static1.studio")||options.home.includes("static2.studio")),isIntegrationActive:A,canRunIntegration:e=>!!A(e)&&!D(e),maybeQueueIntegration:e=>{I(e)&&(D(e)||(x.push(e),P(x)))},maybeUnqueueIntegration:e=>{if(!I(e))return;if(!D(e))return;const t=x.indexOf(e);t<0||(x.splice(t,1),P(x))},isQueuedIntegration:D,showMobileNav:k,setShowMobileNav:R,isDelayed:c}},t.children)},c=window.wp.components;var u=i(554),p=i.n(u);const{__:d}=wp.i18n,m=function({title:t,videoUrl:a}){const[l,r]=(0,n.useState)(!1);return(0,e.createElement)(e.Fragment,null,l&&(0,e.createElement)("div",{class:"simply-static-video-modal-background"},(0,e.createElement)(c.Modal,{title:t,className:"simply-static-video-modal",onRequestClose:()=>r(!1)},(0,e.createElement)(p(),{url:a,controls:!0,width:"920px",height:"560px"}))),(0,e.createElement)(c.Button,{variant:"link",className:"simply-static-video-button",onClick:()=>r(!0)},(0,e.createElement)(c.Dashicon,{icon:"format-video"})))},{__:h}=wp.i18n,g=function(){const{settings:t,updateSetting:a,saveSettings:l,settingsSaved:r,setSettingsSaved:i,isPro:s}=(0,n.useContext)(o),[u,p]=(0,n.useState)("relative"),[d,g]=(0,n.useState)(!1),[y,f]=(0,n.useState)("https://"),[b,E]=(0,n.useState)(""),[v,w]=(0,n.useState)("/"),[_,S]=(0,n.useState)(!1),[C,x]=(0,n.useState)(!1),[P,k]=(0,n.useState)(!1),[R,O]=(0,n.useState)(!1),[T,I]=(0,n.useState)(!1),[D,A]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{t.destination_url_type&&p(t.destination_url_type),t.destination_scheme&&f(t.destination_scheme),t.destination_host&&E(t.destination_host),t.relative_path&&w(t.relative_path),(t.use_forms||t.use_comments)&&g(!0),t.force_replace_url&&S(t.force_replace_url),t.generate_404&&k(t.generate_404),t.add_feeds&&I(t.add_feeds),t.add_rest_api&&A(t.add_rest_api),t.scan_themes_plugins_dir&&O(t.scan_themes_plugins_dir)}),[t]),(0,e.createElement)("div",{className:"inner-settings"},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,h("Replacing URLs","simply-static"),(0,e.createElement)(m,{title:h("How to replace URLs","simply-static"),videoUrl:"https://youtu.be/cb8jAMJlfGI"}))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,h("When exporting your static site, any links to your WordPress site will be replaced by one of the following: absolute URLs, relative URLs, or URLs contructed for offline use.","simply-static")),(0,e.createElement)(c.SelectControl,{label:h("Replacing URLs","simply-static"),value:u,options:[{label:h("Absolute URLs","simply-static"),value:"absolute"},{label:h("Relative Path","simply-static"),value:"relative"},{label:h("Offline Usage","simply-static"),value:"offline"}],onChange:e=>{p(e),a("destination_url_type",e)}}),"absolute"===u&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,{style:{minWidth:"15%"}},(0,e.createElement)(c.SelectControl,{label:h("Scheme","simply-static"),value:y,options:[{label:"https://",value:"https://"},{label:"http://",value:"http://"},{label:"//",value:"//"}],onChange:e=>{f(e),a("destination_scheme",e)}})),(0,e.createElement)(c.FlexItem,{style:{minWidth:"85%"}},(0,e.createElement)(c.TextControl,{label:h("Host","simply-static"),type:"text",placeholder:"example.com",value:b,onChange:e=>{E(e),a("destination_host",e)}}))),(0,e.createElement)("p",null,h("Convert all URLs for your WordPress site to absolute URLs at the domain specified above.","simply-static"))),"relative"===u&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.TextControl,{label:h("Path","simply-static"),type:"text",placeholder:"/",value:v,onChange:e=>{w(e),a("relative_path",e)}}),(0,e.createElement)("p",null,h("Convert all URLs for your WordPress site to relative URLs that will work at any domain.","simply-static"),(0,e.createElement)("br",null),h("Optionally specify a path above if you intend to place the files in a subdirectory.","simply-static")),(0,e.createElement)("p",null,(0,e.createElement)(c.Notice,{status:"warning",isDismissible:!1},(0,e.createElement)("b",null,h("Example","simply-static"),": "),h("enter /path above if you wanted to serve your files at www.example.com/path/","simply-static")))),"offline"===u&&(0,e.createElement)("p",null,h("Convert all URLs for your WordPress site so that you can browse the site locally on your own computer without hosting it on a web server.","simply-static")),!d&&(0,e.createElement)(c.ToggleControl,{label:h("Force URL replacements","simply-static"),help:h(_?"Replace all occurrences of the WordPress URL with the static URL (includes inline CSS and JS).":"Replace only occurrences of the WordPress URL that match our tag list.","simply-static"),checked:_,onChange:e=>{S(e),a("force_replace_url",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,h("Include","simply-static"),(0,e.createElement)(m,{title:h("Include & Exclude files and pages","simply-static"),videoUrl:"https://youtu.be/voAHfwVMLi8"}))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextareaControl,{label:h("Additional URLs","simply-static"),placeholder:options.home+"/hidden-page/",help:h("If you want to create static copies of pages or files that aren't linked to, add the URLs here (one per line).","simply-static"),value:t.additional_urls,onChange:e=>{a("additional_urls",e)}}),(0,e.createElement)(c.TextareaControl,{label:h("Additional Files and Directories","simply-static"),placeholder:options.home_path+"additional-directory/\n"+options.home_path+"additional-file.html",help:h("Sometimes you may want to include additional files (such as files referenced via AJAX) or directories. Add the paths to those files or directories here (one per line).","simply-static"),value:t.additional_files,onChange:e=>{a("additional_files",e)}}),(0,e.createElement)(c.ClipboardButton,{variant:"secondary",text:options.home_path,onCopy:()=>x(!0),onFinishCopy:()=>x(!1)},h(C?"Copied home path":"Copy home path","simply-static")),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.ToggleControl,{label:(0,e.createElement)(e.Fragment,null,h("Generate 404 Page?","simply-static"),(0,e.createElement)(m,{title:h("How to manage 404 pages?","simply-static"),videoUrl:"https://youtu.be/dnRtuQrXG-k"})),help:h(P?"Generate a 404 page based on your theme template.":"Don't generate a 404 page.","simply-static"),checked:P,onChange:e=>{k(e),a("generate_404",e)}}),(0,e.createElement)(c.ToggleControl,{label:(0,e.createElement)(e.Fragment,null,h("Include Feeds?","simply-static")),help:h(T?"Include feed URLs of all your posts.":"Don't include feed URLs for all your posts.","simply-static"),checked:T,onChange:e=>{I(e),a("add_feeds",e)}}),(0,e.createElement)(c.ToggleControl,{label:(0,e.createElement)(e.Fragment,null,h("Include Rest API?","simply-static")),help:h(D?"Include all Rest API endpoints as JSON files.":"Don't include Rest API endpoints as JSON files.","simply-static"),checked:D,onChange:e=>{A(e),a("add_rest_api",e)}}),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.ToggleControl,{label:(0,e.createElement)(e.Fragment,null,h("Include all Theme and Plugins assets?","simply-static")),help:h(R?"Include all assets from active theme and plugins.":"Don't include all assets from active theme and plugins.","simply-static"),checked:R,onChange:e=>{O(e),a("scan_themes_plugins_dir",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,h("Exclude","simply-static"),(0,e.createElement)(m,{title:h("Include & Exclude files and pages","simply-static"),videoUrl:"https://youtu.be/voAHfwVMLi8"}))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextareaControl,{label:h("Urls to exclude","simply-static"),placeholder:"some-directory\nsome-file.json\n.jpg",help:h("Specify URLs (or parts of URLs) you want to exclude from the processing (one per line).","simply-static"),value:t.urls_to_exclude,onChange:e=>{a("urls_to_exclude",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),r&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,h("Settings saved successfully.","simply-static"))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5})),(0,e.createElement)("div",{className:"save-settings"},(0,e.createElement)(c.Button,{onClick:()=>{l(),i(!0),setTimeout((function(){i(!1)}),2e3)},variant:"primary"},h("Save Settings","simply-static"))))},{__:y}=wp.i18n,f=function(){const{configs:t,resetDiagnostics:a}=(0,n.useContext)(o),[l,r]=(0,n.useState)(!1);return(0,e.createElement)("div",{className:"inner-settings"},(0,e.createElement)("div",null,(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,y("Diagnostics","simply-static"),(0,e.createElement)(m,{title:y("How to use diagnostics","simply-static"),videoUrl:"https://youtu.be/X59YMlz6F2s"}))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,y("Our diagnostics tool provides detailed insights into your WordPress installation and server configuration and tells you exactly what needs to be optimized to get the most out of Simply Static. Click the button below to get the latest results.","simply-static")),(0,e.createElement)("p",null,(0,e.createElement)(c.Button,{onClick:()=>{a(),r(!0),setTimeout((function(){window.location.reload()}),2e3)},variant:"secondary"},y("Reset Diagnostics","simply-static"))),l?(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,y("Diagnostics resetted successfully.","simply-static"))))):"")),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),Object.keys(t).map((n=>{const a=t[n];return(0,e.createElement)("div",{key:n},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,n)),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("div",null,(0,e.createElement)("table",{style:{width:"100%",tableLayout:"fixed"}},(0,e.createElement)("tbody",{className:"table-data"},Object.entries(a).map((t=>(0,e.createElement)("tr",{className:"table-row",key:t[0]},(0,e.createElement)("td",{className:"diagnostics-icon"}," ",t[1].test?(0,e.createElement)(c.Dashicon,{className:"icon-yes",icon:"yes"}):(0,e.createElement)(c.Dashicon,{className:"icon-no",icon:"no"})),(0,e.createElement)("td",{className:"diagnostics-test"},(0,e.createElement)("b",null,t[0])),(0,e.createElement)("td",null,t[1].test),(0,e.createElement)("td",{className:"diagnostics-result"}," ",t[1].test?(0,e.createElement)("p",null,t[1].description):(0,e.createElement)("p",null,t[1].error)))))))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}))}))))},{__:b}=wp.i18n,E=function(){const{settings:t,importSettings:a,saveSettings:l,resetSettings:r,migrateSettings:i,resetDatabase:s}=(0,n.useContext)(o),[u,p]=(0,n.useState)(!1),[d,h]=(0,n.useState)(!1),[g,y]=(0,n.useState)(!1),[f,E]=(0,n.useState)(!1),[v,w]=(0,n.useState)(!1),[_,S]=(0,n.useState)(!1),[C,x]=(0,n.useState)(!1);return(0,e.createElement)("div",{className:"inner-settings"},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,b("Migrate Settings","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,b("Migrate all of your settings to Simply Static 3.0","simply-static")),(0,e.createElement)("p",null,(0,e.createElement)(c.Button,{onClick:()=>{i(),l(),w(!0),setTimeout((function(){w(!1),location.reload()}),2e3)},variant:"primary"},b("Migrate settings","simply-static"))),v?(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,b("Settings migration successfully.","simply-static"))))):"")),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,b("Export","simply-static"),(0,e.createElement)(m,{title:b("Export & Import settings","simply-static"),videoUrl:"https://youtu.be/fmM123Y-gwg"}))),(0,e.createElement)(c.CardBody,null,u?(0,e.createElement)(e.Fragment,null,(0,e.createElement)("p",null,(0,e.createElement)("code",null,JSON.stringify(t))),(0,e.createElement)("p",null,(0,e.createElement)(c.ClipboardButton,{variant:"secondary",text:JSON.stringify(t),onCopy:()=>S(!0),onFinishCopy:()=>S(!1)},b(_?"Copied!":"Copy export data","simply-static")))):(0,e.createElement)("p",null,(0,e.createElement)(c.Button,{onClick:p,variant:"primary"},b("Export Settings","simply-static"))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,b("Import","simply-static"),(0,e.createElement)(m,{title:b("Export & Import settings","simply-static"),videoUrl:"https://youtu.be/fmM123Y-gwg"}))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,b("Paste in the JSON string you got from your export to import all settings for the plugin.","simply-static")),(0,e.createElement)("textarea",{rows:"8",name:"import-data",onChange:e=>{x(JSON.parse(e.target.value))}}),(0,e.createElement)("p",null,(0,e.createElement)(c.Button,{onClick:()=>{a(C),h(!0),setTimeout((function(){h(!1)}),2e3)},variant:"primary"},b("Import Settings","simply-static"))),d?(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,b("Settings imported successfully.","simply-static"))))):"")),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,b("Reset","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,b('By clicking the "Reset Plugin Settings", you will reset all plugin settings. This can be useful if you want to import a new set of settings or you want a fresh start.',"simply-static")),(0,e.createElement)("p",null,b('If you click the "Reset Database Table" button instead, you will keep all your settings, and we will only recreate our DB table.',"simply-static")),(0,e.createElement)("p",null,(0,e.createElement)(c.Button,{onClick:()=>{r(),y(!0),setTimeout((function(){y(!1)}),2e3)},variant:"secondary"},b("Reset Plugin Settings","simply-static")),(0,e.createElement)(c.Button,{onClick:()=>{s(),E(!0),setTimeout((function(){E(!1)}),2e3)},className:"reset-db-btn",variant:"primary"},b("Reset Database Table","simply-static"))),g?(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,b("Settings resetted successfully.","simply-static"))))):"",f?(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,b("Database table resetted successfully.","simply-static"))))):"")))},{__:v}=wp.i18n,w=function(){var t;const{settings:a,updateSetting:r,saveSettings:i,settingsSaved:s,setSettingsSaved:u,isRunning:p,isPro:d,isStudio:h}=(0,n.useContext)(o),[g,y]=(0,n.useState)("zip"),[f,b]=(0,n.useState)(!1),[E,w]=(0,n.useState)("personal"),[_,S]=(0,n.useState)("private"),[C,x]=(0,n.useState)(!1),[P,k]=(0,n.useState)(!1),[R,O]=(0,n.useState)("us-east-2"),[T,I]=(0,n.useState)("aws-iam-key"),[D,A]=(0,n.useState)(!1),[N,F]=(0,n.useState)(!1),[j,$]=(0,n.useState)(!1),[L,H]=(0,n.useState)(!1),[M]=(0,n.useState)([{label:v("ZIP Archive","simply-static"),value:"zip"},{label:v("Local Directory","simply-static"),value:"local"},{label:v("Static Studio","simply-static"),value:"simply-static-studio"},{label:v("SFTP","simply-static"),value:"sftp"},{label:v("GitHub","simply-static"),value:"github"},{label:v("AWS S3","simply-static"),value:"aws-s3"},{label:v("Bunny CDN","simply-static"),value:"cdn"},{label:v("Tiiny.host","simply-static"),value:"tiiny"}]),B=()=>{i(),u(!0),$(!1),setTimeout((function(){u(!1)}),2e3)};return(0,n.useEffect)((()=>{a.delivery_method&&y(a.delivery_method),a.clear_directory_before_export&&b(a.clear_directory_before_export),a.github_account_type&&w(a.github_account_type),a.github_repository_visibility&&S(a.github_repository_visibility),a.github_repository_visibility&&S(a.github_repository_visibility),a.github_throttle_requests&&k(a.github_throttle_requests),a.aws_empty&&x(a.aws_empty),a.aws_auth_method&&I(a.aws_auth_method),a.aws_region&&O(a.aws_region),l()({path:"/simplystatic/v1/pages"}).then((e=>{let t=e;t.unshift({label:v("No page selected","simply-static"),value:0}),F(t)}))}),[a]),(0,e.createElement)("div",{className:"inner-settings"},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,v("Deployment Settings","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,v("Choose from a variety of deployment methods. Depending on your selection we either provide a ZIP file, export to a local directory or send your files to a remote destination.","simply-static")),(0,e.createElement)(c.SelectControl,{label:v("Deployment method","simply-static"),value:g,options:M,onChange:e=>{y(e),r("delivery_method",e),$(!0)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),"simply-static-studio"===g&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,v("Static Studio","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,v("The all-in-one Static WordPress cloud-hosting platform.","simply-static")),(0,e.createElement)("p",null,v("Enjoy secure WordPress, the fastest exports, and the best-performing static site hosting in one package.","simply-static")),(0,e.createElement)("p",null,(0,e.createElement)("a",{class:"button button-primary",href:"https://simplystatic.com/simply-static-studio/",target:"_blank"},"Check out Static Studio")))),"zip"===g&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,v("ZIP","simply-static"),(0,e.createElement)(m,{title:v("How to export a ZIP file","simply-static"),videoUrl:"https://youtu.be/WHaFjDte6zI"}))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,v("Get a download link in the activity log once the static export has finished.","simply-static")))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),"local"===g&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,v("Local Directory","simply-static"),(0,e.createElement)(m,{title:v("How to deploy to a local directory","simply-static"),videoUrl:"https://youtu.be/ZRdXQB5slnY"}))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextControl,{label:v("Path","simply-static"),type:"text",help:v("This is the directory where your static files will be saved. We will create it automatically on the first export if it doesn't exist.","simply-static"),placeholder:options.home_path+"public_static/",value:a.local_dir,onChange:e=>{r("local_dir",e)}}),(0,e.createElement)("p",null,(0,e.createElement)(c.ClipboardButton,{variant:"secondary",text:options.home_path,onCopy:()=>A(!0),onFinishCopy:()=>A(!1)},v(D?"Copied home path":"Copy home path","simply-static"))),(0,e.createElement)("p",null,(0,e.createElement)(c.ToggleControl,{label:v("Clear Local Directory","simply-static"),help:v(f?"Clear local directory before running an export.":"Don't clear local directory before running an export.","simply-static"),checked:f,onChange:e=>{b(e),r("clear_directory_before_export",e)}})))),(0,e.createElement)(e.Fragment,null,"github"===g&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,v("GitHub","simply-static")," ",(0,e.createElement)(m,{title:v("How to deploy to a GitHub (2/2)","simply-static"),videoUrl:"https://youtu.be/HqyTKwZuUAM"}))),("free"===options.plan||!d())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",v("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,v("GitHub enables you to export your static website to one of the common static hosting providers like Netlify, Cloudflare Pages or GitHub Pages.","simply-static")),(0,e.createElement)(c.SelectControl,{label:v("Account Type","simply-static"),value:E,help:v("Depending on the account type the settings fields will change.","simply-static"),disabled:"free"===options.plan||!d(),options:[{label:v("Personal","simply-static"),value:"personal"},{label:v("Organization","simply-static"),value:"organization"}],onChange:e=>{w(e),r("github_account_type",e)}}),"organization"===E?(0,e.createElement)(c.TextControl,{label:v("Organization","simply-static"),type:"text",help:v("Enter the name of your organization.","simply-static"),disabled:"free"===options.plan||!d(),value:a.github_user,onChange:e=>{r("github_user",e)}}):(0,e.createElement)(c.TextControl,{label:v("Username","simply-static"),type:"text",help:v("Enter your GitHub username.","simply-static"),disabled:"free"===options.plan||!d(),value:a.github_user,onChange:e=>{r("github_user",e)}}),(0,e.createElement)(c.TextControl,{label:v("E-Mail","simply-static"),type:"email",help:v("Enter your GitHub email address. This will be used to commit files to your repository.","simply-static"),disabled:"free"===options.plan||!d(),value:a.github_email,onChange:e=>{r("github_email",e)}}),(0,e.createElement)(c.TextControl,{label:(0,e.createElement)(e.Fragment,null,v("Personal Access Token","simply-static"),(0,e.createElement)(m,{title:v("How to prepare your GitHub account","simply-static"),videoUrl:"https://youtu.be/fjsJJmPeKuc"})),type:"password",help:(0,e.createElement)(e.Fragment,null,v("You need a personal access token from GitHub. Learn how to get one ","simply-static"),(0,e.createElement)("a",{href:"https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#creating-a-personal-access-token-classic",target:"_blank"},v("here","simply-static"))),disabled:"free"===options.plan||!d(),value:a.github_personal_access_token,onChange:e=>{r("github_personal_access_token",e)}}),(0,e.createElement)(c.TextControl,{label:v("Repository","simply-static"),type:"text",help:v("Enter a name for your repository (lowercase without spaces or special characters).","simply-static"),disabled:"free"===options.plan||!d(),value:a.github_repository,onChange:e=>{r("github_repository",e)}}),(0,e.createElement)(c.Notice,{status:"warning",isDismissible:!1},(0,e.createElement)("p",null,v("Ensure to create the repository and add a readme file to it before running an export as shown in the docs ","simply-static"),(0,e.createElement)("a",{href:"https://docs.simplystatic.com/article/33-set-up-the-github-integration/",target:"_blank"},v("here","simply-static")))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.TextControl,{label:v("Folder","simply-static"),type:"text",help:v("Enter a relative path to a folder if you want to push files under it. Example: for github.com/USER/REPOSITORY/folder1, enter folder1","simply-static"),disabled:"free"===options.plan||!d(),value:a.github_folder_path,onChange:e=>{r("github_folder_path",e)}}),"organization"===E&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Notice,{status:"warning",isDismissible:!1},(0,e.createElement)("p",null,v("You need to create the repository manually within your organization before connecting it.","simply-static"))),(0,e.createElement)(c.__experimentalSpacer,{margin:5})),(0,e.createElement)(c.SelectControl,{label:v("Visiblity","simply-static"),value:_,help:v("Decide if you want to make your repository public or private.","simply-static"),disabled:"free"===options.plan||!d(),options:[{label:v("Public","simply-static"),value:"public"},{label:v("Private","simply-static"),value:"private"}],onChange:e=>{S(e),r("github_repository_visibility",e)}}),(0,e.createElement)(c.TextControl,{label:v("Branch","simply-static"),type:a.github_branch,placeholder:"main",help:v('Simply Static automatically uses "main" as branch. You may want to modify that for example to gh-pages. for GitHub Pages.',"simply-static"),disabled:"free"===options.plan||!d(),value:a.github_branch,onChange:e=>{r("github_branch",e)}}),(0,e.createElement)(c.TextControl,{label:v("Webhook URL","simply-static"),type:"url",help:v("Enter your Webhook URL here and Simply Static will send a POST request after all files are commited to GitHub.","simply-static"),disabled:"free"===options.plan||!d(),value:a.github_webhook_url,onChange:e=>{r("github_webhook_url",e)}}),(0,e.createElement)(c.ToggleControl,{label:v("Throttle Requests","simply-static"),help:v("Enable this option if you are experiencing issues with the GitHub API rate limit.","simply-static"),disabled:"free"===options.plan||!d(),checked:P,onChange:e=>{k(e),r("github_throttle_requests",e)}}),(0,e.createElement)(c.TextControl,{label:v("Batch size","simply-static"),type:"number",help:v("Enter the number of files you want to be processed in a single batch. If current export fails to deploy, lower the number.","simply-static"),disabled:"free"===options.plan||!d(),value:null!==(t=a.github_batch_size)&&void 0!==t?t:100,onChange:e=>{r("github_batch_size",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),"tiiny"===g&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,v("Tiiny.host","simply-static")," ",(0,e.createElement)(m,{title:v("How to deploy to Tiiny.host","simply-static"),videoUrl:"https://youtu.be/Y9EDaQkGl1Y"}))),("free"===options.plan||!d())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",v("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,v("Deploying to Tiiny.host is the easiest and fastest deployment option available in Simply Static Pro.","simply-static")),(0,e.createElement)(c.TextControl,{disabled:!0,label:v("E-Mail","simply-static"),type:"text",help:(0,e.createElement)(e.Fragment,null,v("This field is auto-filled with the e-mail address used for activating Simply Static Pro.","simply-static"),(0,e.createElement)("br",null),(0,e.createElement)("b",null,v("An account will be created automatically on your first deployment.","simply-static"))),value:options.admin_email}),(0,e.createElement)(c.TextControl,{label:v("Subdomain","simply-static"),type:"text",help:v("That's the part before your TLD. Your full URL is the combination of the subdomain plus the domain suffix.","simply-static"),disabled:"free"===options.plan||!d(),value:a.tiiny_subdomain,onChange:e=>{r("tiiny_subdomain",e)}}),(0,e.createElement)(c.TextControl,{label:v("Domain Suffix","simply-static"),type:"text",help:v("This defaults to tiiny.site. If you have a custom domain configured in Tiiny.host, you can also use  that one.","simply-static"),disabled:"free"===options.plan||!d(),value:a.tiiny_domain_suffix,onChange:e=>{r("tiiny_domain_suffix",e)}}),(0,e.createElement)(c.TextControl,{label:v("Password Protection","simply-static"),type:"password",help:v("Adding a password will activate password protection on your static site. The website is only visible with the password.","simply-static"),disabled:"free"===options.plan||!d(),value:a.tiiny_password,onChange:e=>{r("tiiny_password",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),"cdn"===g&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,v("Bunny CDN","simply-static"),(0,e.createElement)(m,{title:v("How to deploy to Bunny CDN","simply-static"),videoUrl:"https://youtu.be/FBRg1BI41VY"}))),("free"===options.plan||!d())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",v("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,v("Bunny CDN is a fast and reliable CDN provider that you can run your static website on.","simply-static")),(0,e.createElement)(c.TextControl,{label:v("Bunny CDN API Key","simply-static"),type:"password",help:(0,e.createElement)(e.Fragment,null,v("Enter your API Key from Bunny CDN. You can find your API-Key as described ","simply-static"),(0,e.createElement)("a",{href:"https://support.bunny.net/hc/en-us/articles/360012168840-Where-do-I-find-my-API-key",target:"_blank"},v("here","simply-static"))),disabled:"free"===options.plan||!d(),value:a.cdn_api_key,onChange:e=>{r("cdn_api_key",e)}}),(0,e.createElement)(c.TextControl,{label:v("Storage Host","simply-static"),type:"text",help:(0,e.createElement)(e.Fragment,null,v("Depending on your location, you have a different storage host. You find out which URL to use ","simply-static"),(0,e.createElement)("a",{href:"https://docs.bunny.net/reference/storage-api#storage-endpoints",target:"_blank"},v("here","simply-static"))),disabled:"free"===options.plan||!d(),value:a.cdn_storage_host,onChange:e=>{r("cdn_storage_host",e)}}),(0,e.createElement)(c.TextControl,{label:v("Bunny CDN Access Key","simply-static"),type:"password",help:v("Enter your Acess Key from Bunny CDN. You will find it within your storage zone setttings within FTP & API Access -> Password.","simply-static"),disabled:"free"===options.plan||!d(),value:a.cdn_access_key,onChange:e=>{r("cdn_access_key",e)}}),(0,e.createElement)(c.TextControl,{label:v("Pull Zone","simply-static"),type:"text",help:v("A pull zone is the connection of your CDN to the internet. Simply Static will try to find an existing pull zone with the provided name, if there is none it creates a new pull zone.","simply-static"),disabled:"free"===options.plan||!d(),value:a.cdn_pull_zone,onChange:e=>{r("cdn_pull_zone",e)}}),(0,e.createElement)(c.TextControl,{label:v("Storage Zone","simply-static"),type:"text",help:v("A storage zone contains your static files. Simply Static will try to find an existing storage zone with the provided name, if there is none it creates a new storage zone.","simply-static"),disabled:"free"===options.plan||!d(),value:a.cdn_storage_zone,onChange:e=>{r("cdn_storage_zone",e)}}),(0,e.createElement)(c.TextControl,{label:v("Subdirectory","simply-static"),type:"text",placeholder:"/subdirectory/",help:v("If you want to transfer the files to a specific subdirectory on your storage zone add the name of that directory here.","simply-static"),disabled:"free"===options.plan||!d(),value:a.cdn_directory,onChange:e=>{r("cdn_directory",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),"aws-s3"===g&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,v("Amazon AWS S3","simply-static"),(0,e.createElement)(m,{title:v("How to deploy to Amazon AWS S3","simply-static"),videoUrl:"https://youtu.be/rtn21J86Upc"}))),("free"===options.plan||!d())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",v("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.SelectControl,{label:v("Authentication Method","simply-static"),value:T,options:[{label:v("AWS IAM Access Key","simply-static"),value:"aws-iam-key"},{label:v("AWS EC2","simply-static"),value:"aws-ec2"}],disabled:"free"===options.plan||!d(),onChange:e=>{I(e),r("aws_auth_method",e)}}),"aws-iam-key"===T&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.TextControl,{label:v("Access Key ID","simply-static"),type:"text",help:(0,e.createElement)(e.Fragment,null,v("Enter your Access Key from AWS. Learn how to get one ","simply-static"),(0,e.createElement)("a",{href:"https://docs.aws.amazon.com/en_en/IAM/latest/UserGuide/id_credentials_access-keys.html",target:"_blank"},v("here","simply-static"))),disabled:"free"===options.plan||!d(),value:a.aws_access_key,onChange:e=>{r("aws_access_key",e)}}),(0,e.createElement)(c.TextControl,{label:v("Secret Access Key","simply-static"),type:"password",help:(0,e.createElement)(e.Fragment,null,v("Enter your Secret Key from AWS. Learn how to get one ","simply-static"),(0,e.createElement)("a",{href:"https://docs.aws.amazon.com/en_en/IAM/latest/UserGuide/id_credentials_access-keys.html",target:"_blank"},v("here","simply-static"))),disabled:"free"===options.plan||!d(),value:a.aws_access_secret,onChange:e=>{r("aws_access_secret",e)}})),(0,e.createElement)(c.SelectControl,{label:v("Region","simply-static"),value:R,options:[{label:v("US East (Ohio)","simply-static"),value:"us-east-2"},{label:v("US East (N. Virginia)","simply-static"),value:"us-east-1"},{label:v("US West (N. California)","simply-static"),value:"us-west-1"},{label:v("US West (Oregon)","simply-static"),value:"us-west-2"},{label:v("Africa (Cape Town)","simply-static"),value:"af-south-1"},{label:v("Asia Pacific (Hong Kong)","simply-static"),value:"ap-east-1"},{label:v("Asia Pacific (Hyderabad)","simply-static"),value:"ap-south-2"},{label:v("Asia Pacific (Jakarta)","simply-static"),value:"ap-southeast-3"},{label:v("Asia Pacific (Melbourne)","simply-static"),value:"ap-southeast-4"},{label:v("Asia Pacific (Mumbai)","simply-static"),value:"ap-south-1"},{label:v("Asia Pacific (Osaka)","simply-static"),value:"ap-northeast-3"},{label:v("Asia Pacific (Seoul)","simply-static"),value:"ap-northeast-2"},{label:v("Asia Pacific (Singapore)","simply-static"),value:"ap-southeast-1"},{label:v("Asia Pacific (Sydney)","simply-static"),value:"ap-southeast-2"},{label:v("Asia Pacific (Tokyo)","simply-static"),value:"ap-northeast-1"},{label:v("Canada (Central)","simply-static"),value:"ca-central-1"},{label:v("Europe (Frankfurt)","simply-static"),value:"eu-central-1"},{label:v("Europe (Ireland)","simply-static"),value:"eu-west-1"},{label:v("Europe (London)","simply-static"),value:"eu-west-2"},{label:v("Europe (Milan)","simply-static"),value:"eu-south-1"},{label:v("Europe (Paris)","simply-static"),value:"eu-west-3"},{label:v("Europe (Spain)","simply-static"),value:"eu-south-2"},{label:v("Europe (Stockholm)","simply-static"),value:"eu-north-1"},{label:v("Europe (Zurich)","simply-static"),value:"eu-central-2"},{label:v("Middle East (Bahrain)","simply-static"),value:"me-south-1"},{label:v("Middle East (UAE)","simply-static"),value:"me-central-1"},{label:v("South America (São Paulo)","simply-static"),value:"sa-east-1"},{label:v("AWS GovCloud (US-East)","simply-static"),value:"us-gov-east-1"},{label:v("AWS GovCloud (US-West)","simply-static"),value:"us-gov-west-1"}],disabled:"free"===options.plan||!d(),onChange:e=>{O(e),r("aws_region",e)}}),(0,e.createElement)(c.TextControl,{label:v("Bucket","simply-static"),type:"text",help:v("Add the name of your bucket here.","simply-static"),disabled:"free"===options.plan||!d(),value:a.aws_bucket,onChange:e=>{r("aws_bucket",e)}}),(0,e.createElement)(c.TextControl,{label:v("Subdirectory","simply-static"),type:"text",help:v("Add an optional subdirectory for your bucket","simply-static"),disabled:"free"===options.plan||!d(),value:a.aws_subdirectory,onChange:e=>{r("aws_subdirectory",e)}}),(0,e.createElement)(c.TextControl,{label:v("Cloudfront Distribution ID","simply-static"),type:"text",help:v("We automatically invalidate the cache after each export.","simply-static"),disabled:"free"===options.plan||!d(),value:a.aws_distribution_id,onChange:e=>{r("aws_distribution_id",e)}}),(0,e.createElement)(c.TextControl,{label:v("Webhook URL","simply-static"),type:"url",help:v("Enter your Webhook URL here and Simply Static will send a POST request after all files are transferred to AWS S3.","simply-static"),disabled:"free"===options.plan||!d(),value:a.aws_webhook_url,onChange:e=>{r("aws_webhook_url",e)}}),(0,e.createElement)(c.ToggleControl,{label:v("Empty bucket before new export?","simply-static"),help:v(C?"Clear bucket before new export.":"Don't clear bucket before new export.","simply-static"),disabled:"free"===options.plan||!d(),checked:C,onChange:e=>{x(e),r("aws_empty",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),"sftp"===g&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,v("SFTP","simply-static")," ",(0,e.createElement)(m,{title:v("How to deploy via SFTP","simply-static"),videoUrl:"https://youtu.be/6-QR9wZA3VQ"}))),("free"===options.plan||!d())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",v("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextControl,{label:v("Host","simply-static"),type:"text",help:v("Enter your SFTP host.","simply-static"),value:a.sftp_host,disabled:"free"===options.plan||!d(),onChange:e=>{r("sftp_host",e)}}),(0,e.createElement)(c.TextControl,{label:v("Port","simply-static"),type:"number",disabled:"free"===options.plan||!d(),help:v("Enter your SFTP port.","simply-static"),value:a.sftp_port,onChange:e=>{r("sftp_port",e)}}),(0,e.createElement)(c.TextControl,{label:v("SFTP username","simply-static"),help:v("Enter your SFTP username.","simply-static"),type:"text",disabled:"free"===options.plan||!d(),placeholder:"username",value:a.sftp_user,onChange:e=>{r("sftp_user",e)}}),(0,e.createElement)(c.TextControl,{label:v("SFTP password","simply-static"),type:"password",disabled:"free"===options.plan||!d(),help:v("Enter your SFTP password.","simply-static"),value:a.sftp_pass,onChange:e=>{r("sftp_pass",e)}}),(0,e.createElement)(c.TextareaControl,{label:v("SFTP private key","simply-static"),disabled:"free"===options.plan||!d(),placeholder:v("OPTIONAL: This is only required if you need to authenticate via a private key to access your SFTP server.","simply-static"),help:v("Enter your SFTP private key if you want passwordless upload and the server is configured to allow it. You can set it as a constant in wp-config.php by using define('SSP_SFTP_KEY', 'YOUR_KEY')","simply-static"),value:a.sftp_private_key,onChange:e=>{r("sftp_private_key",e)}}),(0,e.createElement)(c.TextControl,{label:v("SFTP folder","simply-static"),help:v('Leave empty to upload to the default SFTP folder. Enter a folder path where you want the static files to be uploaded to (example: "uploads" will upload to uploads folder. "uploads/new-folder" will upload files to "new-folder"). ',"simply-static"),type:"text",disabled:"free"===options.plan||!d(),placeholder:"",value:a.sftp_folder,onChange:e=>{r("sftp_folder",e)}})))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),s&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,v("Settings saved successfully.","simply-static"))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5})),(0,e.createElement)("div",{className:"save-settings"},"free"===options.plan?(0,e.createElement)(e.Fragment,null,"zip"===g&&(0,e.createElement)(c.Button,{onClick:B,variant:"primary"},v("Save Settings","simply-static")),"local"===g&&(0,e.createElement)(c.Button,{onClick:B,variant:"primary"},v("Save Settings","simply-static"))):(0,e.createElement)(c.Button,{onClick:B,variant:"primary"},v("Save Settings","simply-static")),"pro"===options.plan&&d()&&(0,e.createElement)(c.Button,{disabled:p||j||L,variant:"secondary",isBusy:p||L,onClick:()=>{H(!0),l()({path:"/simplystatic/v1/apply-single",method:"POST"}).then((e=>{404===parseInt(e.status)?alert(e.message):window.location.reload()}))}},j&&v("Save settings to test","simply-static"),!j&&v("Test Deployment","simply-static"))))},{__:_}=wp.i18n,S=function(){const{settings:t,updateSetting:a,saveSettings:r,settingsSaved:i,setSettingsSaved:s,isPro:u}=(0,n.useContext)(o),[p,d]=(0,n.useState)("allowed_http_origins"),[h,g]=(0,n.useState)(!1),[y,f]=(0,n.useState)(!1),[b,E]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{l()({path:"/simplystatic/v1/pages-slugs"}).then((e=>{let t=e;t.unshift({label:_("No page selected","simply-static"),value:""}),E(t)})),t.fix_cors&&d(t.fix_cors),t.use_forms&&g(t.use_forms),t.use_comments&&f(t.use_comments)}),[t]),(0,e.createElement)("div",{className:"inner-settings"},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,_("Forms","simply-static"))),("free"===options.plan||!u())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",_("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.ToggleControl,{label:_("Use forms?","simply-static"),help:_(h?"Use Forms on your static website.":"Don't use forms on your static website.","simply-static"),disabled:"free"===options.plan||!u(),checked:h,onChange:e=>{g(e),a("use_forms",e)}}),h&&options.form_connection_url&&"free"!==options.plan&&(0,e.createElement)(c.Button,{href:options.form_connection_url,variant:"secondary"},_("Create a form connection","simply-static")))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,_("Comments","simply-static"))),("free"===options.plan||!u())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",_("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.ToggleControl,{label:_("Use comments?","simply-static"),help:_(y?"Use comments on your static website.":"Don't use comments on your static website.","simply-static"),disabled:"free"===options.plan||!u(),checked:y,onChange:e=>{f(e),a("use_comments",e)}}),y&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.SelectControl,{label:_("Select a redirect page","content-protector"),options:b,help:_("The post will be regenerated after comment submission, but it might take a while so its good practice to redirect the visitor.","simply-static"),disabled:"free"===options.plan||!u(),value:t.comment_redirect,onChange:e=>{a("comment_redirect",e)}})))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,_("CORS","simply-static"),(0,e.createElement)(m,{title:_("How to deal with CORS","simply-static"),videoUrl:"https://youtu.be/fArtvZhkU14"}))),("free"===options.plan||!u())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",_("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,_("When using Forms and Comments in Simply Static Pro you may encounter CORS issues as you make requests from your static website to your original one.","simply-static")),(0,e.createElement)(c.Notice,{status:"warning",isDismissible:!1},(0,e.createElement)("p",null,_("Due to the variety of server setups out there, you may need to make changes on your server.","simply-static"))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.TextControl,{label:_("Static URL","simply-static"),type:"url",placeholder:"https://static-site.com",help:_("Add the URL of your static website to allow CORS from it.","simply-static"),disabled:"free"===options.plan||!u(),value:t.static_url,onChange:e=>{a("static_url",e)}}),(0,e.createElement)(c.SelectControl,{label:_("Select CORS method","simply-static"),value:p,help:_("Choose one of the methods to allow CORS for your website.","simply-static"),disabled:"free"===options.plan||!u(),options:[{label:"allowed_http_origins",value:"allowed_http_origins"},{label:"wp_headers",value:"wp_headers"}],onChange:e=>{d(e),a("fix_cors",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,_("Embed Dynamic Content (iFrame)","simply-static"),(0,e.createElement)(m,{title:_("Embed Dynamic Content (iFrame)","simply-static"),videoUrl:"https://youtu.be/ZGRaG_Jma7E"}))),("free"===options.plan||!u())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",_("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,_("We replace the HTML of the URLs with an iFrame that embeds the content directly from your WordPress website.","simply-static"),(0,e.createElement)("br",null),_("This way you can use dynamic elements on your static website without the need of a specific integration.","simply-static")),(0,e.createElement)(c.Notice,{status:"warning",isDismissible:!1},(0,e.createElement)("p",null,_("This requires your WordPress website to be online all the time.","simply-static"))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.TextareaControl,{label:_("URLs to embed as an iFrame","simply-static"),placeholder:options.home+"/my-form-page/",help:_("If you want to embed specific pages from your WordPress website into your static website, add the URLs here (one per line).","simply-static"),disabled:"free"===options.plan||!u(),value:t.iframe_urls,onChange:e=>{a("iframe_urls",e)}}),(0,e.createElement)(c.TextareaControl,{label:_("Custom CSS","simply-static"),help:_("These styles will only apply to the embedded pages, not your entire website.","simply-static"),disabled:"free"===options.plan||!u(),value:t.iframe_custom_css,onChange:e=>{a("iframe_custom_css",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),i&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,_("Settings saved successfully.","simply-static"))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5})),(0,e.createElement)("div",{className:"save-settings"},"pro"===options.plan&&u()&&(0,e.createElement)(c.Button,{onClick:()=>{r(),s(!0),setTimeout((function(){s(!1),h&&(localStorage.setItem("ss-initial-page","/forms"),window.location.reload())}),2e3)},variant:"primary"},_("Save Settings","simply-static"))))},{__:C}=wp.i18n,x=function(){const{settings:t,updateSetting:a,saveSettings:l,settingsSaved:r,setSettingsSaved:i,isPro:s}=(0,n.useContext)(o),[u,p]=(0,n.useState)(!1),[d,h]=(0,n.useState)("fuse"),[g,y]=(0,n.useState)(!1),f=()=>y(!0);return(0,n.useEffect)((()=>{t.use_search&&p(t.use_search),t.search_type&&h(t.search_type)}),[t]),(0,e.createElement)("div",{className:"inner-settings"},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,C("Search","simply-static"))),("free"===options.plan||!s())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",C("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.ToggleControl,{label:C("Use search?","simply-static"),help:C(u?"Use search on your static website.":"Don't use search on your static website.","simply-static"),disabled:"free"===options.plan||!s(),checked:u,onChange:e=>{p(e),a("use_search",e)}}),(0,e.createElement)(c.SelectControl,{label:C("Search Type","simply-static"),value:d,help:C("Decide which search type you want to use. Fuse runs locally based on a file, and Algolia is an external API service.","simply-static"),options:[{label:"Fuse JS",value:"fuse"},{label:"Algolia API",value:"algolia"}],onChange:e=>{h(e),a("search_type",e)}}))),(0,e.createElement)(e.Fragment,null,g&&(0,e.createElement)(c.Modal,{title:C("How to select data with meta tags","simply-static"),onRequestClose:()=>y(!1)},(0,e.createElement)("p",null,C("Targeting for excerpt in the meta description tag.","simply-static")),(0,e.createElement)("pre",null,'<meta name="description" content="This content is what we want as excerpt" />'),(0,e.createElement)("p",null,C("Adding such meta in the excerpt field would be:","simply-static")),(0,e.createElement)("pre",null,"description|content"),(0,e.createElement)("p",null,C("Targeting for title in the property meta tag.","simply-static")),(0,e.createElement)("pre",null,'<meta property="og:title" content="This content is what we want as excerpt" />'),(0,e.createElement)("p",null,C("Adding such meta in the excerpt field would be:","simply-static")),(0,e.createElement)("pre",null,"property|og:title"),(0,e.createElement)("p",null,C('If the second item (after | ) is not <code>content</code>, we\'ll use it as value of that attribute (<code>property="og:title"</code> in this example) and use <code>content</code> for value.',"simply-static")),(0,e.createElement)("p",null,(0,e.createElement)("strong",null,C("Caution: Use meta tags that exist everywhere for title.","simply-static")))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,C("Indexing","simply-static"))),("free"===options.plan||!s())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",C("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextControl,{label:C("CSS-Selector for Title","simply-static"),type:"text",placeholder:"title",help:[C("Add the CSS selector which contains the title of the page/post","simply-static")," ",(0,e.createElement)(c.Button,{variant:"link",onClick:f},C("Or meta tags. Click for more information.","simply-static"))],disabled:"free"===options.plan||!s(),value:t.search_index_title,onChange:e=>{a("search_index_title",e)}}),(0,e.createElement)(c.TextControl,{label:C("CSS-Selector for Content","simply-static"),type:"text",placeholder:"body",help:[C("Add the CSS selector which contains the content of the page/post.","simply-static")," ",(0,e.createElement)(c.Button,{variant:"link",onClick:f},C("Or meta tags. Click for more information.","simply-static"))],disabled:"free"===options.plan||!s(),value:t.search_index_content,onChange:e=>{a("search_index_content",e)}}),(0,e.createElement)(c.TextControl,{label:C("CSS-Selector for Excerpt","simply-static"),type:"text",placeholder:".entry-content",help:[C("Add the CSS selector which contains the excerpt of the page/post.","simply-static")," ",(0,e.createElement)(c.Button,{variant:"link",onClick:f},C("Or meta tags. Click for more information.","simply-static"))],disabled:"free"===options.plan||!s(),value:t.search_index_excerpt,onChange:e=>{a("search_index_excerpt",e)}}),(0,e.createElement)(c.TextareaControl,{label:C("Exclude URLs","simply-static"),placeholder:"author\narchive\ncategory",help:C("Exclude URLs from indexing (one per line). You can use full URLs, parts of an URL or plain words (like stop words).","simply-static"),disabled:"free"===options.plan||!s(),value:t.search_excludable,onChange:e=>{a("search_excludable",e)}})))),"fuse"===d&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,C("Fuse.js","simply-static"),(0,e.createElement)(m,{title:C("How to add search with FuseJS","simply-static"),videoUrl:"https://youtu.be/K34l1DXjCHk"}))),("free"===options.plan||!s())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",C("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextControl,{label:C("CSS-Selector","simply-static"),type:"text",help:C("Add the CSS selector of your search element here.","simply-static"),disabled:"free"===options.plan||!s(),value:t.fuse_selector,onChange:e=>{a("fuse_selector",e)}}),(0,e.createElement)(c.__experimentalNumberControl,{label:C("Threshold","simply-static"),isShiftStepEnabled:!0,step:.1,min:.1,max:1,help:C(" A threshold of 0.0 requires a perfect match, a threshold of 1.0 would match anything.","simply-static"),disabled:"free"===options.plan||!s(),value:t.fuse_threshold,placeholder:.1,onChange:e=>{a("fuse_threshold",e)}})))),"algolia"===d&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,C("Algolia API","simply-static"),(0,e.createElement)(m,{title:C("How to add search with the Algolia API","simply-static"),videoUrl:"https://youtu.be/H9PNZSl0KnU"}))),("free"===options.plan||!s())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",C("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextControl,{label:C("Application ID","simply-static"),type:"password",help:C("Add your Algolia App ID.","simply-static"),disabled:"free"===options.plan||!s(),value:t.algolia_app_id,onChange:e=>{a("algolia_app_id",e)}}),(0,e.createElement)(c.TextControl,{label:C("Admin API Key","simply-static"),type:"password",help:C("Add your Algolia Admin API Key.","simply-static"),disabled:"free"===options.plan||!s(),value:t.algolia_admin_api_key,onChange:e=>{a("algolia_admin_api_key",e)}}),(0,e.createElement)(c.TextControl,{label:C("Search-Only API Key","simply-static"),type:"password",help:C("Add your Algolia Search-Only API Key here. This is the only key that will be visible on your static site.","simply-static"),disabled:"free"===options.plan||!s(),value:t.algolia_search_api_key,onChange:e=>{a("algolia_search_api_key",e)}}),(0,e.createElement)(c.TextControl,{label:C("Name for your index","simply-static"),type:"text",help:C("Add your Algolia index name here.","simply-static"),disabled:"free"===options.plan||!s(),value:t.algolia_index,onChange:e=>{a("algolia_index",e)}}),(0,e.createElement)(c.TextControl,{label:C("CSS-Selector","simply-static"),type:"text",help:C("Add the CSS selector of your search element here.","simply-static"),disabled:"free"===options.plan||!s(),value:t.algolia_selector,onChange:e=>{a("algolia_selector",e)}}),(0,e.createElement)("p",null,(0,e.createElement)(c.Notice,{status:"warning",isDismissible:!1},C("If you have multiple search elements with different CSS selectors, separate them by a comma (,) such as: .search-field, .search-field2","simply-static")))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),r&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,C("Settings saved successfully.","simply-static"))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5})),(0,e.createElement)("div",{className:"save-settings"},"pro"===options.plan&&s()&&(0,e.createElement)(c.Button,{onClick:()=>{l(),i(!0),setTimeout((function(){i(!1)}),2e3)},variant:"primary"},C("Save Settings","simply-static"))))},{__:P}=wp.i18n,k=function(){const{settings:t,updateSetting:a,saveSettings:l,settingsSaved:r,setSettingsSaved:i,isPro:s,isStudio:u}=(0,n.useContext)(o),[p,d]=(0,n.useState)(!1),[h,g]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{t.debugging_mode&&d(t.debugging_mode),t.server_cron&&g(t.server_cron)}),[t]),(0,e.createElement)("div",{className:"inner-settings"},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,P("Basic Auth","simply-static"),(0,e.createElement)(m,{title:P("How to set up basic auth","simply-static"),videoUrl:"https://youtu.be/6udSR3_zSOU"}))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("p",null,P("If you've secured WordPress with HTTP Basic Auth you need to specify the username and password to use below.","simply-static")),(0,e.createElement)(c.TextControl,{label:P("Basic Auth Username","simply-static"),autoComplete:"off",type:"text",value:t.http_basic_auth_username,onChange:e=>{a("http_basic_auth_username",e)}}),(0,e.createElement)(c.TextControl,{label:P("Basic Auth Password","simply-static"),type:"password",autoComplete:"off",value:t.http_basic_auth_password,onChange:e=>{a("http_basic_auth_password",e)}}),(0,e.createElement)("p",null,(0,e.createElement)(c.ToggleControl,{label:P("Enable Basic Auth","simply-static"),help:(0,e.createElement)(e.Fragment,null,"free"===options.plan?(0,e.createElement)(e.Fragment,null,P("Automatically setting up Basic Auth requires Simply Static Pro.","simply-static")):(0,e.createElement)(e.Fragment,null,P("Once enabled we will put your entire website behind password protection.","simply-static"))),disabled:"free"===options.plan||!s(),checked:t.http_basic_auth_on,onChange:e=>{a("http_basic_auth_on",e)}}),t.http_basic_auth_on&&(!t.http_basic_auth_username||!t.http_basic_auth_password)&&(0,e.createElement)(c.Notice,{status:"warning",isDismissible:!1},P("Requires Username & Password to work","simply-static"))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),!u()&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,P("Temporary Files","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextControl,{label:P("Temporary Files Directory","simply-static"),type:"text",placeholder:options.temp_files_dir,help:P("Optionally specify the directory to save your temporary files. This directory must exist and be writeable.","simply-static"),value:t.temp_files_dir,onChange:e=>{a("temp_files_dir",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,P("Whitelist Plugins","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextareaControl,{label:P("Whitelist plugins in diagnostics","simply-static"),placeholder:"autoptimize\nwp-search-with-algolia\nwp-rocket",help:P("If you want to exclude certain plugins from the diagnostics check add the plugin slugs here (one per line).","simply-static"),value:t.whitelist_plugins,onChange:e=>{a("whitelist_plugins",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),!u()&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,P("Proxy Setup","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextControl,{label:P("Origin URL","simply-static"),type:"url",help:P("If the URL of your WordPress installation differs from the public-facing URL (Proxy Setup), add the public URL here.","simply-static"),placeholder:options.home,autoComplete:"off",value:t.origin_url,onChange:e=>{a("origin_url",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,P("Debug Log","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.ToggleControl,{label:P("Activate Debug Log","simply-static"),help:P("Enable it to download the debug log from Simply Static -> Generate.","simply-static"),checked:p,onChange:e=>{d(e),a("debugging_mode",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),!u()&&(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,P("Cron","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.ToggleControl,{label:P("Use server-side cron job","simply-static"),help:P("Enable this if you use a server-side cron job instead of the default WP-Cron.","simply-static"),checked:h,onChange:e=>{g(e),a("server_cron",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),r&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,P("Settings saved successfully.","simply-static"))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5})),(0,e.createElement)("div",{className:"save-settings"},(0,e.createElement)(c.Button,{onClick:()=>{l(),i(!0),setTimeout((function(){i(!1)}),2e3)},variant:"primary"},P("Save Settings","simply-static"))))},{__:R}=wp.i18n,O=function({integration:t,settings:a,toggleIntegration:l}){const{isQueuedIntegration:r}=(0,n.useContext)(o);let i=t.active;const s=t.pro,u=t.can_run,p=t.always_active,d=r(t.id);void 0!==a.integrations&&!1!==a.integrations&&(i=a.integrations.indexOf(t.id)>=0);let h="pro"===options.plan||!s;return(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,{className:"ss-integration"},(0,e.createElement)("div",null,(0,e.createElement)("strong",null,t.name||t.id,u&&d&&(0,e.createElement)("em",{class:"ss-text-notice"},R("Requires saving settings","simply-static")),"redirection"===t.id&&(0,e.createElement)(m,{title:R("Automated Redirects with Redirection","simply-static"),videoUrl:"https://youtu.be/sS4BQcZ4dN8"}),"complianz"===t.id&&(0,e.createElement)(m,{title:R("Cookie Consent with Complianz","simply-static"),videoUrl:"https://youtu.be/GPKYtt8A5QE"})),""!=t.description&&[(0,e.createElement)("br",null),t.description]),!u&&(0,e.createElement)("span",{className:"ss-align-right ss-no-shrink"},(0,e.createElement)("em",null,"Missing Plugin"),!h&&(0,e.createElement)("div",null,(0,e.createElement)(c.Button,{variant:"link",href:"https://simplystatic.com/pricing/"},R("Requires Simply Static Pro","simply-static")))),u&&h&&!p&&(0,e.createElement)(c.ToggleControl,{className:"integration-toggle",checked:i,onChange:e=>{l(t.id,e)}}),u&&h&&p&&(0,e.createElement)("em",null,"Always Active"),u&&!h&&(0,e.createElement)(c.Button,{variant:"primary",href:"https://simplystatic.com/pricing/"},R("Get the Pro version","simply-static"))))},{__:T}=wp.i18n,I=function(){const{settings:t,updateSetting:a,saveSettings:l,settingsSaved:r,setSettingsSaved:i,maybeQueueIntegration:s,maybeUnqueueIntegration:u}=(0,n.useContext)(o),p=(e,n)=>{n?(e=>{let n=t.integrations;!1===n&&(n=[]),n.indexOf(e)>=0||(n.push(e),a("integrations",n),s(e))})(e):(e=>{let n=t.integrations;!1===n&&(n=[]);const l=n.indexOf(e);l<0||(n.splice(l,1),a("integrations",n),u(e))})(e)},d=Object.keys(options.integrations).filter((e=>options.integrations[e].can_run&&!options.integrations[e].always_active)),m=Object.keys(options.integrations).filter((e=>!options.integrations[e].can_run&&!options.integrations[e].always_active));return(0,e.createElement)("div",{className:"inner-settings"},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,T("Integrations","simply-static"))),(0,e.createElement)(c.CardBody,null,T("Control Integrations that will be active during the export of the static site.","simply-static"))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),d.map((n=>{const a=options.integrations[n];return(0,e.createElement)(O,{integration:a,settings:t,toggleIntegration:p})})),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),m.map((n=>{const a=options.integrations[n];return(0,e.createElement)(O,{integration:a,settings:t,toggleIntegration:p})})),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),r&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,T("Settings saved successfully.","simply-static"))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5})),(0,e.createElement)("div",{className:"save-settings"},(0,e.createElement)(c.Button,{onClick:()=>{l(),i(!0),setTimeout((function(){i(!1)}),2e3)},variant:"primary"},T("Save Settings","simply-static"))))};function D(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var a,l,r=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(e){l={error:e}}finally{try{a&&!a.done&&(n=r.return)&&n.call(r)}finally{if(l)throw l.error}}return i}"function"==typeof SuppressedError&&SuppressedError;var A,N,F=function(e){var n=e.children;return t().createElement("div",{className:"react-terminal-line"},n)};!function(e,t){void 0===t&&(t={});var n=t.insertAt;if(e&&"undefined"!=typeof document){var a=document.head||document.getElementsByTagName("head")[0],l=document.createElement("style");l.type="text/css","top"===n&&a.firstChild?a.insertBefore(l,a.firstChild):a.appendChild(l),l.styleSheet?l.styleSheet.cssText=e:l.appendChild(document.createTextNode(e))}}("/**\n * Modfied version of [termynal.js](https://github.com/ines/termynal/blob/master/termynal.css).\n *\n * <AUTHOR> Montani <<EMAIL>>\n * @version 0.0.1\n * @license MIT\n */\n .react-terminal-wrapper {\n  width: 100%;\n  background: #252a33;\n  color: #eee;\n  font-size: 18px;\n  font-family: 'Fira Mono', Consolas, Menlo, Monaco, 'Courier New', Courier, monospace;\n  border-radius: 4px;\n  padding: 75px 45px 35px;\n  position: relative;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n }\n\n.react-terminal {\n  overflow: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.react-terminal-wrapper.react-terminal-light {\n  background: #ddd;\n  color: #1a1e24;\n}\n\n.react-terminal-window-buttons {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  display: flex;\n  flex-direction: row;\n  gap: 10px;\n}\n\n.react-terminal-window-buttons button {\n  width: 15px;\n  height: 15px;\n  border-radius: 50%;\n  border: 0;\n}\n\n.react-terminal-window-buttons button.clickable {\n  cursor: pointer;\n}\n\n.react-terminal-window-buttons button.red-btn {\n  background: #d9515d;\n}\n\n.react-terminal-window-buttons button.yellow-btn {\n  background: #f4c025;\n}\n\n.react-terminal-window-buttons button.green-btn {\n  background: #3ec930;\n}\n\n.react-terminal-wrapper:after {\n  content: attr(data-terminal-name);\n  position: absolute;\n  color: #a2a2a2;\n  top: 5px;\n  left: 0;\n  width: 100%;\n  text-align: center;\n  pointer-events: none;\n}\n\n.react-terminal-wrapper.react-terminal-light:after {\n  color: #D76D77;\n}\n\n.react-terminal-line {\n  white-space: pre;\n}\n\n.react-terminal-line:before {\n  /* Set up defaults and ensure empty lines are displayed. */\n  content: '';\n  display: inline-block;\n  vertical-align: middle;\n  color: #a2a2a2;\n}\n\n.react-terminal-light .react-terminal-line:before {\n  color: #D76D77;\n}\n\n.react-terminal-input:before {\n  margin-right: 0.75em;\n  content: '$';\n}\n\n.react-terminal-input[data-terminal-prompt]:before {\n  content: attr(data-terminal-prompt);\n}\n\n.react-terminal-wrapper:focus-within .react-terminal-active-input .cursor {\n  position: relative;\n  display: inline-block;\n  width: 0.55em;\n  height: 1em;\n  top: 0.225em;\n  background: #fff;\n  -webkit-animation: blink 1s infinite;\n          animation: blink 1s infinite;\n}\n\n/* Cursor animation */\n\n@-webkit-keyframes blink {\n  50% {\n      opacity: 0;\n  }\n}\n\n@keyframes blink {\n  50% {\n      opacity: 0;\n  }\n}\n\n.terminal-hidden-input {\n    position: fixed;\n    left: -1000px;\n}\n\n/* .react-terminal-progress {\n  display: flex;\n  margin: .5rem 0;\n}\n\n.react-terminal-progress-bar {\n  background-color: #fff;\n  border-radius: .25rem;\n  width: 25%;\n}\n\n.react-terminal-wrapper.react-terminal-light .react-terminal-progress-bar {\n  background-color: #000;\n} */\n"),(N=A||(A={}))[N.Light=0]="Light",N[N.Dark=1]="Dark";var j=function(n){var a=n.name,l=n.prompt,r=n.height,i=void 0===r?"600px":r,o=n.colorMode,s=n.onInput,c=n.children,u=n.startingInputValue,p=void 0===u?"":u,d=n.redBtnCallback,m=n.yellowBtnCallback,h=n.greenBtnCallback,g=n.scrollToPosition,y=void 0===g||g,f=D((0,e.useState)(""),2),b=f[0],E=f[1],v=D((0,e.useState)(0),2),w=v[0],_=v[1],S=(0,e.useRef)(null);(0,e.useEffect)((function(){E(p.trim())}),[p]),(0,e.useEffect)((function(){var e,t;if(null!=s){var n=[],a=function(e){var t=function(){var t;return null===(t=null==e?void 0:e.querySelector(".terminal-hidden-input"))||void 0===t?void 0:t.focus()};null==e||e.addEventListener("click",t),n.push({terminalEl:e,listener:t})};try{for(var l=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],a=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&a>=e.length&&(e=void 0),{value:e&&e[a++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(document.getElementsByClassName("react-terminal-wrapper")),r=l.next();!r.done;r=l.next())a(r.value)}catch(t){e={error:t}}finally{try{r&&!r.done&&(t=l.return)&&t.call(l)}finally{if(e)throw e.error}}return function(){n.forEach((function(e){e.terminalEl.removeEventListener("click",e.listener)}))}}}),[s]);var C=["react-terminal-wrapper"];return o===A.Light&&C.push("react-terminal-light"),t().createElement("div",{className:C.join(" "),"data-terminal-name":a},t().createElement("div",{className:"react-terminal-window-buttons"},t().createElement("button",{className:(m?"clickable":"")+" red-btn",disabled:!d,onClick:d}),t().createElement("button",{className:(m?"clickable":"")+" yellow-btn",disabled:!m,onClick:m}),t().createElement("button",{className:(h?"clickable":"")+" green-btn",disabled:!h,onClick:h})),t().createElement("div",{className:"react-terminal",style:{height:i}},c,"function"==typeof s&&t().createElement("div",{className:"react-terminal-line react-terminal-input react-terminal-active-input","data-terminal-prompt":l||"$",key:"terminal-line-prompt"},b,t().createElement("span",{className:"cursor",style:{left:w+1+"px"}})),t().createElement("div",{ref:S})),t().createElement("input",{className:"terminal-hidden-input",placeholder:"Terminal Hidden Input",value:b,autoFocus:null!=s,onChange:function(e){E(e.target.value)},onKeyDown:function(e){var t,n;if(s)if("Enter"===e.key)s(b),_(0),E(""),y&&setTimeout((function(){var e;return null===(e=null==S?void 0:S.current)||void 0===e?void 0:e.scrollIntoView({behavior:"auto",block:"nearest"})}),500);else if(["ArrowLeft","ArrowRight","ArrowDown","ArrowUp","Delete"].includes(e.key)){var a=e.currentTarget,l="",r=b.length-(a.selectionStart||0);r=(t=r)>(n=b.length)?n:t<0?0:t,"ArrowLeft"===e.key?(r>b.length-1&&r--,l=b.slice(b.length-1-r)):"ArrowRight"===e.key||"Delete"===e.key?l=b.slice(b.length-r+1):"ArrowUp"===e.key&&(l=b.slice(0));var i=function(e,t){var n=document.createElement("span");n.style.visibility="hidden",n.style.position="absolute",n.style.fontSize=window.getComputedStyle(e).fontSize,n.style.fontFamily=window.getComputedStyle(e).fontFamily,n.innerText=t,document.body.appendChild(n);var a=n.getBoundingClientRect().width;return document.body.removeChild(n),-a}(a,l);_(i)}}}))};const{__:$}=wp.i18n,L=function(){const{isRunning:t,isResumed:a,isPaused:i,blogId:s}=(0,n.useContext)(o),[c,u]=(0,n.useState)([(0,e.createElement)(F,null,"Waiting for new export..")]);function p(){l()({path:"/simplystatic/v1/activity-log?blog_id="+s+"&is_network_admin="+options.is_network,method:"GET"}).then((t=>{var n=JSON.parse(t),a=[];for(var l in n.data){var r=n.data[l].datetime,i=n.data[l].message,o=l.includes("pause")||l.includes("cancel"),s=l.includes("resume");a.push((0,e.createElement)(F,null,"[",r,"] ",(0,e.createElement)("span",{className:`${o?"is-error":""} ${s?"is-success":""}`,dangerouslySetInnerHTML:{__html:i}})))}u(a)}))}return r((()=>{p()}),t?2500:null),(0,n.useEffect)((()=>{t&&!a&&u([(0,e.createElement)(F,null,"Waiting for new export..")]),t&&a&&u([(0,e.createElement)(F,null,"Resuming the export..")]),p()}),[t]),(0,e.createElement)(j,{name:$("Activity Log","simply-static"),height:"250px",colorMode:A.Dark},c)};var H=i(757);const M=function(){const{isRunning:t,blogId:a}=(0,n.useContext)(o),[i,s]=(0,n.useState)([]),[c,u]=(0,n.useState)(!1),[p,d]=(0,n.useState)(25),[m,h]=(0,n.useState)(0),g=[{name:"Code",selector:e=>e.code,sortable:!1,maxWidth:"100px"},{name:"URL",selector:t=>(0,e.createElement)("a",{target:"_blank",href:t.url},t.url),sortable:!1},{name:"Notes",wrap:!0,selector:t=>(0,e.createElement)("span",{dangerouslySetInnerHTML:{__html:t.notes}})}];function y(e,t=!1){var n;((e=null!==(n=e)&&void 0!==n?n:1)!==m||t)&&u(!0),l()({path:`/simplystatic/v1/export-log?page=${e}&per_page=${p}&blog_id=${a}&is_network_admin=${options.is_network}`,method:"GET"}).then((n=>{var a=JSON.parse(n);e!==m||t?(s(a.data),u(!1)):(i.total_static_pages=a.data.total_static_pages,s(i)),h(e)}))}return r((()=>{y()}),t?5e3:null),(0,n.useEffect)((()=>{y(1,!0)}),[t]),(0,e.createElement)("div",{className:"log-table-container"},(0,e.createElement)(H.Ay,{columns:g,data:i.static_pages,pagination:!0,paginationServer:!0,paginationTotalRows:i.total_static_pages,paginationPerPage:25,paginationRowsPerPageOptions:[25,50,100,200],progressPending:c,onChangeRowsPerPage:(e,t)=>{d(e),y(t,!0)},onChangePage:e=>{y(e)}}))},{__:B}=wp.i18n,z=function(){const[t,a]=(0,n.useState)(!1);return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Button,{variant:"primary",href:options.log_file,download:!0,style:{marginRight:"10px"}},B("Download Log","simply-static")),(0,e.createElement)(c.Button,{variant:"secondary",onClick:()=>{l()({path:"/simplystatic/v1/delete-log",method:"POST"}),a(!0),setTimeout((function(){a(!1)}),2e3)}},B("Clear Log","simply-static")),t&&(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,B("Log file cleared.","simply-static"))))))},{__:U}=wp.i18n,W=function(){const{settings:t,blogId:a,setBlogId:l}=(0,n.useContext)(o),[r,i]=(0,n.useState)(""),[s,u]=(0,n.useState)("");return(0,e.createElement)("div",{className:"inner-settings"},!options.is_network&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(L,null),(0,e.createElement)(c.__experimentalSpacer,{margin:5})),(0,e.createElement)(c.Flex,{align:"top"},options.is_network&&(0,e.createElement)(c.FlexItem,{isBlock:!0},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,U("Multisite","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.SelectControl,{label:U("Choose a site to export","simply-static"),value:a,options:options.sites.map((function(e){return{label:`${e.name} (${e.url})`,value:e.blog_id}})),onChange:e=>{l(e),options.sites.some((t=>{t.blog_id===e&&(i(t.settings_url),u(t.activity_log_url))}))}}),r&&(0,e.createElement)("p",null,(0,e.createElement)(c.Button,{isPrimary:!0,href:r},"Switch to Site settings"),(0,e.createElement)(c.Button,{style:{marginLeft:"5px"},isSecondary:!0,href:s},"Check progress"))))),t.debugging_mode&&options.log_file&&!options.is_network&&(0,e.createElement)(c.FlexItem,{isBlock:!0},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,U("Debugging","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(z,null))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)("b",null,U("Export Log","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(M,null))))},{__:G}=wp.i18n,V=function(){const{settings:t,updateSetting:a,saveSettings:r,settingsSaved:i,setSettingsSaved:s,isPro:u}=(0,n.useContext)(o),[p,d]=(0,n.useState)(!1),[h,g]=(0,n.useState)(!1),[y,f]=(0,n.useState)(!1),[b,E]=(0,n.useState)(!1),[v,w]=(0,n.useState)(!1),[_,S]=(0,n.useState)(!1),[C,x]=(0,n.useState)("wp-content"),[P,k]=(0,n.useState)("wp-includes"),[R,O]=(0,n.useState)("wp-content/uploads"),[T,I]=(0,n.useState)("wp-content/plugins"),[D,A]=(0,n.useState)("wp-content/themes"),[N,F]=(0,n.useState)("style"),[j,$]=(0,n.useState)("author"),[L,H]=(0,n.useState)(!1),[M,B]=(0,n.useState)(!1),[z,U]=(0,n.useState)(!1),[W,V]=(0,n.useState)(!1),[q,Y]=(0,n.useState)(!1),[J,K]=(0,n.useState)(!1),[Z,X]=(0,n.useState)(!1),[Q,ee]=(0,n.useState)(!1),[te,ne]=(0,n.useState)(!1),[ae,le]=(0,n.useState)(!1),[re,ie]=(0,n.useState)(!1),[oe,se]=(0,n.useState)(!1),[ce,ue]=(0,n.useState)(!1),[pe,de]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{t.use_minify&&d(t.use_minify),t.minify_html&&g(t.minify_html),t.minify_css&&f(t.minify_css),t.minify_inline_css&&E(t.minify_inline_css),t.minify_js&&w(t.minify_js),t.minify_inline_js&&S(t.minify_inline_js),t.wp_content_directory&&x(t.wp_content_directory),t.wp_includes_directory&&k(t.wp_includes_directory),t.wp_uploads_directory&&O(t.wp_uploads_directory),t.wp_plugins_directory&&I(t.wp_plugins_directory),t.wp_themes_directory&&A(t.wp_themes_directory),t.theme_style_name&&F(t.theme_style_name),t.author_url&&$(t.author_url),t.hide_comments&&U(t.hide_comments),t.hide_version&&V(t.hide_version),t.hide_generator&&K(t.hide_generator),t.hide_prefetch&&Y(t.hide_prefetch),t.hide_rsd&&X(t.hide_rsd),t.hide_emotes&&ee(t.hide_emotes),t.disable_xmlrpc&&ne(t.disable_xmlrpc),t.disable_embed&&le(t.disable_embed),t.disable_db_debug&&ie(t.disable_db_debug),t.disable_wlw_manifest&&se(t.disable_wlw_manifest),t.disable_directory_browsing&&ue(t.disable_directory_browsing)}),[t]),(0,e.createElement)("div",{className:"inner-settings"},(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,G("Minify","simply-static"),(0,e.createElement)(m,{title:G("How to minify HTML, CSS and JavaScript?","simply-static"),videoUrl:"https://youtu.be/52IKv5ai-i4"}))),("free"===options.plan||!u())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",G("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.ToggleControl,{label:G("Minify Files?","simply-static"),help:G(p?"Enable minify files on your static website.":"Don't enable minify files on your static website.","simply-static"),disabled:"free"===options.plan||!u(),checked:p,onChange:e=>{d(e),a("use_minify",e)}}),p&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.ToggleControl,{label:G("Minify HTML","simply-static"),help:G(h?"Minify HTML files.":"Don't minify HTML files.","simply-static"),disabled:"free"===options.plan||!u(),checked:h,onChange:e=>{g(e),a("minify_html",e)}}),h&&(0,e.createElement)(c.ToggleControl,{label:G("Leave quotes inside HTML attributes","simply-static"),help:G("If there are issues with comments or JavaScript when minifying HTML, toggle this ON.","simply-static"),disabled:"free"===options.plan||!u(),checked:t.minify_html_leave_quotes,onChange:e=>{a("minify_html_leave_quotes",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Minify CSS","simply-static"),help:G(y?"Minify CSS files.":"Don't minify CSS files.","simply-static"),disabled:"free"===options.plan||!u(),checked:y,onChange:e=>{f(e),a("minify_css",e)}}),y&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.TextareaControl,{label:G("Exclude Stylesheet URLs","simply-static"),help:G("Exclude URLs from minification (one per line).","simply-static"),disabled:"free"===options.plan||!u(),value:t.minify_css_exclude,onChange:e=>{a("minify_css_exclude",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Minify Inline CSS","simply-static"),help:G(b?"Minify Inline CSS.":"Don't minify Inline CSS.","simply-static"),disabled:"free"===options.plan||!u(),checked:b,onChange:e=>{E(e),a("minify_inline_css",e)}})),(0,e.createElement)(c.ToggleControl,{label:G("Minify JavaScript","simply-static"),help:G(v?"Minify JavaScript files.":"Don't minify JavaScript files.","simply-static"),disabled:"free"===options.plan||!u(),checked:v,onChange:e=>{w(e),a("minify_js",e)}}),v&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.TextareaControl,{label:G("Exclude JavaScript URLs","simply-static"),help:G("Exclude URLs from minification (one per line).","simply-static"),disabled:"free"===options.plan||!u(),value:t.minify_js_exclude,onChange:e=>{a("minify_js_exclude",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Minify Inline JavaScript","simply-static"),help:G(_?"Minify Inline JavaScript.":"Don't minify Inline JavaScript.","simply-static"),disabled:"free"===options.plan||!u(),checked:_,onChange:e=>{S(e),a("minify_inline_js",e)}}))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,G("Image Optimization","simply-static"),(0,e.createElement)(m,{title:G("How to optimize images with ShortPixel?","simply-static"),videoUrl:"https://youtu.be/OIfKcXz3cxY"}))),("free"===options.plan||!u())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",G("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.ToggleControl,{label:G("Optimize Images with ShortPixel?","simply-static"),help:t.shortpixel_enabled?G("Optimize images.","simply-static"):G("Don't optimize images.","simply-static"),disabled:"free"===options.plan||!u(),checked:t.shortpixel_enabled,onChange:e=>{a("shortpixel_enabled",e)}}),t.shortpixel_enabled&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.TextControl,{label:G("ShortPixel API Key","simply-static"),type:"password",value:t.shortpixel_api_key,disabled:"free"===options.plan||!u(),onChange:e=>{a("shortpixel_api_key",e)}}),(0,e.createElement)(c.__experimentalSpacer,{padding:1}),(0,e.createElement)(c.ToggleControl,{label:G("Backup the original images?","simply-static"),checked:t.shortpixel_backup_enabled,disabled:"free"===options.plan||!u(),onChange:e=>{a("shortpixel_backup_enabled",e)}}),t.shortpixel_backup_enabled&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Button,{disabled:pe,onClick:()=>{de(!0),l()({path:"/simplystatic/v1/shortpixel-restore",method:"POST"}).then((e=>{const t=JSON.parse(e);de(!1),alert(t.message)})).catch((e=>{de(!1),alert(e.message)}))},variant:"secondary"},!pe&&G("Restore Original Images","simply-static"),pe&&[(0,e.createElement)(c.Dashicon,{icon:"update spin"}),G("Restoring...","simply-static")]))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,G("Replace","simply-static"),(0,e.createElement)(m,{title:G("How to replace WP default paths","simply-static"),videoUrl:"https://youtu.be/GedyNJJMGaY"}))),("free"===options.plan||!u())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",G("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.TextControl,{label:G("wp-content directory","simply-static"),help:G('Replace the "wp-content" directory.',"simply-static"),disabled:"free"===options.plan||!u(),type:"text",placeholder:"wp-content",value:C,onChange:e=>{a("wp_content_directory",e)}}),(0,e.createElement)(c.TextControl,{label:G("wp-includes directory","simply-static"),help:G('Replace the "wp-includes" directory.',"simply-static"),disabled:"free"===options.plan||!u(),type:"text",placeholder:"wp-includes",value:P,onChange:e=>{a("wp_includes_directory",e)}}),(0,e.createElement)(c.TextControl,{label:G("uploads directory","simply-static"),help:G('Replace the "wp-content/uploads" directory.',"simply-static"),disabled:"free"===options.plan||!u(),type:"text",placeholder:"uploads",value:R,onChange:e=>{O(e),a("wp_uploads_directory",e)}}),(0,e.createElement)(c.TextControl,{label:G("plugins directory","simply-static"),help:G('Replace the "wp-content/plugins" directory.',"simply-static"),disabled:"free"===options.plan||!u(),type:"text",placeholder:"plugins",value:T,onChange:e=>{I(e),a("wp_plugins_directory",e)}}),(0,e.createElement)(c.TextControl,{label:G("themes directory","simply-static"),help:G('Replace the "wp-content/themes" directory.',"simply-static"),disabled:"free"===options.plan||!u(),type:"text",placeholder:"themes",value:D,onChange:e=>{A(e),a("wp_themes_directory",e)}}),(0,e.createElement)(c.__experimentalInputControl,{label:G("Theme style name","simply-static"),help:G("Replace the style.css filename.","simply-static"),disabled:"free"===options.plan||!u(),type:"text",className:"ss-theme-style-name",suffix:".css",placeholder:"style",value:N,onChange:e=>{F(e),a("theme_style_name",e)}}),(0,e.createElement)(c.TextControl,{label:G("Author URL","simply-static"),help:G("Replace the author url.","simply-static"),disabled:"free"===options.plan||!u(),type:"text",placeholder:"author",value:j,onChange:e=>{$(e),a("author_url",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,G("Hide","simply-static"),(0,e.createElement)(m,{title:G("How to hide and disable WP core features","simply-static"),videoUrl:"https://youtu.be/GijIsrfFB8o"}))),("free"===options.plan||!u())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",G("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.ToggleControl,{label:G("Hide HTML Comments","simply-static"),checked:z,disabled:"free"===options.plan||!u(),onChange:e=>{U(e),a("hide_comments",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Hide WordPress Version","simply-static"),checked:W,disabled:"free"===options.plan||!u(),onChange:e=>{V(e),a("hide_version",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Hide WordPress Generator Meta","simply-static"),checked:J,disabled:"free"===options.plan||!u(),onChange:e=>{K(e),a("hide_generator",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Hide DNS Prefetch WordPress link","simply-static"),checked:q,disabled:"free"===options.plan||!u(),onChange:e=>{Y(e),a("hide_prefetch",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Hide RSD Header","simply-static"),checked:Z,disabled:"free"===options.plan||!u(),onChange:e=>{X(e),a("hide_rsd",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Hide Emojis if you don't use them","simply-static"),checked:Q,disabled:"free"===options.plan||!u(),onChange:e=>{ee(e),a("hide_emotes",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Card,null,(0,e.createElement)(c.CardHeader,null,(0,e.createElement)(c.Flex,null,(0,e.createElement)(c.FlexItem,null,(0,e.createElement)("b",null,G("Disable","simply-static"),(0,e.createElement)(m,{title:G("How to hide and disable WP core features","simply-static"),videoUrl:"https://youtu.be/GijIsrfFB8o"}))),("free"===options.plan||!u())&&(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.ExternalLink,{href:"https://simplystatic.com"}," ",G("Requires Simply Static Pro","simply-static"))))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)(c.ToggleControl,{label:G("Disable XML-RPC","simply-static"),checked:te,disabled:"free"===options.plan||!u(),onChange:e=>{ne(e),a("disable_xmlrpc",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Disable Embed Scripts","simply-static"),checked:ae,disabled:"free"===options.plan||!u(),onChange:e=>{le(e),a("disable_embed",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Disable DB Debug in Frontend","simply-static"),checked:re,disabled:"free"===options.plan||!u(),onChange:e=>{ie(e),a("disable_db_debug",e)}}),(0,e.createElement)(c.ToggleControl,{label:G("Disable WLW Manifest Scripts","simply-static"),checked:oe,disabled:"free"===options.plan||!u(),onChange:e=>{se(e),a("disable_wlw_manifest",e)}}))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),i&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1},(0,e.createElement)("p",null,G("Settings saved successfully.","simply-static"))))),(0,e.createElement)(c.__experimentalSpacer,{margin:5})),(0,e.createElement)("div",{className:"save-settings"},"pro"===options.plan&&u()&&(0,e.createElement)(c.Button,{onClick:()=>{r(),s(!0),setTimeout((function(){s(!1)}),2e3)},variant:"primary"},G("Save Settings","simply-static"))))},{__:q}=wp.i18n;function Y({onClose:t,setSelectableEnvironments:a,setSelectedEnvironment:r}){const[i,o]=(0,n.useState)(""),[s,u]=(0,n.useState)(!1);return(0,e.createElement)("div",{className:"ss-environment-form"},(0,e.createElement)(c.TextControl,{label:"Name",onChange:e=>o(e),value:i}),(0,e.createElement)("p",null,q("A new environment will be created with the current configuration.","simply-static")),(0,e.createElement)(c.Flex,{justify:"flex-start"},(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.Button,{variant:"primary",onClick:()=>{u(!0),l()({path:"/simplystatic/v1/environment",method:"POST",data:{title:i}}).then((e=>{let n=Object.keys(e.environments).map((function(t){return{label:e.environments[t],value:t}}));a(n),r(e.current_environment),t()})).catch((e=>{alert(e.message)})).finally((()=>u(!1)))},isBusy:s},q(s?"Creating...":"Create","simply-static"))),(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.Button,{variant:"link",onClick:t},q("Cancel","simply-static")))))}const{__:J}=wp.i18n;function K({onChange:t,current:n,environments:a,disabled:l,onDelete:r}){return(0,e.createElement)(c.Flex,{align:"flex-start"},(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.SelectControl,{disabled:l,value:n,options:a,help:J("Choose an environment or create a new one to configure settings.","simply-static"),onChange:t})),(0,e.createElement)(c.FlexItem,null,(0,e.createElement)(c.Button,{className:"environment-delete-button",variant:"tertiary",label:J("Delete selected environment","simply-static"),showToolTip:!0,size:"small",icon:"trash",disabled:l,onClick:r})))}const{__:Z}=wp.i18n;function X({getSettings:t,isRunning:a}){const[r,i]=(0,n.useState)(""),[o,s]=(0,n.useState)([]),[u,p]=(0,n.useState)(!1),[d,m]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{l()({path:"/simplystatic/v1/environment",method:"GET"}).then((e=>{let t=Object.keys(e.environments).map((function(t){return{label:e.environments[t],value:t}}));s(t),i(e.current_environment)}))}),[]),(0,e.createElement)(e.Fragment,null,(0,e.createElement)("h4",{className:"settings-headline"}," ",Z("Environment","simply-static")),!u&&r&&(0,e.createElement)("p",null,"Current: ",(0,e.createElement)("strong",null,d?Z("Changing ...","simply-static"):o.filter((e=>e.value===r)).pop().label)),!u&&o.length>0&&(0,e.createElement)(K,{onChange:e=>{m(!0),l()({path:"/simplystatic/v1/environment",method:"PUT",data:{version:e}}).then((()=>{t(),i(e)})).catch((e=>alert(e.message))).finally((()=>{m(!1)}))},environments:o,onDelete:()=>{m(!0),l()({path:"/simplystatic/v1/environment",method:"DELETE",data:{version:r}}).then((e=>{t();let n=Object.keys(e.environments).map((function(t){return{label:e.environments[t],value:t}}));s(n),i(e.current_environment)})).catch((e=>alert(e.message))).finally((()=>{m(!1)}))},current:r,disabled:a||d}),!u&&(0,e.createElement)(c.Button,{disabled:a||d,variant:"primary",size:"large",onClick:()=>p(!0)},"Create an Environment"),u&&(0,e.createElement)(Y,{onClose:()=>p(!1),setSelectedEnvironment:i,setSelectableEnvironments:s}))}const{__:Q}=wp.i18n,ee=function(){const{isRunning:t,setIsRunning:a,isResumed:r,setIsResumed:i,isPaused:s,setIsPaused:u,blogId:p,settings:d,updateFromNetwork:m,getSettings:h,passedChecks:y,isPro:b,isStudio:v,canRunIntegration:_,showMobileNav:C,setShowMobileNav:P,isDelayed:R}=(0,n.useContext)(o),[O,T]=(0,n.useState)({activeItem:"/"}),[D,A]=(0,n.useState)(localStorage.getItem("ss-initial-page")?localStorage.getItem("ss-initial-page"):options.initial),[N,F]=(0,n.useState)(!1),[j,$]=(0,n.useState)(!1),[L,H]=(0,n.useState)("current"),[M,B]=(0,n.useState)([]),[z,U]=(0,n.useState)(!1),[G,q]=(0,n.useState)("export");(0,n.useEffect)((()=>{$(t||s);let e=localStorage.getItem("ss-initial-page");if(N||(F(!0),e?(T(e),A(e),localStorage.removeItem("ss-initial-page")):(T(options.initial),A(options.initial))),options.selectable_sites&&!options.is_network&&options.is_multisite){let e=options.selectable_sites.map((function(e){return{label:`${e.name}`,value:e.blog_id}}));e.unshift({label:Q("Use current settings","simply-static"),value:"current"}),B(e)}options.last_export_end&&!t&&q("update")}),[options,t,s]);const Y=()=>{$(!0),i(!1),u(!1),l()({path:"/simplystatic/v1/start-export",method:"POST",data:{blog_id:p,type:G}}).then((e=>{var t=JSON.parse(e);if(500===t.status)return alert(t.message),void $(!1);a(!0)}))},J=()=>{l()({path:"/simplystatic/v1/cancel-export",method:"POST",data:{blog_id:p}}).then((e=>{i(!1),u(!1),a(!1),$(!1)}))},K=()=>{l()({path:"/simplystatic/v1/pause-export",method:"POST",data:{blog_id:p}}).then((e=>{a(!1),i(!1),u(!0)}))},Z=()=>{l()({path:"/simplystatic/v1/resume-export",method:"POST",data:{blog_id:p}}).then((e=>{i(!0),u(!1),a(!0)}))};let ee="";if(Object.keys(options.builds).length){const t=Object.keys(options.builds).map((t=>(0,e.createElement)("option",{value:t},options.builds[t])));t.sort(((e,t)=>e.props.children.localeCompare(t.props.children))),ee=(0,e.createElement)("optgroup",{label:"Builds"},t)}return(0,e.createElement)("div",{className:"plugin-settings-container"},(0,e.createElement)(c.__experimentalNavigatorProvider,{initialPath:D},(0,e.createElement)(c.Flex,null,(0,e.createElement)("a",{onClick:()=>{P(!C)},className:"show-nav"},(0,e.createElement)(c.Dashicon,{icon:"align-center"})," ",Q("Toggle menu","simply-static")),(0,e.createElement)(c.FlexItem,{className:C?"toggle-nav sidebar":"sidebar"},options.is_network?(0,e.createElement)(c.Card,{className:"plugin-nav"},(0,e.createElement)("div",{className:"plugin-logo"},(0,e.createElement)("img",{alt:"Logo",src:options.logo})),"pro"===options.plan&&b()?(0,e.createElement)("p",{className:"version-number"},"Free: ",(0,e.createElement)("b",null,options.version),(0,e.createElement)("br",null),"Pro: ",(0,e.createElement)("b",null,options.version_pro)):(0,e.createElement)("p",{className:"version-number"},"Version: ",(0,e.createElement)("b",null,options.version)),(0,e.createElement)("div",{className:"generate-container "+(j?"generating":"")},!j&&(0,e.createElement)(c.Button,{onClick:()=>{q("export"),Y()},disabled:j||R,className:"/"===O?"is-active-item generate":"generate"},!j&&[(0,e.createElement)(c.Dashicon,{icon:"update"}),Q("Generate","simply-static")],!j&&R>0&&(0,e.createElement)(e.Fragment,null,R,"s"),j&&[(0,e.createElement)(c.Dashicon,{icon:"update spin"}),Q("Generating...","simply-static")]),j&&(0,e.createElement)(e.Fragment,null,!s&&(0,e.createElement)(c.Button,{label:Q("Pause","simply-static"),showToolTip:!0,className:"ss-generate-media-button",onClick:()=>K()},(0,e.createElement)(c.Dashicon,{icon:"controls-pause"})),s&&(0,e.createElement)(c.Button,{label:Q("Resume","simply-static"),showToolTip:!0,className:"ss-generate-media-button",onClick:()=>Z()},(0,e.createElement)(c.Dashicon,{icon:"controls-play"})),(0,e.createElement)(c.Button,{onClick:()=>J(),label:Q("Cancel","simply-static"),className:"ss-generate-cancel-button",showToolTip:!0},(0,e.createElement)(c.Dashicon,{icon:"no"})))),(0,e.createElement)(c.__experimentalSpacer,{margin:5}),(0,e.createElement)(c.Button,{href:"https://simplystatic.com/changelogs/",target:"_blank"},(0,e.createElement)(c.Dashicon,{icon:"editor-ul"})," ",Q("Changelog","simply-static")),(0,e.createElement)(c.Button,{href:"https://docs.simplystatic.com",target:"_blank"},(0,e.createElement)(c.Dashicon,{icon:"admin-links"})," ",Q("Documentation","simply-static")),"free"===options.plan&&(0,e.createElement)(c.Button,{href:"https://simplystatic.com",target:"_blank"},(0,e.createElement)(c.Dashicon,{icon:"admin-site-alt3"}),"Simply Static Pro")):(0,e.createElement)(c.Card,{className:"plugin-nav"},(0,e.createElement)("div",{className:"plugin-logo"},(0,e.createElement)("img",{alt:"Logo",src:options.logo})),"pro"===options.plan&&b()?(0,e.createElement)(e.Fragment,null,v()?(0,e.createElement)("p",{className:"version-number"},"Free: ",(0,e.createElement)("b",null,options.version),(0,e.createElement)("br",null),"Pro: ",(0,e.createElement)("b",null,options.version_pro),(0,e.createElement)("br",null),"Studio: ",(0,e.createElement)("b",null,options.version_studio)):(0,e.createElement)("p",{className:"version-number"},"Free: ",(0,e.createElement)("b",null,options.version),(0,e.createElement)("br",null),"Pro: ",(0,e.createElement)("b",null,options.version_pro))):(0,e.createElement)("p",{className:"version-number"},"Version: ",(0,e.createElement)("b",null,options.version)),(0,e.createElement)("div",{className:"generate-container "+(j?"generating":"")},(0,e.createElement)(c.SelectControl,{className:"generate-type",value:G,disabled:j,onChange:e=>{q(e)}},(0,e.createElement)("option",{value:"export"},Q("Export","simply-static")),"zip"!==d.delivery_method&&"tiiny"!==d.delivery_method&&(0,e.createElement)(e.Fragment,null,"pro"===options.plan&&b()?(0,e.createElement)("option",{value:"update"},Q("Export Changes","simply-static")):(0,e.createElement)("option",{disabled:!0,value:"update"},Q("Export Changes (Requires Simply Static Pro)","simply-static"))),ee),(0,e.createElement)("div",{className:"generate-buttons-container"},!j&&(0,e.createElement)(c.Button,{onClick:()=>{Y()},disabled:j||R,className:"/"===O?"is-active-item generate":"generate"},!j&&[(0,e.createElement)(c.Dashicon,{icon:"update"}),Q("Generate","simply-static")],!j&&R>0&&(0,e.createElement)(e.Fragment,null," ",R,"s"),j&&(0,e.createElement)(c.Dashicon,{icon:"update spin"})),j&&(0,e.createElement)(e.Fragment,null,!s&&(0,e.createElement)(c.Button,{label:Q("Pause","simply-static"),className:"ss-generate-media-button",showToolTip:!0,onClick:()=>K()},(0,e.createElement)(c.Dashicon,{icon:"controls-pause"})),s&&(0,e.createElement)(c.Button,{label:Q("Resume","simply-static"),className:"ss-generate-media-button",showToolTip:!0,onClick:()=>Z()},(0,e.createElement)(c.Dashicon,{icon:"controls-play"})),(0,e.createElement)(c.Button,{onClick:()=>J(),label:Q("Cancel","simply-static"),className:"ss-generate-cancel-button",showToolTip:!0},(0,e.createElement)(c.Dashicon,{icon:"no"}))))),(0,e.createElement)(c.CardBody,null,"pro"===options.plan&&b()&&(0,e.createElement)(e.Fragment,null,!options.is_network&&_("environments")&&(0,e.createElement)(X,{isRunning:t,getSettings:h})),!options.is_network&&options.is_multisite&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)("h4",{className:"settings-headline"}," ",Q("Import","simply-static")),(0,e.createElement)(c.SelectControl,{value:L,options:M,help:Q("Choose a subsite to import settings from.","simply-static"),onChange:e=>{H(e)}}),"current"!==L&&(0,e.createElement)(c.Button,{isPrimary:!0,onClick:()=>{(e=>{m(e),U(!0),setTimeout((function(){U(!1),window.location.reload()}),2e3)})(L)}},Q("Import Settings","simply-static")),z?(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"success",isDismissible:!1,className:"upgrade-network-notice"},(0,e.createElement)("p",null,Q("Settings successfully imported.","simply-static"))))):""),(0,e.createElement)("h4",{className:"settings-headline"}," ",Q("Tools","simply-static")),(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/"),P(!C)},className:"/"===O?"is-active-item generate":"generate",path:"/"},(0,e.createElement)(c.Dashicon,{icon:"update"})," ",Q("Activity Log","simply-static")),(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/diagnostics"),P(!C)},className:"/diagnostics"===O?"is-active-item":"",path:"/diagnostics"},(0,e.createElement)(c.Dashicon,{icon:"bell"})," ",Q("Diagnostics","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("h4",{className:"settings-headline"}," ",Q("Settings","simply-static")),(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/general"),P(!C)},className:"/general"===O?"is-active-item":"",path:"/general"},(0,e.createElement)(c.Dashicon,{icon:"admin-generic"})," ",Q("General","simply-static")),!options.is_network&&!options.hidden_settings.includes("deployment")&&(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/deployment"),P(!C)},className:"/deployment"===O?"is-active-item":"",path:"/deployment"},(0,e.createElement)(c.Dashicon,{icon:"migrate"})," ",Q("Deploy","simply-static")),!options.is_network&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/forms"),P(!C)},className:"/forms"===O?"is-active-item":"",path:"/forms"},(0,e.createElement)(c.Dashicon,{icon:"align-center"})," ",Q("Forms","simply-static")),(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/search"),P(!C)},className:"/search"===O?"is-active-item":"",path:"/search"},(0,e.createElement)(c.Dashicon,{icon:"search"})," ",Q("Search","simply-static")),(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/optimize"),P(!C)},className:"/optimize"===O?"is-active-item":"",path:"/optimize"},(0,e.createElement)(c.Dashicon,{icon:"dashboard"})," ",Q("Optimize","simply-static")))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("h4",{className:"settings-headline"}," ",Q("Advanced","simply-static")),(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/integrations"),P(!C)},className:"/integrations"===O?"is-active-item":"",path:"/integrations"},(0,e.createElement)(c.Dashicon,{icon:"block-default"})," ",Q("Integrations","simply-static")),(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/utilities"),P(!C)},className:"/utilities"===O?"is-active-item":"",path:"/utilities"},(0,e.createElement)(c.Dashicon,{icon:"admin-tools"})," ",Q("Utilities","simply-static")),(0,e.createElement)(c.__experimentalNavigatorButton,{onClick:()=>{T("/debug"),P(!C)},className:"/debug"===O?"is-active-item":"",path:"/debug"},(0,e.createElement)(c.Dashicon,{icon:"editor-help"})," ",Q("Debug","simply-static"))),(0,e.createElement)(c.CardBody,null,(0,e.createElement)("h4",{className:"settings-headline"},"Learn"),(0,e.createElement)(c.Button,{href:"https://docs.simplystatic.com",target:"_blank"},(0,e.createElement)(c.Dashicon,{icon:"admin-links"})," ",Q("Documentation","simply-static")),(0,e.createElement)(c.Button,{href:"https://www.youtube.com/playlist?list=PLcpe8_rNg8U5g1gCOa0Ge6T17f50nSvmg",target:"_blank"},(0,e.createElement)(c.Dashicon,{icon:"format-video"})," ",Q("Video Course","simply-static")),(0,e.createElement)(c.Button,{href:"https://simplystatic.com/tutorials/",target:"_blank"},(0,e.createElement)(c.Dashicon,{icon:"edit"})," ",Q("Tutorials","simply-static")),!v()&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Button,{className:"ss-get-pro",isPrimary:!0,href:"https://simplystatic.com/simply-static-studio/",target:"_blank"},"Try Simply Static Studio"))))),(0,e.createElement)(c.FlexItem,{isBlock:!0,className:C?"":"toggle-nav"},(0,e.createElement)("div",{class:"plugin-settings"},"no"!==y||options.is_network?"":(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(c.Notice,{status:"notice",isDismissible:!1,className:"/"==O?"diagnostics-notice diagnostics-notice-generate":"diagnostics-notice"},(0,e.createElement)("p",null,Q("There are errors in diagnostics that may negatively affect your static export.","simply-static"),(0,e.createElement)("br",null),Q("Please review them and get them fixed to avoid problems.","simply-static")),(0,e.createElement)(c.__experimentalNavigatorButton,{isSecondary:!0,onClick:()=>{T("/diagnostics"),P(!C)},className:"/diagnostics"===O?"is-active-item":"",path:"/diagnostics"},(0,e.createElement)(c.Dashicon,{icon:"editor-help"})," ",Q("Visit Diagnostics","simply-static"))))),"pro"!==options.plan||b()?"":(0,e.createElement)(c.Animate,{type:"slide-in",options:{origin:"top"}},(()=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)(c.Notice,{status:"error",isDismissible:!1,className:"/"==O?"diagnostics-notice diagnostics-notice-generate":"diagnostics-notice"},(0,e.createElement)("p",null,Q("You are using the pro version without a valid license.","simply-static"),(0,e.createElement)("br",null),Q("We have temporarily disabled all the pro features now. Please contact our support to have the problem solved.","simply-static")),(0,e.createElement)(c.Button,{isPrimary:!0,href:"https://simplystatic.com/support/",target:"_blank"},"Contact Support")),(0,e.createElement)(c.__experimentalSpacer,{margin:"5px"})))),"/"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/"},(0,e.createElement)(W,null)),"/diagnostics"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/diagnostics"},(0,e.createElement)(f,null)),"/general"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/general"},(0,e.createElement)(g,null)),"/deployment"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/deployment"},(0,e.createElement)(w,null)),"/forms"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/forms"},(0,e.createElement)(S,null)),"/search"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/search"},(0,e.createElement)(x,null)),"/optimize"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/optimize"},(0,e.createElement)(V,null)),"/utilities"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/utilities"},(0,e.createElement)(E,null)),"/debug"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/debug"},(0,e.createElement)(k,null)),"/integrations"===O&&(0,e.createElement)(c.__experimentalNavigatorScreen,{path:"/integrations"},(0,e.createElement)(I,null)))))))},te=function(){return(0,e.createElement)(s,null,(0,e.createElement)("div",null,(0,e.createElement)(ee,null)))};"simplystatic-settings"===options.screen&&(0,n.createRoot)(document.getElementById("simplystatic-settings")).render((0,e.createElement)(te,null))})()})();