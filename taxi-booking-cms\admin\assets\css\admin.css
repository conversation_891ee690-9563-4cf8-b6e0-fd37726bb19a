/* ===== ADMIN STYLES ===== */

/* Admin Body */
.admin-body {
    background: #f8f9fa;
    font-family: var(--font-primary);
    margin: 0;
    padding: 0;
}

/* Admin Header */
.admin-header {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 1rem 0;
}

.admin-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.admin-logo h1 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-primary);
}

.admin-header-actions {
    display: flex;
    gap: 1rem;
}

/* Admin Layout */
.admin-layout {
    display: flex;
    min-height: calc(100vh - 80px);
    max-width: 1400px;
    margin: 0 auto;
}

/* Admin Sidebar */
.admin-sidebar {
    width: 250px;
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    padding: 2rem 0;
}

.admin-nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.admin-nav-item {
    margin-bottom: 0.5rem;
}

.admin-nav-item a {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.admin-nav-item a:hover {
    background: #f8f9fa;
    color: var(--primary-color);
}

.admin-nav-item.active a {
    background: rgba(44, 90, 160, 0.1);
    color: var(--primary-color);
    border-right-color: var(--primary-color);
}

.admin-nav-item i {
    width: 20px;
    text-align: center;
}

/* Admin Main */
.admin-main {
    flex: 1;
    padding: 2rem;
    background: #f8f9fa;
}

/* Admin Sections */
.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.admin-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.admin-section-header h2 {
    margin: 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-section-header p {
    margin: 0.5rem 0 0 0;
    color: var(--text-secondary);
}

/* Admin Cards */
.admin-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.admin-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.admin-card-header h3 {
    margin: 0;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-card-body {
    padding: 1.5rem;
}

/* Admin Grid */
.admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Admin Table */
.admin-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid #e9ecef;
}

.admin-table td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.admin-table tr:hover {
    background: #f8f9fa;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.pending {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.status-badge.confirmed {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.status-badge.completed {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.status-badge.cancelled {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.status-badge.active {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.status-badge.inactive {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
}

.status-badge.unread {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.status-badge.read {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
}

.status-badge.replied {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.action-btn.edit {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.action-btn.edit:hover {
    background: #3498db;
    color: white;
}

.action-btn.delete {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.action-btn.delete:hover {
    background: #e74c3c;
    color: white;
}

.action-btn.view {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.action-btn.view:hover {
    background: #27ae60;
    color: white;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.stat-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 2rem;
    color: rgba(44, 90, 160, 0.1);
}

/* Filters */
.admin-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.admin-filters select {
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    background: white;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.admin-filters select:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    user-select: none;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checkmark {
    font-weight: 600;
    color: var(--text-primary);
}

/* Recent Activities */
.recent-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: background 0.3s ease;
}

.recent-item:hover {
    background: #f8f9fa;
}

.recent-item:last-child {
    border-bottom: none;
}

.recent-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.recent-icon.booking {
    background: var(--primary-color);
}

.recent-icon.message {
    background: var(--secondary-color);
}

.recent-content {
    flex: 1;
}

.recent-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.recent-meta {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.recent-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    flex-shrink: 0;
}

/* Social Links */
.social-link-item {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.social-link-item input {
    flex: 1;
}

.social-link-remove {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.social-link-remove:hover {
    background: #c0392b;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.3;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

/* Loading State */
.loading-row {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

/* Responsive */
@media (max-width: 1024px) {
    .admin-layout {
        flex-direction: column;
    }
    
    .admin-sidebar {
        width: 100%;
        order: 2;
    }
    
    .admin-main {
        order: 1;
    }
    
    .admin-nav-menu {
        display: flex;
        overflow-x: auto;
        padding: 0 1rem;
    }
    
    .admin-nav-item {
        margin-bottom: 0;
        margin-right: 0.5rem;
        flex-shrink: 0;
    }
    
    .admin-nav-item a {
        padding: 0.75rem 1rem;
        border-right: none;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
    }
    
    .admin-nav-item.active a {
        border-right: none;
        border-bottom-color: var(--primary-color);
    }
}

@media (max-width: 768px) {
    .admin-header-content {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .admin-main {
        padding: 1rem;
    }
    
    .admin-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .admin-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .admin-filters {
        width: 100%;
        justify-content: flex-start;
    }
    
    .admin-table-container {
        overflow-x: auto;
    }
    
    .admin-table {
        min-width: 600px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .admin-header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .admin-header-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .recent-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .recent-time {
        align-self: flex-end;
    }
}
