/* ===== CSS VARIABLES ===== */
:root {
    /* Colors */
    --primary-color: #2c5aa0;
    --secondary-color: #f39c12;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #ecf0f1;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #2c3e50;
    
    --border-color: #dee2e6;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    
    /* Typography */
    --font-primary: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Layout */
    --container-max-width: 1200px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
}

/* ===== RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

ul, ol {
    list-style: none;
}

button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
    transition: var(--transition);
}

input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    border: none;
    outline: none;
}

/* ===== UTILITIES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-lg); }
.mt-4 { margin-top: var(--spacing-xl); }

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #3d6db0);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1e4080, var(--primary-color));
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #e67e22;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

.btn-block {
    width: 100%;
    justify-content: center;
}

/* ===== FORMS ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-actions {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* ===== LOADING ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== HEADER ===== */
.header {
    background: white;
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.top-bar {
    background: var(--bg-dark);
    color: var(--text-light);
    padding: var(--spacing-sm) 0;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    display: flex;
    gap: var(--spacing-lg);
}

.contact-info span {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    color: var(--text-light);
    font-size: var(--font-size-lg);
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--secondary-color);
    transform: translateY(-2px);
}

.main-header {
    padding: var(--spacing-lg) 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo img {
    height: 50px;
    width: auto;
}

.logo h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    gap: var(--spacing-xl);
}

.nav-menu a {
    font-weight: 600;
    color: var(--text-primary);
    padding: var(--spacing-sm) 0;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.mobile-menu-toggle {
    display: none;
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    height: 70vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color), #3d6db0);
    color: white;
    overflow: hidden;
}

.hero-slider {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.hero-slide.active {
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-text p {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2xl);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== SECTIONS ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== SERVICES SECTION ===== */
.services {
    padding: var(--spacing-3xl) 0;
    background: var(--bg-secondary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

/* ===== BOOKING SECTION ===== */
.booking {
    padding: var(--spacing-3xl) 0;
}

.booking-form-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-medium);
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: var(--spacing-3xl) 0;
    background: var(--bg-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    max-width: 1000px;
    margin: 0 auto;
}

.contact-form {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-medium);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--bg-dark);
    color: var(--text-light);
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: var(--text-light);
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--secondary-color);
}

/* ===== FLOATING CONTACTS ===== */
.floating-contacts {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.floating-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    box-shadow: var(--shadow-heavy);
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.floating-btn:hover {
    transform: scale(1.1);
    animation: none;
}

@keyframes pulse {
    0% { box-shadow: var(--shadow-heavy); }
    50% { box-shadow: var(--shadow-heavy), 0 0 0 10px rgba(44, 90, 160, 0.3); }
    100% { box-shadow: var(--shadow-heavy); }
}

/* ===== MODAL ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: var(--transition);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
}

.modal-close:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: var(--spacing-lg);
}

/* ===== NOTIFICATION ===== */
.notification {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: 3000;
    transform: translateX(100%);
    transition: var(--transition);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    background: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-width: 300px;
}

.notification.success .notification-content {
    border-left: 4px solid var(--success-color);
}

.notification.error .notification-content {
    border-left: 4px solid var(--danger-color);
}

.notification.warning .notification-content {
    border-left: 4px solid var(--warning-color);
}

.notification.info .notification-content {
    border-left: 4px solid var(--info-color);
}

.notification-icon {
    font-size: var(--font-size-xl);
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.error .notification-icon {
    color: var(--danger-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.info .notification-icon {
    color: var(--info-color);
}

.notification-message {
    font-weight: 600;
    color: var(--text-primary);
}
