(()=>{var e,t={226:(e,t,n)=>{"use strict";var i=n(296),o=n.n(i);const s=window.wp.domReady;var r=n.n(s);const a=window.wp.i18n,c={init(){this.addSSMessage(),this.addSSMessageInSearchResult()},addSSMessageInSearchResult(){const e=document.getElementById("plugin-filter"),t=document.querySelector(".plugin-install-php .wp-filter-search");if(!e||!t)return;const n=o()((()=>{t.removeEventListener("input",n,{once:!0}),new MutationObserver((()=>{this.addSSMessage()})).observe(e,{childList:!0})}),1e3);t.addEventListener("input",n,{once:!0})},addSSMessage(){for(const e of ssPlugins.SS_PLUGINS){const t=document.querySelector(`.plugin-card.plugin-card-${e}`);if(!t)continue;if(t.classList.contains("ss-extension-card-message"))continue;const n=document.createElement("div"),i=document.createElement("span"),o=document.createElement("span");n.classList.add("ss-extension-card-message"),i.classList.add("ss-logo-icon"),o.classList.add("tooltiptext"),o.append((0,a.__)("This is known to work well with the Simply Static plugin.","simply-static")),n.append(i),n.append(o),t.appendChild(n)}}};r()((()=>{c.init()}))},296:e=>{function t(e,t=100,n={}){if("function"!=typeof e)throw new TypeError(`Expected the first parameter to be a function, got \`${typeof e}\`.`);if(t<0)throw new RangeError("`wait` must not be negative.");const{immediate:i}="boolean"==typeof n?{immediate:n}:n;let o,s,r,a,c;function l(){const n=Date.now()-a;if(n<t&&n>=0)r=setTimeout(l,t-n);else if(r=void 0,!i){const t=o,n=s;o=void 0,s=void 0,c=e.apply(t,n)}}const d=function(...n){if(o&&this!==o)throw new Error("Debounced method called with different contexts.");o=this,s=n,a=Date.now();const d=i&&!r;if(r||(r=setTimeout(l,t)),d){const t=o,n=s;o=void 0,s=void 0,c=e.apply(t,n)}return c};return d.clear=()=>{r&&(clearTimeout(r),r=void 0)},d.flush=()=>{if(!r)return;const t=o,n=s;o=void 0,s=void 0,c=e.apply(t,n),clearTimeout(r),r=void 0},d}e.exports.debounce=t,e.exports=t}},n={};function i(e){var o=n[e];if(void 0!==o)return o.exports;var s=n[e]={exports:{}};return t[e](s,s.exports,i),s.exports}i.m=t,e=[],i.O=(t,n,o,s)=>{if(!n){var r=1/0;for(d=0;d<e.length;d++){for(var[n,o,s]=e[d],a=!0,c=0;c<n.length;c++)(!1&s||r>=s)&&Object.keys(i.O).every((e=>i.O[e](n[c])))?n.splice(c--,1):(a=!1,s<r&&(r=s));if(a){e.splice(d--,1);var l=o();void 0!==l&&(t=l)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[n,o,s]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={826:0,431:0};i.O.j=t=>0===e[t];var t=(t,n)=>{var o,s,[r,a,c]=n,l=0;if(r.some((t=>0!==e[t]))){for(o in a)i.o(a,o)&&(i.m[o]=a[o]);if(c)var d=c(i)}for(t&&t(n);l<r.length;l++)s=r[l],i.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return i.O(d)},n=globalThis.webpackChunksimplystatic_install_plugins=globalThis.webpackChunksimplystatic_install_plugins||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var o=i.O(void 0,[431],(()=>i(226)));o=i.O(o)})();