#button-contact-vr{position:fixed;bottom:0;z-index:99999}#button-contact-vr .button-contact{position:relative;margin-top:-5px}#button-contact-vr .button-contact .phone-vr{position:relative;visibility:visible;background-color:transparent;width:90px;height:90px;cursor:pointer;z-index:11;-webkit-backface-visibility:hidden;-webkit-transform:translateZ(0);transition:visibility .5s;left:0;bottom:0;display:block}.phone-vr-circle-fill{width:65px;height:65px;top:12px;left:12px;position:absolute;box-shadow:0 0 0 0 #c31d1d;background-color:rgba(230,8,8,0.7);border-radius:50%;border:2px solid transparent;-webkit-animation:phone-vr-circle-fill 2.3s infinite ease-in-out;animation:phone-vr-circle-fill 2.3s infinite ease-in-out;transition:all .5s;-webkit-transform-origin:50% 50%;-ms-transform-origin:50% 50%;transform-origin:50% 50%;-webkit-animuiion:zoom 1.3s infinite;animation:zoom 1.3s infinite}.phone-vr-img-circle{background-color:#e60808;width:40px;height:40px;line-height:40px;top:25px;left:25px;position:absolute;border-radius:50%;overflow:hidden;display:flex;justify-content:center;-webkit-animation:phonering-alo-circle-img-anim 1s infinite ease-in-out;animation:phone-vr-circle-fill 1s infinite ease-in-out}.phone-vr-img-circle a{display:block;line-height:37px}.phone-vr-img-circle img{max-height:25px;max-width:27px;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);-moz-transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%);-o-transform:translate(-50%,-50%)}#instagram-vr .phone-vr-circle-fill{background:rgb(17,143,253);background:linear-gradient(160deg,rgba(17,143,253,1) 20%,rgba(188,60,218,1) 50%,rgba(253,223,5,1) 80%);background-size:contain;box-shadow:0 0 0 0 #c840c9;background-color:rgb(79 103 254);border:0}#instagram-vr  .phone-vr-img-circle{background:transparent}#telegram-vr .phone-vr-circle-fill{box-shadow:0 0 0 0 #2c9fd8;background-color:rgb(44 159 216 / 74%)}#telegram-vr .phone-vr-img-circle{background:#2c9fd8}@-webkit-keyframes phone-vr-circle-fill{0%{-webkit-transform:rotate(0) scale(1) skew(1deg)}10%{-webkit-transform:rotate(-25deg) scale(1) skew(1deg)}20%{-webkit-transform:rotate(25deg) scale(1) skew(1deg)}30%{-webkit-transform:rotate(-25deg) scale(1) skew(1deg)}40%{-webkit-transform:rotate(25deg) scale(1) skew(1deg)}50%{-webkit-transform:rotate(0) scale(1) skew(1deg)}100%{-webkit-transform:rotate(0) scale(1) skew(1deg)}}@-webkit-keyframes zoom{0%{transform:scale(.9)}70%{transform:scale(1);box-shadow:0 0 0 15px transparent}100%{transform:scale(.9);box-shadow:0 0 0 0 transparent}}@keyframes zoom{0%{transform:scale(.9)}70%{transform:scale(1);box-shadow:0 0 0 15px transparent}100%{transform:scale(.9);box-shadow:0 0 0 0 transparent}}.phone-bar a{position:absolute;margin-top:-65px;left:30px;z-index:-1;color:#fff;font-size:16px;padding:7px 15px 7px 50px;border-radius:100px;white-space:nowrap}.phone-bar a:hover{opacity:0.8;color:#fff}@media(max-width:736px){.phone-bar{display:none}}#zalo-vr .phone-vr-circle-fill{box-shadow:0 0 0 0 #2196F3;background-color:rgba(33,150,243,0.7)}#zalo-vr .phone-vr-img-circle{background-color:#2196F3}#viber-vr .phone-vr-circle-fill{box-shadow:0 0 0 0 #714497;background-color:rgba(113,68,151,0.8)}#viber-vr .phone-vr-img-circle{background-color:#714497}#contact-vr .phone-vr-circle-fill{box-shadow:0 0 0 0 #2196F3;background-color:rgba(33,150,243,0.7)}#contact-vr .phone-vr-img-circle{background-color:#2196F3}div#whatsapp-vr .phone-vr .phone-vr-circle-fill{box-shadow:0 0 0 0 #1fd744;background-color:rgb(35 217 72 / 70%)}div#whatsapp-vr .phone-vr .phone-vr-img-circle{background:#1cd741}div#whatsapp-vr .phone-vr .phone-vr-img-circle img{max-width:100%;max-height:100%;border-radius:50%}#fanpage-vr img{max-width:35px;max-height:35px}#fanpage-vr .phone-vr-img-circle{background-color:#1877f2}#fanpage-vr .phone-vr-circle-fill{box-shadow:0 0 0 0 rgb(24 119 242 / 65%);background-color:rgb(24 119 242 / 70%)}#gom-all-in-one .button-contact{transition:1.6s all;-moz-transition:1.6s all;-webkit-transition:1.6s all}#button-contact-vr.active #gom-all-in-one .button-contact{margin-left:-100%}#all-in-one-vr .phone-bar{position:absolute;top:50%;transform:translateY(-50%);left:100%;color:#fff;padding:5px 15px 5px 48px;border-radius:50px;margin-left:-64px;width:max-content;cursor:pointer}#popup-showroom-vr,div#popup-form-contact-vr{display:none}#popup-showroom-vr.active,div#popup-form-contact-vr.active{display:block;position:fixed;width:100%;height:100vh;top:0;left:0;z-index:99999}.bg-popup-vr{position:absolute;left:0;top:0;background:rgb(51 51 51 / 50%);width:100%;height:100vh}.content-popup-vr{background:#fff;position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:95%;border-radius:5px;box-shadow:0 0 14px 5px rgb(0 0 0 / 49%);max-width:600px;display:flex}.content-popup-vr input,.content-popup-vr textarea{width:100%;max-height:100px;min-height:38px;border:1px solid #b1b1b1;margin-bottom:10px;padding:0 7px;background:#fff}.content-popup-vr label{width:100%}.content-popup-vr input.wpcf7-form-control.wpcf7-submit{max-width:fit-content;padding:5px 32px 2px;background:#2196f3;border:0;color:#fff;font-size:16px;border-radius:5px;margin:10px auto 0;display:block;box-shadow:0 0 5px 1px rgb(0 0 0 / 29%);font-weight:400;min-height:auto;line-height:30px}.content-popup-vr input.wpcf7-form-control.wpcf7-submit:hover{opacity:0.7}.content-popup-vr .close-popup-vr{font-family:sans-serif;width:23px;height:23px;background:black;position:absolute;top:-10px;right:-10px;color:#fff;text-align:center;line-height:23px;font-size:17px;border-radius:50%;cursor:pointer}.content-popup-vr .close-popup-vr:hover{background:#b50000}.content-popup-vr .content-popup-div-vr{width:100%;padding:25px}.content-popup-vr .content-popup-img-vr{width:100%;max-width:45%;border-radius:10px 0 0 10px;overflow:hidden}#loco-top,#loco-bottom{display:block}#loco-top .content-popup-img-vr,div#popup-form-contact-vr #loco-bottom .content-popup-img-vr{max-width:100%;text-align:center}#contact-showroom.no-event a{pointer-events:none}.content-popup-vr .content-popup-div-vr ul{color:#333;list-style:none;font-size:15px}@media(max-width:673px){div#popup-form-contact-vr .content-popup-vr{display:block}div#popup-form-contact-vr .content-popup-vr .content-popup-img-vr{max-width:100%;display:none}}
#ftiktok-vr .phone-vr-img-circle {background-color: #020202;}
#tiktok-vr .phone-vr-circle-fill {box-shadow: 0 0 0 0 rgb(2 2 2 / 55%);background-color: rgb(2 2 2 / 60%);}
#tiktok-vr  .phone-vr-img-circle img {max-width: 90%;max-height: 90%;}
#tiktok-vr .phone-vr-img-circle {background: #020202;}
#messenger-vr .phone-vr-circle-fill {box-shadow: 0 0 0 0 #6a4ffe;background-color: rgb(106 79 254 / 50%);}
#messenger-vr .phone-vr-img-circle {background-color: #6a4ffe;}
#messenger-vr .phone-vr-img-circle img {max-width: 100%;max-height: 100%;}