# Translation of Development (trunk) in German
# This file is distributed under the same license as the Development (trunk) package.
msgid ""
msgstr ""
"Project-Id-Version: Development (trunk)\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/simply-static\n"
"POT-Creation-Date: 2024-12-13 13:07+0100\n"
"PO-Revision-Date: 2024-12-13 13:38+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de_DE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Poedit 3.5\n"

#: assets/install-plugins/src/index.js:85
msgid "This is known to work well with the Simply Static plugin."
msgstr "Dieses Plugin ist mit Simply Static kompatibel."

#: simply-static.php:27
msgid "Simply Static requires PHP 7.4 or higher."
msgstr "Simply Static benötigt PHP 7.4 oder höher."

#: simply-static.php:73
msgid ""
"You need to update Simply Static Pro to version 1.6.3.2 before continuing to "
"use Simply Static, as we made significant changes requiring an upgrade."
msgstr ""
"Du musst Simply Static Pro auf Version 1.6.3.2 aktualisieren, bevor du "
"Simply Static weiterhin benutzt."

#: simply-static.php:84
msgid ""
"You need to update Simply Static Pro to version  1.6.3.2 before continuing "
"to use Simply Static, as we made significant changes requiring an upgrade."
msgstr ""
"Du musst Simply Static Pro auf Version 1.6.3.2 aktualisieren, bevor du "
"Simply Static weiterhin benutzt."

#: src/admin/build/index.js:252 src/admin/build/index.js:1066
msgid "Activity Log"
msgstr "Aktivitätsprotokoll"

#: src/admin/build/index.js:353
msgid "Changing ..."
msgstr "Wechseln.."

#: src/admin/build/index.js:361
msgid "Environment"
msgstr "Umgebung"

#: src/admin/build/index.js:442
msgid "A new environment will be created with the current configuration."
msgstr "Eine neue Umgebung wird erstellt mit der aktuellen Konfiguration."

#: src/admin/build/index.js:448
msgid "Creating..."
msgstr "Erstelle.."

#: src/admin/build/index.js:448
msgid "Create"
msgstr "Erstellen"

#: src/admin/build/index.js:451
msgid "Cancel"
msgstr "Abbrechen"

#: src/admin/build/index.js:489
msgid "Choose an environment or create a new one to configure settings."
msgstr ""
"Wähle eine Umgebung oder erstelle eine neue mit den aktuellen Einstellungen."

#: src/admin/build/index.js:494
msgid "Delete selected environment"
msgstr "Lösche die ausgewählte Umgebung"

#: src/admin/build/index.js:716
msgid "Requires saving settings"
msgstr "Benötigt das Speichern der Einstellungen"

#: src/admin/build/index.js:717
msgid "Automated Redirects with Redirection"
msgstr "Automatische Weiterleitungen mit Redirection"

#: src/admin/build/index.js:720
msgid "Cookie Consent with Complianz"
msgstr "Cookie Banner mit Complianz"

#: src/admin/build/index.js:727 src/admin/build/index.js:1901
#: src/admin/build/index.js:2050 src/admin/build/index.js:2090
#: src/admin/build/index.js:2158 src/admin/build/index.js:2321
#: src/admin/build/index.js:2373 src/admin/build/index.js:2653
#: src/admin/build/index.js:2669 src/admin/build/index.js:2694
#: src/admin/build/index.js:2732 src/admin/build/index.js:3386
#: src/admin/build/index.js:3471 src/admin/build/index.js:3509
#: src/admin/build/index.js:3593 src/admin/build/index.js:3664
#: src/admin/build/index.js:3779 src/admin/build/index.js:3810
#: src/admin/build/index.js:3865 src/admin/build/index.js:3881
msgid "Requires Simply Static Pro"
msgstr "Erfordert Simply Static Pro"

#: src/admin/build/index.js:736
msgid "Get the Pro version"
msgstr "Hole dir die Pro-Version"

#: src/admin/build/index.js:787
msgid "Download Log"
msgstr "Log herunterladen"

#: src/admin/build/index.js:790
msgid "Clear Log"
msgstr "Log löschen"

#: src/admin/build/index.js:798
msgid "Log file cleared."
msgstr "Log-Datei erfolgreich geleert."

#: src/admin/build/index.js:910
msgid "Use current settings"
msgstr "Nutze aktuelle Einstellungen"

#: src/admin/build/index.js:971 src/admin/build/index.js:1025
msgid "Generate Static Files"
msgstr "Generieren"

#: src/admin/build/index.js:973 src/admin/build/index.js:1027
msgid "Generating..."
msgstr "Generiere.."

#: src/admin/build/index.js:978 src/admin/build/index.js:1032
msgid "Cancel Export"
msgstr "Export abbrechen"

#: src/admin/build/index.js:985
msgid "Changelog"
msgstr "Changelog"

#: src/admin/build/index.js:990 src/admin/build/index.js:1131
msgid "Documentation"
msgstr "Dokumentation"

#: src/admin/build/index.js:1012 src/admin/build/index.js:4043
msgid "Export"
msgstr "Export"

#: src/admin/build/index.js:1014
msgid "Update"
msgstr "Aktualisieren"

#: src/admin/build/index.js:1017
msgid "Update (Requires Simply Static Pro)"
msgstr "Aktualisieren (benötigt Simply Static Pro)"

#: src/admin/build/index.js:1037 src/admin/build/index.js:4056
msgid "Import"
msgstr "Import"

#: src/admin/build/index.js:1040
msgid "Choose a subsite to import settings from."
msgstr "Wähle eine Subsite um Einstellungen zu importieren."

#: src/admin/build/index.js:1049 src/admin/build/index.js:4067
msgid "Import Settings"
msgstr "Import-Einstellungen"

#: src/admin/build/index.js:1058
msgid "Settings successfully imported."
msgstr "Einstellungen erfolgreich importiert."

#: src/admin/build/index.js:1060
msgid "Tools"
msgstr "Tools"

#: src/admin/build/index.js:1072 src/admin/build/index.js:2515
#: src/admin/inc/class-ss-admin-settings.php:96
#: src/admin/inc/class-ss-admin-settings.php:97
msgid "Diagnostics"
msgstr "Diagnose"

#: src/admin/build/index.js:1074 src/admin/inc/class-ss-admin-settings.php:82
#: src/admin/inc/class-ss-admin-settings.php:83 src/class-ss-plugin.php:417
msgid "Settings"
msgstr "Einstellungen"

#: src/admin/build/index.js:1080
msgid "General"
msgstr "Allgemein"

#: src/admin/build/index.js:1086
msgid "Deploy"
msgstr "Deployment"

#: src/admin/build/index.js:1092 src/admin/build/index.js:2651
msgid "Forms"
msgstr "Formulare"

#: src/admin/build/index.js:1098 src/admin/build/index.js:3777
msgid "Search"
msgstr "Suche"

#: src/admin/build/index.js:1104
msgid "Optimize"
msgstr "Optimierung"

#: src/admin/build/index.js:1106
msgid "Advanced"
msgstr "Erweitert"

#: src/admin/build/index.js:1112 src/admin/build/index.js:3171
msgid "Integrations"
msgstr "Integrationen"

#: src/admin/build/index.js:1118
msgid "Utilities"
msgstr "Werkzeuge"

#: src/admin/build/index.js:1124
msgid "Debug"
msgstr "Debugging"

#: src/admin/build/index.js:1136
msgid "Video Course"
msgstr "Video Kurs"

#: src/admin/build/index.js:1141
msgid "Tutorials"
msgstr "Tutorials"

#: src/admin/build/index.js:1159
msgid ""
"There are errors in diagnostics that may negatively affect your static "
"export."
msgstr ""
"Es gibt Fehler in der Diagnose welche möglicherweise deinen Export "
"beeinträchtigen."

#: src/admin/build/index.js:1159
msgid "Please review them and get them fixed to avoid problems."
msgstr "Bitte überprüfe und behebe die Meldungen um Fehlern vorzubeugen."

#: src/admin/build/index.js:1166
msgid "Visit Diagnostics"
msgstr "Diagnose ansehen"

#: src/admin/build/index.js:1175
msgid "You are using the pro version without a valid license."
msgstr "Du nutzt die Pro-Version ohne gültige Lizenz."

#: src/admin/build/index.js:1175
msgid ""
"We have temporarily disabled all the pro features now. Please contact our "
"support to have the problem solved."
msgstr ""
"Wir haben alle Pro-Funktionen vorübergehend deaktiviert. Bitte kontaktiere "
"den Support um das Problem zu beheben."

#: src/admin/build/index.js:1614 src/class-ss-diagnostic.php:55
msgid "Basic Auth"
msgstr "Basic Auth"

#: src/admin/build/index.js:1615
msgid "How to set up basic auth"
msgstr "Wie man Basic Auth konfiguriert"

#: src/admin/build/index.js:1617
msgid ""
"If you've secured WordPress with HTTP Basic Auth you need to specify the "
"username and password to use below."
msgstr ""
"Wenn du deine WordPress-Seite mit HTTP BASIC Auth geschützt hast, trage "
"Nutzername und Password hier ein."

#: src/admin/build/index.js:1618
msgid "Basic Auth Username"
msgstr "Basic Auth Nutzername"

#: src/admin/build/index.js:1626
msgid "Basic Auth Password"
msgstr "Basic Auth Passwort"

#: src/admin/build/index.js:1634
msgid "Enable Basic Auth"
msgstr "Basic Auth aktivieren"

#: src/admin/build/index.js:1635
msgid "Automatically setting up Basic Auth requires Simply Static Pro."
msgstr "Automatische Einrichtung von Basic Auth benötigt Simply Static Pro."

#: src/admin/build/index.js:1635
msgid ""
"Once enabled we will put your entire website behind password protection."
msgstr ""
"Sobald aktiviert, wird deine gesamte WordPress-Seite mit einem Passwort-"
"Schutz versehen."

#: src/admin/build/index.js:1644
msgid "Requires Username & Password to work"
msgstr "Benötigt Nutzername und Passwort"

#: src/admin/build/index.js:1646
msgid "Temporary Files"
msgstr "Temporäre Dateien"

#: src/admin/build/index.js:1647
msgid "Temporary Files Directory"
msgstr "Temporäres Dateiverzeichnis"

#: src/admin/build/index.js:1650
msgid ""
"Optionally specify the directory to save your temporary files. This "
"directory must exist and be writeable."
msgstr ""
"Lege das temporäre Dateiverzeichnis fest. Diese Verzeichnis muss existieren "
"und beschreibbar sein."

#: src/admin/build/index.js:1657
msgid "Whitelist Plugins"
msgstr "Plugins whitelisten"

#: src/admin/build/index.js:1658
msgid "Whitelist plugins in diagnostics"
msgstr "Plugins whitelisten und aus Diagnose-Report ausschließen"

#: src/admin/build/index.js:1660
msgid ""
"If you want to exclude certain plugins from the diagnostics check add the "
"plugin slugs here (one per line)."
msgstr ""
"Wenn du bestimmte Plugins vom Diagnose-Report ausschließen möchtest, trage "
"sie hier ein (ein Plugin pro Zeile)."

#: src/admin/build/index.js:1667
msgid "Proxy Setup"
msgstr "Proxy-Konfiguration"

#: src/admin/build/index.js:1668 src/tasks/class-ss-setup-task.php:74
msgid "Origin URL"
msgstr "Quell-URL"

#: src/admin/build/index.js:1670
msgid ""
"If the URL of your WordPress installation differs from the public-facing URL "
"(Proxy Setup), add the public URL here."
msgstr ""
"Wenn die URL deiner WordPress-Installation von der öffentlichen URL abweicht "
"(Proxy), für die öffentliche URL hier ein."

#: src/admin/build/index.js:1679
msgid "Debug Log"
msgstr "Debug-Log"

#: src/admin/build/index.js:1680
msgid "Activate Debug Log"
msgstr "Debug-Log aktivieren"

#: src/admin/build/index.js:1681
msgid "Enable it to download the debug log from Simply Static -> Generate."
msgstr ""
"Aktiviere den Debug Log um die Datei in Simply Static -> Aktivitätsprotokoll "
"herunterzuladen."

#: src/admin/build/index.js:1689
msgid "Cron"
msgstr "Cron"

#: src/admin/build/index.js:1690
msgid "Use server-side cron job"
msgstr "Nutzer einen server-seitigen Cron-Job"

#: src/admin/build/index.js:1691
msgid ""
"Enable this if you use a server-side cron job instead of the default WP-Cron."
msgstr ""
"Aktiviere diese Option wenn du einen server-seitigen Cron-Job statt dem "
"standard WP-Cron verwendest."

#: src/admin/build/index.js:1707 src/admin/build/index.js:2439
#: src/admin/build/index.js:2764 src/admin/build/index.js:2988
#: src/admin/build/index.js:3199 src/admin/build/index.js:3706
#: src/admin/build/index.js:3939
msgid "Settings saved successfully."
msgstr "Einstellungen erfolgreich gespeichert."

#: src/admin/build/index.js:1714 src/admin/build/index.js:2446
#: src/admin/build/index.js:2449 src/admin/build/index.js:2452
#: src/admin/build/index.js:2771 src/admin/build/index.js:2995
#: src/admin/build/index.js:3206 src/admin/build/index.js:3713
#: src/admin/build/index.js:3946
msgid "Save Settings"
msgstr "Einstellungen speichern"

#: src/admin/build/index.js:1773
msgid "ZIP Archive"
msgstr "ZIP Archiv"

#: src/admin/build/index.js:1776 src/admin/build/index.js:1871
msgid "Local Directory"
msgstr "Lokales Verzeichnis"

#: src/admin/build/index.js:1779 src/admin/build/index.js:2368
msgid "SFTP"
msgstr "SFTP"

#: src/admin/build/index.js:1782 src/admin/build/index.js:1896
msgid "GitHub"
msgstr "GitHub"

#: src/admin/build/index.js:1785
msgid "AWS S3"
msgstr "AWS S3"

#: src/admin/build/index.js:1790 src/admin/build/index.js:2085
msgid "Bunny CDN"
msgstr "Bunny CDN"

#: src/admin/build/index.js:1793 src/admin/build/index.js:2045
msgid "Tiiny.host"
msgstr "Tiiny.host"

#: src/admin/build/index.js:1836 src/admin/build/index.js:2631
msgid "No page selected"
msgstr "Keine Seite ausgewählt"

#: src/admin/build/index.js:1845 src/admin/build/index.js:1863
msgid "Simply Static Studio"
msgstr "Simply Static Studio"

#: src/admin/build/index.js:1852
msgid "Deployment Settings"
msgstr "Deployment Einstellungen"

#: src/admin/build/index.js:1852
msgid ""
"Choose from a variety of deployment methods. Depending on your selection we "
"either provide a ZIP file, export to a local directory or send your files to "
"a remote destination."
msgstr ""
"Wähle eine der unterschiedlichen Deployment-Optionen. Basierend auf deiner "
"Einstellung erhälst du eine Liste von zusätzlichen Optionen."

#: src/admin/build/index.js:1853
msgid "Deployment method"
msgstr "Deployment-Methode"

#: src/admin/build/index.js:1864
msgid "What is Simply Static Studio?"
msgstr "Was ist Simply Static Studio?"

#: src/admin/build/index.js:1866
msgid ""
"The static site hosting platform for your Static Studio powered WordPress "
"websites."
msgstr ""
"Ein statischer Hosting-Service für Websites die auf Static Studio betrieben "
"werden."

#: src/admin/build/index.js:1866
msgid "ZIP"
msgstr "ZIP"

#: src/admin/build/index.js:1867
msgid "How to export a ZIP file"
msgstr "Wie man eine ZIP-Datei exportiert"

#: src/admin/build/index.js:1869
msgid ""
"Get a download link in the activity log once the static export has finished."
msgstr ""
"Du bekommst einen Download-Link im Aktivitätsprotokoll angezeigt sobald der "
"Export abgeschlossen ist."

#: src/admin/build/index.js:1872
msgid "How to deploy to a local directory"
msgstr "Wie man in ein lokales Verzeichnis exportiert"

#: src/admin/build/index.js:1875 src/admin/build/index.js:2907
msgid "Path"
msgstr "Pfad"

#: src/admin/build/index.js:1877
msgid ""
"This is the directory where your static files will be saved. We will create "
"it automatically on the first export if it doesn't exist."
msgstr ""
"Dies ist das Verzeichnis in dem deine statischen Dateien gespeichert werden. "
"Dieses Verzeichnis muss existieren und beschreibbar sein."

#: src/admin/build/index.js:1888 src/admin/build/index.js:2952
msgid "Copied home path"
msgstr "Homeverzeichnis kopiert"

#: src/admin/build/index.js:1888 src/admin/build/index.js:2952
msgid "Copy home path"
msgstr "Homeverzeichnis kopieren"

#: src/admin/build/index.js:1889
msgid "Clear Local Directory"
msgstr "Lokales Verzeichnis leeren"

#: src/admin/build/index.js:1890
msgid "Clear local directory before running an export."
msgstr "Leere lokales Verzeichnis vor nächstem Export."

#: src/admin/build/index.js:1890
msgid "Don't clear local directory before running an export."
msgstr "Leere lokales Verzeichnis nicht vor nächstem Export."

#: src/admin/build/index.js:1897
msgid "How to deploy to a GitHub (2/2)"
msgstr "Wie man zu GitHub exportiert (2/2)"

#: src/admin/build/index.js:1901
msgid ""
"GitHub enables you to export your static website to one of the common static "
"hosting providers like Netlify, Cloudflare Pages or GitHub Pages."
msgstr ""
"GitHub ermöglicht es dir deine statische Seite auf einem der populären "
"statischen Hostinganbieter zu hosten. Beispiele sind Netlify, Cloudflare "
"Pages oder GitHub Pages."

#: src/admin/build/index.js:1902
msgid "Account Type"
msgstr "Account-Typ"

#: src/admin/build/index.js:1904
msgid "Depending on the account type the settings fields will change."
msgstr "Basierend auf deinem Account-Typ werden sich die Einstellungen ändern."

#: src/admin/build/index.js:1907
msgid "Personal"
msgstr "Personal"

#: src/admin/build/index.js:1910 src/admin/build/index.js:1918
msgid "Organization"
msgstr "Organization"

#: src/admin/build/index.js:1920
msgid "Enter the name of your organization."
msgstr "Trage den Namen deiner Organisation ein."

#: src/admin/build/index.js:1927
msgid "Username"
msgstr "Nutzername"

#: src/admin/build/index.js:1929
msgid "Enter your GitHub username."
msgstr "Trage deinen GitHub Nutzernamen ein."

#: src/admin/build/index.js:1936 src/admin/build/index.js:2052
msgid "E-Mail"
msgstr "E-Mail"

#: src/admin/build/index.js:1938
msgid ""
"Enter your GitHub email address. This will be used to commit files to your "
"repository."
msgstr "Trage deine GitHub E-Mail-Adresse ein."

#: src/admin/build/index.js:1945
msgid "Personal Access Token"
msgstr "Personal Access Token"

#: src/admin/build/index.js:1946
msgid "How to prepare your GitHub account"
msgstr "Wie du deinen GitHub Account vorbereitest"

#: src/admin/build/index.js:1950
msgid "You need a personal access token from GitHub. Learn how to get one "
msgstr ""
"Du benötigst deinen Personal Access Token um eine Verbindung zu GitHub "
"aufzubauen "

#: src/admin/build/index.js:1953 src/admin/build/index.js:1974
#: src/admin/build/index.js:2096 src/admin/build/index.js:2108
#: src/admin/build/index.js:2164 src/admin/build/index.js:2176
msgid "here"
msgstr "hier"

#: src/admin/build/index.js:1960
msgid "Repository"
msgstr "Repository"

#: src/admin/build/index.js:1962
msgid ""
"Enter a name for your repository (lowercase without spaces or special "
"characters)."
msgstr ""
"Trage den Namen deines Repositories ein. Dieser Name sollte aus "
"Kleinbuchstaben bestehen und keine Leer-und Sonderzeichen enthalten."

#: src/admin/build/index.js:1971
msgid ""
"Ensure to create the repository and add a readme file to it before running "
"an export as shown in the docs "
msgstr ""
"Stelle sicher das dein Repository ein README file enthält bevor du den "
"Export ausführst "

#: src/admin/build/index.js:1977
msgid "Folder"
msgstr "Verzeichnis"

#: src/admin/build/index.js:1979
msgid ""
"Enter a relative path to a folder if you want to push files under it. "
"Example: for github.com/USER/REPOSITORY/folder1, enter folder1"
msgstr ""
"Füge einen relativen Pfad ein. Beispiel: für github.com/USER/REPOSITORY/"
"folder1 setze folder1 ein"

#: src/admin/build/index.js:1988
msgid ""
"You need to create the repository manually within your organization before "
"connecting it."
msgstr ""
"Du musst das Repository manuell in deinem Konto einrichten bevor du die "
"Verbindung aufbauen kannst."

#: src/admin/build/index.js:1991
msgid "Visiblity"
msgstr "Sichtbarkeit"

#: src/admin/build/index.js:1993
msgid "Decide if you want to make your repository public or private."
msgstr "Entscheide ob dein Repository public oder private sein soll."

#: src/admin/build/index.js:1996
msgid "Public"
msgstr "Public"

#: src/admin/build/index.js:1999
msgid "Private"
msgstr "Private"

#: src/admin/build/index.js:2007
msgid "Branch"
msgstr "Branch"

#: src/admin/build/index.js:2010
msgid ""
"Simply Static automatically uses \"main\" as branch. You may want to modify "
"that for example to gh-pages. for GitHub Pages."
msgstr ""
"Simply Static nutzt automatisch den \"main\" branch. Du kannst den Branch "
"mit dieser Einstellung anpassen."

#: src/admin/build/index.js:2017
msgid "Webhook URL"
msgstr "Webhook URL"

#: src/admin/build/index.js:2019
msgid ""
"Enter your Webhook URL here and Simply Static will send a POST request after "
"all files are commited to GitHub."
msgstr ""
"Trage deine Webhook URL hier ein. Simply Static wird einen POST request an "
"diese URL senden sobald alle Dateien auf GitHub hochgeladen wurden."

#: src/admin/build/index.js:2026
msgid "Throttle Requests"
msgstr "Anfragen pro Sekunde limitieren"

#: src/admin/build/index.js:2027
msgid ""
"Enable this option if you are experiencing issues with the GitHub API rate "
"limit."
msgstr ""
"Aktiviere die Option wenn du Probleme mit deinem GitHub Rate Limit bekommst."

#: src/admin/build/index.js:2035
msgid "Batch size"
msgstr "Batch-Größe"

#: src/admin/build/index.js:2037
msgid ""
"Enter the number of files you want to be processed in a single batch. If "
"current export fails to deploy, lower the number."
msgstr ""
"Trage die Anzahl der Seiten/Dateien ein die pro Durchlauf exportiert werden "
"sollen. Falls es Probleme bei der Übertragung gibt, reduziere die Anzahl."

#: src/admin/build/index.js:2046
msgid "How to deploy to Tiiny.host"
msgstr "Wie man zu Tiiny.host exportiert"

#: src/admin/build/index.js:2050
msgid ""
"Deploying to Tiiny.host is the easiest and fastest deployment option "
"available in Simply Static Pro."
msgstr ""
"Die Übertragung zur Tiiny.host ist die schnellste Deployment-Option in "
"Simply Static Pro."

#: src/admin/build/index.js:2054
msgid ""
"This field is auto-filled with the e-mail address used for activating Simply "
"Static Pro."
msgstr ""
"Dieses Feld wird automatisch mit deiner Admin-E-Mail-Adresse ausgefüllt. Ein "
"Konto wird automatisch bei der ersten Übertragung erstellt."

#: src/admin/build/index.js:2054
msgid "An account will be created automatically on your first deployment."
msgstr "Ein Konto wird automatisch bei der ersten Übertragung erstellt."

#: src/admin/build/index.js:2057
msgid "Subdomain"
msgstr "Subdomain"

#: src/admin/build/index.js:2059
msgid ""
"That's the part before your TLD. Your full URL is the combination of the "
"subdomain plus the domain suffix."
msgstr ""
"Das ist der Part vor deiner Domain. Deine URL ist die Kombination aus "
"Subdomain und Domain-Suffix."

#: src/admin/build/index.js:2066
msgid "Domain Suffix"
msgstr "Domain-Suffix"

#: src/admin/build/index.js:2068
msgid ""
"This defaults to tiiny.site. If you have a custom domain configured in Tiiny."
"host, you can also use  that one."
msgstr "Wenn du noch keine eigene Domain hast, nutzen wir .tiiny.site."

#: src/admin/build/index.js:2075
msgid "Password Protection"
msgstr "Passwort-Schutz"

#: src/admin/build/index.js:2077
msgid ""
"Adding a password will activate password protection on your static site. The "
"website is only visible with the password."
msgstr ""
"Deine statische Website wird mit dem eingetragenen Passwort geschützt und "
"ist erst nach Eingabe erreichbar."

#: src/admin/build/index.js:2086
msgid "How to deploy to Bunny CDN"
msgstr "Wie man zu BunnyCDN exportiert"

#: src/admin/build/index.js:2090
msgid ""
"Bunny CDN is a fast and reliable CDN provider that you can run your static "
"website on."
msgstr ""
"Bunny CDN ist eine schnelle und zuverlässige Option um deine statische Seite "
"zu hosten."

#: src/admin/build/index.js:2091
msgid "Bunny CDN API Key"
msgstr "Bunny CDN API Key"

#: src/admin/build/index.js:2093
msgid ""
"Enter your API Key from Bunny CDN. You can find your API-Key as described "
msgstr "Trage deinen Bunny CDN API Key hier ein "

#: src/admin/build/index.js:2103
msgid "Storage Host"
msgstr "Storage Host"

#: src/admin/build/index.js:2105
msgid ""
"Depending on your location, you have a different storage host. You find out "
"which URL to use "
msgstr ""
"Basierend auf deinem Standort benötigst du womöglich einen anderen Host.  Du "
"findest mehr dazu in der Dokumentation von BunnyCDN "

#: src/admin/build/index.js:2115
msgid "Bunny CDN Access Key"
msgstr "Bunny CDN Access Key"

#: src/admin/build/index.js:2117
msgid ""
"Enter your Acess Key from Bunny CDN. You will find it within your storage "
"zone setttings within FTP & API Access -> Password."
msgstr ""
"Trage deinen Access Key hier ein. Du findest ihn in deinem BunnyCDN Konto "
"unter Storage Zone -> Settings -> FTP & API Access."

#: src/admin/build/index.js:2124
msgid "Pull Zone"
msgstr "Pull Zone"

#: src/admin/build/index.js:2126
msgid ""
"A pull zone is the connection of your CDN to the internet. Simply Static "
"will try to find an existing pull zone with the provided name, if there is "
"none it creates a new pull zone."
msgstr ""
"Pullzones sind vergleichbar mit Domains. Wenn du noch keine Pullzone in "
"BunnyCDN erstellst hast, übernimmt Simply Static das für dich."

#: src/admin/build/index.js:2133
msgid "Storage Zone"
msgstr "Storage Zone"

#: src/admin/build/index.js:2135
msgid ""
"A storage zone contains your static files. Simply Static will try to find an "
"existing storage zone with the provided name, if there is none it creates a "
"new storage zone."
msgstr ""
"Storage Zones sind der physische Speicher für deine statischen Dateien. "
"Simply Static erstellt dies automatisch falls du es noch nicht gemacht hast."

#: src/admin/build/index.js:2142 src/admin/build/index.js:2288
#: src/admin/build/index.js:2358
msgid "Subdirectory"
msgstr "Unterverzeichnis"

#: src/admin/build/index.js:2145
msgid ""
"If you want to transfer the files to a specific subdirectory on your storage "
"zone add the name of that directory here."
msgstr ""
"Wenn du deine statischen Dateien in einem Unterverzeichnis ablegen möchtest, "
"gib den Pfad hier ein."

#: src/admin/build/index.js:2153
msgid "Amazon AWS S3"
msgstr "Amazon AWS S3"

#: src/admin/build/index.js:2154
msgid "How to deploy to Amazon AWS S3"
msgstr "Wie man zu Amazon AWS S3 exportiert"

#: src/admin/build/index.js:2159 src/admin/build/index.js:2322
msgid "Access Key ID"
msgstr "Access Key ID"

#: src/admin/build/index.js:2161
msgid "Enter your Access Key from AWS. Learn how to get one "
msgstr "Trage deinen AWS Access Key hier ein "

#: src/admin/build/index.js:2171 src/admin/build/index.js:2331
msgid "Secret Access Key"
msgstr "Secret Access Key"

#: src/admin/build/index.js:2173
msgid "Enter your Secret Key from AWS. Learn how to get one "
msgstr "Tragen deinen AWS Secret Key hier ein "

#: src/admin/build/index.js:2183
msgid "Region"
msgstr "Region"

#: src/admin/build/index.js:2186
msgid "US East (Ohio)"
msgstr "US East (Ohio)"

#: src/admin/build/index.js:2189
msgid "US East (N. Virginia)"
msgstr "US East (N. Virginia)"

#: src/admin/build/index.js:2192
msgid "US West (N. California)"
msgstr "US West (N. California)"

#: src/admin/build/index.js:2195
msgid "US West (Oregon)"
msgstr "US West (Oregon)"

#: src/admin/build/index.js:2198
msgid "Africa (Cape Town)"
msgstr "Africa (Cape Town)"

#: src/admin/build/index.js:2201
msgid "Asia Pacific (Hong Kong)"
msgstr "Asia Pacific (Hong Kong)"

#: src/admin/build/index.js:2204
msgid "Asia Pacific (Hyderabad)"
msgstr "Asia Pacific (Hyderabad)"

#: src/admin/build/index.js:2207
msgid "Asia Pacific (Jakarta)"
msgstr "Asia Pacific (Jakarta)"

#: src/admin/build/index.js:2210
msgid "Asia Pacific (Melbourne)"
msgstr "Asia Pacific (Melbourne)"

#: src/admin/build/index.js:2213
msgid "Asia Pacific (Mumbai)"
msgstr "Asia Pacific (Mumbai)"

#: src/admin/build/index.js:2216
msgid "Asia Pacific (Osaka)"
msgstr "Asia Pacific (Osaka)"

#: src/admin/build/index.js:2219
msgid "Asia Pacific (Seoul)"
msgstr "Asia Pacific (Seoul)"

#: src/admin/build/index.js:2222
msgid "Asia Pacific (Singapore)"
msgstr "Asia Pacific (Singapore)"

#: src/admin/build/index.js:2225
msgid "Asia Pacific (Sydney)"
msgstr "Asia Pacific (Sydney)"

#: src/admin/build/index.js:2228
msgid "Asia Pacific (Tokyo)"
msgstr "Asia Pacific (Tokyo)"

#: src/admin/build/index.js:2231
msgid "Canada (Central)"
msgstr "Canada (Central)"

#: src/admin/build/index.js:2234
msgid "Europe (Frankfurt)"
msgstr "Europe (Frankfurt)"

#: src/admin/build/index.js:2237
msgid "Europe (Ireland)"
msgstr "Europe (Ireland)"

#: src/admin/build/index.js:2240
msgid "Europe (London)"
msgstr "Europe (London)"

#: src/admin/build/index.js:2243
msgid "Europe (Milan)"
msgstr "Europe (Milan)"

#: src/admin/build/index.js:2246
msgid "Europe (Paris)"
msgstr "Europe (Paris)"

#: src/admin/build/index.js:2249
msgid "Europe (Spain)"
msgstr "Europe (Spain)"

#: src/admin/build/index.js:2252
msgid "Europe (Stockholm)"
msgstr "Europe (Stockholm)"

#: src/admin/build/index.js:2255
msgid "Europe (Zurich)"
msgstr "Europe (Zurich)"

#: src/admin/build/index.js:2258
msgid "Middle East (Bahrain)"
msgstr "Middle East (Bahrain)"

#: src/admin/build/index.js:2261
msgid "Middle East (UAE)"
msgstr "Middle East (UAE)"

#: src/admin/build/index.js:2264
msgid "South America (São Paulo)"
msgstr "South America (São Paulo)"

#: src/admin/build/index.js:2267
msgid "AWS GovCloud (US-East)"
msgstr "AWS GovCloud (US-East)"

#: src/admin/build/index.js:2270
msgid "AWS GovCloud (US-West)"
msgstr "AWS GovCloud (US-West)"

#: src/admin/build/index.js:2279 src/admin/build/index.js:2349
msgid "Bucket"
msgstr "Bucket"

#: src/admin/build/index.js:2281 src/admin/build/index.js:2351
msgid "Add the name of your bucket here."
msgstr "Trage den Namen deines Buckets hier ein."

#: src/admin/build/index.js:2290 src/admin/build/index.js:2360
msgid "Add an optional subdirectory for your bucket"
msgstr "Füge ein optionales Unterverzeichnis ein"

#: src/admin/build/index.js:2297
msgid "Cloudfront Distribution ID"
msgstr "Cloudfront Distribution ID"

#: src/admin/build/index.js:2299
msgid "We automatically invalidate the cache after each export."
msgstr "Wir leeren den Cache automatisch nach jedem statischen Export."

#: src/admin/build/index.js:2306
msgid "Empty bucket before new export?"
msgstr ""
"Sollen wir den Inhalt des Buckets löschen bevor wir die neuen Dateien "
"hochladen?"

#: src/admin/build/index.js:2307
msgid "Clear bucket before new export."
msgstr "Bucket vor neuem Export löschen."

#: src/admin/build/index.js:2307
msgid "Don't clear bucket before new export."
msgstr "Bucket nicht vor neuem Export löschen."

#: src/admin/build/index.js:2316
msgid "S3-compatible Storage"
msgstr "S3-kompatible Speicher"

#: src/admin/build/index.js:2317
msgid "How to deploy to S3 compatible storages?"
msgstr "Wie man zu S3-kompatiblen Speichern exportiert?"

#: src/admin/build/index.js:2324
msgid "Enter your Access Key from your S3 provider."
msgstr "Trage deinen AWS Access Key hier ein."

#: src/admin/build/index.js:2333
msgid "Enter your Secret Key from S3 provider."
msgstr "Tragen deinen AWS Secret Key hier ein."

#: src/admin/build/index.js:2340
msgid "Base URL"
msgstr "Basis URL"

#: src/admin/build/index.js:2342
msgid "Add the base URL of the S3 service."
msgstr "Füge die Basis-URL des S3 Dienstes ein."

#: src/admin/build/index.js:2369
msgid "How to deploy via SFTP"
msgstr "Wie man zu einem SFTP Server exportiert"

#: src/admin/build/index.js:2374 src/admin/build/index.js:2898
msgid "Host"
msgstr "Host"

#: src/admin/build/index.js:2376
msgid "Enter your SFTP host."
msgstr "Trage deinen SFTP Host ein."

#: src/admin/build/index.js:2383
msgid "Port"
msgstr "Port"

#: src/admin/build/index.js:2386
msgid "Enter your SFTP port."
msgstr "Trage deinen SFTP Port ein."

#: src/admin/build/index.js:2392
msgid "SFTP username"
msgstr "SFTP Nutzername"

#: src/admin/build/index.js:2393
msgid "Enter your SFTP username."
msgstr "Trage deinen SFTP Nutzernamen ein."

#: src/admin/build/index.js:2402
msgid "SFTP password"
msgstr "SFTP Passwort"

#: src/admin/build/index.js:2405
msgid "Enter your SFTP password."
msgstr "Trage dein SFTP Passwort ein."

#: src/admin/build/index.js:2411
msgid "SFTP private key"
msgstr "SFTP Private Key"

#: src/admin/build/index.js:2413
msgid ""
"OPTIONAL: This is only required if you need to authenticate via a private "
"key to access your SFTP server."
msgstr ""
"Optional: Dies ist nur erforderlich wenn du dich mit einem privaten "
"Schlüssel statt einem Passwort mit deinem SFTP Server verbinden möchtest."

#: src/admin/build/index.js:2414
msgid ""
"Enter your SFTP private key if you want password.less upload and the server "
"is configured to allow it. You can set it as a constant in wp-config.php by "
"using define('SSP_SFTP_KEY', 'YOUR_KEY')"
msgstr ""
"Füge deinen SFTP Private Key ein falls du dich ohne Passwort bei deinem SFTP "
"server anmelden möchtest. Du kannst den Schlüssel auch in deiner wp-config."
"php definieren mit der Konstante define('SSP_SFTP_KEY', 'YOUR_KEY')"

#: src/admin/build/index.js:2420
msgid "SFTP folder"
msgstr "SFTP Verzeichnis"

#: src/admin/build/index.js:2421
msgid ""
"Leave empty to upload to the default SFTP folder. Enter a folder path where "
"you want the static files to be uploaded to (example: \"uploads\" will "
"upload to uploads folder. \"uploads/new-folder\" will upload files to \"new-"
"folder\"). "
msgstr ""
"Trage den Pfad auf deinem SFTP-Server ein in dem du die Dateien ablegen "
"möchtest. Lasse das Feld leer falls es das Stammverzeichnis sein soll. "

#: src/admin/build/index.js:2469
msgid "Save settings to test"
msgstr "Einstellungen speichern zum testen"

#: src/admin/build/index.js:2469
msgid "Test Deployment"
msgstr "Test-Export"

#: src/admin/build/index.js:2516
msgid "How to use diagnostics"
msgstr "Wie man die Diagnose benutzt"

#: src/admin/build/index.js:2518
msgid ""
"Our diagnostics tool provides detailed insights into your WordPress "
"installation and server configuration and tells you exactly what needs to be "
"optimized to get the most out of Simply Static. Click the button below to "
"get the latest results."
msgstr ""
"Unser Diagnose-Tool zeigt dir alle WordPress-und Serverkonfiguration die "
"nötig sind um Simply Static bestmöglich auszuführen."

#: src/admin/build/index.js:2521
msgid "Reset Diagnostics"
msgstr "Diagnose-Daten zurücksetzen"

#: src/admin/build/index.js:2529
msgid "Diagnostics resetted successfully."
msgstr "Diagnose erfolgreich zurückgesetzt."

#: src/admin/build/index.js:2654
msgid "Use forms?"
msgstr "Formulare benutzen?"

#: src/admin/build/index.js:2655
msgid "Use Forms on your static website."
msgstr "Nutze Formulare auf deiner statischen Website."

#: src/admin/build/index.js:2655
msgid "Don't use forms on your static website."
msgstr "Nutze keine Formulare auf deiner statischen Website."

#: src/admin/build/index.js:2665
msgid "Create a form connection"
msgstr "Erstelle eine Formular-Verbindung"

#: src/admin/build/index.js:2667
msgid "Comments"
msgstr "Kommentare"

#: src/admin/build/index.js:2670
msgid "Use comments?"
msgstr "Kommentare benutzen?"

#: src/admin/build/index.js:2671
msgid "Use comments on your static website."
msgstr "Nutze Kommentare auf deiner statischen Website."

#: src/admin/build/index.js:2671
msgid "Don't use comments on your static website."
msgstr "Nutze keine Kommentare auf deiner statischen Website."

#: src/admin/build/index.js:2679
msgid "Select a redirect page"
msgstr "Wähle eine Weiterleitungs-Seite"

#: src/admin/build/index.js:2681
msgid ""
"The post will be regenerated after comment submission, but it might take a "
"while so its good practice to redirect the visitor."
msgstr ""
"Der Beitrag wird automatisch neu generiert sobald ein Kommentar hinzugefügt "
"wurde. Damit deine Besucher ein direktes Feedback bekommen, solltest du eine "
"Weiterleitungsseite hinterlegen."

#: src/admin/build/index.js:2689
msgid "CORS"
msgstr "CORS"

#: src/admin/build/index.js:2690
msgid "How to deal with CORS"
msgstr "Wie man mit CORS umgeht"

#: src/admin/build/index.js:2694
msgid ""
"When using Forms and Comments in Simply Static Pro you may encounter CORS "
"issues as you make requests from your static website to your original one."
msgstr ""
"Wenn du Formulare oder Kommentare in Simply Static Pro benutzt, wirst du "
"womöglich CORS Probleme haben, da du Anfragen von deiner statischen Seite zu "
"deiner WordPress Website machst."

#: src/admin/build/index.js:2697
msgid ""
"Due to the variety of server setups out there, you may need to make changes "
"on your server."
msgstr ""
"Durch die Menge an unterschiedlichen Server-Setups musst du womöglich "
"Anpassungen an deinem Server vornehmen."

#: src/admin/build/index.js:2700
msgid "Static URL"
msgstr "Statische URL"

#: src/admin/build/index.js:2703
msgid "Add the URL of your static website to allow CORS from it."
msgstr ""
"Trage die URL zu deiner statischen Seite hier ein um CORS Anfragen zu "
"erlauben."

#: src/admin/build/index.js:2710
msgid "Select CORS method"
msgstr "Wähle eine CORS-Methode"

#: src/admin/build/index.js:2712
msgid "Choose one of the methods to allow CORS for your website."
msgstr "Wähle einer der Methoden um CORS Anfragen zu erlauben."

#: src/admin/build/index.js:2727 src/admin/build/index.js:2728
msgid "Embed Dynamic Content (iFrame)"
msgstr "Dynamischen Inhalt einbetten (iFrame)"

#: src/admin/build/index.js:2732
msgid ""
"We replace the HTML of the URLs with an iFrame that embeds the content "
"directly from your WordPress website."
msgstr ""
"Wir ersetzen das HTML der URLs durch ein iFrame, das den Inhalt direkt von "
"Ihrer WordPress-Website einbettet."

#: src/admin/build/index.js:2732
msgid ""
"This way you can use dynamic elements on your static website without the "
"need of a specific integration."
msgstr ""
"Auf diese Weise können Sie dynamische Elemente auf Ihrer statischen Website "
"verwenden, ohne dass eine spezielle Integration erforderlich ist."

#: src/admin/build/index.js:2735
msgid "This requires your WordPress website to be online all the time."
msgstr "Dies erfordert, dass Ihre WordPress-Website ständig online ist."

#: src/admin/build/index.js:2738
msgid "URLs to embed as an iFrame"
msgstr "Als iFrame einzubettende URLs"

#: src/admin/build/index.js:2740
msgid ""
"If you want to embed specific pages from your WordPress website into your "
"static website, add the URLs here (one per line)."
msgstr ""
"Simply Static wird eine statische Kopie jeder Unterseite anlegen die es "
"finden kann und beginnt bei %s. Wenn du zusätzliche Dateien, welche "
"nirgendwo verlinkt sind, hinzufügen möchtest, kannst du dies hier tun. Bitte "
"trage eine URL je Zeile ein."

#: src/admin/build/index.js:2747
msgid "Custom CSS"
msgstr "Benutzerdefiniertes CSS"

#: src/admin/build/index.js:2748
msgid ""
"These styles will only apply to the embedded pages, not your entire website."
msgstr ""
"Diese Stile gelten nur für die eingebetteten Seiten, nicht für Ihre gesamte "
"Website."

#: src/admin/build/index.js:2852 src/admin/build/index.js:2856
msgid "Replacing URLs"
msgstr "URLs ersetzen"

#: src/admin/build/index.js:2853
msgid "How to replace URLs"
msgstr "Wie man URLs ersetzt"

#: src/admin/build/index.js:2855
msgid ""
"When exporting your static site, any links to your WordPress site will be "
"replaced by one of the following: absolute URLs, relative URLs, or URLs "
"contructed for offline use."
msgstr ""
"Wenn du eine statische Seite exportierst, werden alle Links deiner WordPress-"
"Seite durch eine der folgenden Optionen ersetzt: absolute URLs, relative "
"URLs oder konstruierte URLs für die Offline-Speicherung."

#: src/admin/build/index.js:2859
msgid "Absolute URLs"
msgstr "Absolute URLs"

#: src/admin/build/index.js:2862
msgid "Relative Path"
msgstr "Relativer Pfad"

#: src/admin/build/index.js:2865
msgid "Offline Usage"
msgstr "Offline"

#: src/admin/build/index.js:2877
msgid "Scheme"
msgstr "Schema"

#: src/admin/build/index.js:2906
msgid ""
"Convert all URLs for your WordPress site to absolute URLs at the domain "
"specified above."
msgstr "Konvertiere alle URLs deiner WordPress-Seite zu absoluten URLs."

#: src/admin/build/index.js:2915
msgid ""
"Convert all URLs for your WordPress site to relative URLs that will work at "
"any domain."
msgstr "Konvertiere alle URLs deiner WordPress-Seite zu relativen URLs."

#: src/admin/build/index.js:2915
msgid ""
"Optionally specify a path above if you intend to place the files in a "
"subdirectory."
msgstr "Füge ein Unterverzeichnis ein (optional)"

#: src/admin/build/index.js:2918
msgid "Example"
msgstr "Beispiel"

#: src/admin/build/index.js:2918
msgid ""
"enter /path above if you wanted to serve your files at www.example.com/path/"
msgstr ""
"füge /pfad/ ein, falls deine Seite unter www.example.com/path/ erreichbar "
"sein soll"

#: src/admin/build/index.js:2918
msgid ""
"Convert all URLs for your WordPress site so that you can browse the site "
"locally on your own computer without hosting it on a web server."
msgstr ""
"Konvertiert alle deine WordPress-URLs so, dass sie auf deinem lokalen "
"Computer geöffnet werden können. Dies ist nicht für die Verwendung auf einem "
"Webserver geeignet."

#: src/admin/build/index.js:2919
msgid "Force URL replacements"
msgstr "Erweitertes URL-Ersetzen"

#: src/admin/build/index.js:2920
msgid ""
"Replace all occurrences of the WordPress URL with the static URL (includes "
"inline CSS and JS)."
msgstr ""
"Ersetze alle Vorkommnisse deiner WordPress URL mit deiner statischen URL."

#: src/admin/build/index.js:2920
msgid "Replace only occurrences of the WordPress URL that match our tag list."
msgstr ""
"Ersetze alle Vorkommnisse deiner WordPress URL die mit dem Tag matchen."

#: src/admin/build/index.js:2928
msgid "Include"
msgstr "Einschließen"

#: src/admin/build/index.js:2929 src/admin/build/index.js:2968
msgid "Include & Exclude files and pages"
msgstr "Einschließen und Ausschließen von Dateien und Seiten"

#: src/admin/build/index.js:2932
msgid "Additional URLs"
msgstr "Zusätzliche URLs"

#: src/admin/build/index.js:2934
msgid ""
"If you want to create static copies of pages or files that aren't linked to, "
"add the URLs here (one per line)."
msgstr ""
"Simply Static wird eine statische Kopie jeder Unterseite anlegen die es "
"finden kann und beginnt bei %s. Wenn du zusätzliche Dateien, welche "
"nirgendwo verlinkt sind, hinzufügen möchtest, kannst du dies hier tun. Bitte "
"trage eine URL je Zeile ein."

#: src/admin/build/index.js:2940
msgid "Additional Files and Directories"
msgstr "Zusätzliche Dateien und Verzeichnisse"

#: src/admin/build/index.js:2942
msgid ""
"Sometimes you may want to include additional files (such as files referenced "
"via AJAX) or directories. Add the paths to those files or directories here "
"(one per line)."
msgstr ""
"Simply Static wird eine statische Kopie jeder Datei anlegen die es finden "
"kann. Wenn du zusätzliche Dateien, welche nirgendwo verlinkt sind, "
"hinzufügen möchtest, kannst du dies hier tun. Bitte trage eine URL je Zeile "
"ein."

#: src/admin/build/index.js:2955
msgid "Generate 404 Page?"
msgstr "Generiere eine 404-Fehlerseite?"

#: src/admin/build/index.js:2956
msgid "How to manage 404 pages?"
msgstr "Wie verwalte ich 404-Seiten?"

#: src/admin/build/index.js:2959
msgid "Generate a 404 page."
msgstr "Generiere 404-Fehlerseite."

#: src/admin/build/index.js:2959
msgid "Don't generate a 404 page."
msgstr "Keine 404-Fehlerseite generieren."

#: src/admin/build/index.js:2967
msgid "Exclude"
msgstr "Ausschließen"

#: src/admin/build/index.js:2971
msgid "Urls to exclude"
msgstr "URLs zum Ausschließen"

#: src/admin/build/index.js:2973
msgid ""
"Specify URLs (or parts of URLs) you want to exclude from the processing (one "
"per line)."
msgstr ""
"Hier kannst du URLs hinterlegen, die du vom statischen Export ausschließen "
"möchtest (eine pro Zeile)."

#: src/admin/build/index.js:3048
msgid "Multisite"
msgstr "Multisite"

#: src/admin/build/index.js:3049
msgid "Choose a site to export"
msgstr "Wähle eine Seite für den Export"

#: src/admin/build/index.js:3079
msgid "Debugging"
msgstr "Debug-Modus"

#: src/admin/build/index.js:3081
msgid "Export Log"
msgstr "Export Log"

#: src/admin/build/index.js:3171
msgid ""
"Control Integrations that will be active during the export of the static "
"site."
msgstr ""
"Kontrolliere die Integrationen, die während des Exports der statischen Site "
"aktiv sein werden."

#: src/admin/build/index.js:3381
msgid "Minify"
msgstr "Minifizieren"

#: src/admin/build/index.js:3382
msgid "How to minify HTML, CSS and JavaScript?"
msgstr "Wie minimiert man HTML, CSS und JavaScript?"

#: src/admin/build/index.js:3387
msgid "Minify Files?"
msgstr "Dateien minifizieren?"

#: src/admin/build/index.js:3388
msgid "Enable minify files on your static website."
msgstr "Aktiviere Datei-Minifizierung auf deiner statischen Website."

#: src/admin/build/index.js:3388
msgid "Don't enable minify files on your static website."
msgstr "Deaktiviere Datei-Minifizierung auf deiner statischen Website."

#: src/admin/build/index.js:3396
msgid "Minify HTML"
msgstr "HTML minifizieren"

#: src/admin/build/index.js:3397
msgid "Minify HTML files."
msgstr "HTML minifizieren."

#: src/admin/build/index.js:3397
msgid "Don't minify HTML files."
msgstr "HTML nicht minifizieren."

#: src/admin/build/index.js:3405
msgid "Leave quotes inside HTML attributes"
msgstr "Anführungszeichen in HTML-Attributen belassen"

#: src/admin/build/index.js:3406
msgid ""
"If there are issues with comments or JavaScript when minifying HTML, toggle "
"this ON."
msgstr ""
"Wenn beim Minimieren von HTML Probleme mit Kommentaren oder JavaScript "
"auftreten, schalten Sie diese Option ein."

#: src/admin/build/index.js:3413
msgid "Minify CSS"
msgstr "CSS minifizieren"

#: src/admin/build/index.js:3414
msgid "Minify CSS files."
msgstr "CSS minifizieren."

#: src/admin/build/index.js:3414
msgid "Don't minify CSS files."
msgstr "CSS nicht minifizieren."

#: src/admin/build/index.js:3422
msgid "Exclude Stylesheet URLs"
msgstr "Stylesheet-URLs ausschließen"

#: src/admin/build/index.js:3423 src/admin/build/index.js:3449
msgid "Exclude URLs from minification (one per line)."
msgstr "URLs von der Minimierung ausschließen (eine pro Zeile)."

#: src/admin/build/index.js:3430
msgid "Minify Inline CSS"
msgstr "Inline-CSS minifizieren"

#: src/admin/build/index.js:3431
msgid "Minify Inline CSS."
msgstr "Inline-CSS minifizieren."

#: src/admin/build/index.js:3431
msgid "Don't minify Inline CSS."
msgstr "Inline-CSS nicht minifizieren."

#: src/admin/build/index.js:3439
msgid "Minify JavaScript"
msgstr "JavaScript minifizieren"

#: src/admin/build/index.js:3440
msgid "Minify JavaScript files."
msgstr "JavaScript minifizieren."

#: src/admin/build/index.js:3440
msgid "Don't minify JavaScript files."
msgstr "JavaScript nicht minifizieren."

#: src/admin/build/index.js:3448
msgid "Exclude JavaScript URLs"
msgstr "JavaScript-URLs ausschließen"

#: src/admin/build/index.js:3456
msgid "Minify Inline JavaScript"
msgstr "Inline-JavaScript minifizieren"

#: src/admin/build/index.js:3457
msgid "Minify Inline JavaScript."
msgstr "Inline-JavaScript minifizieren."

#: src/admin/build/index.js:3457
msgid "Don't minify Inline JavaScript."
msgstr "Inline-JavaScript nicht minifizieren."

#: src/admin/build/index.js:3466
msgid "Image Optimization"
msgstr "Bild-Optimierung"

#: src/admin/build/index.js:3467
msgid "How to optimize images with ShortPixel?"
msgstr "Wie optimiere ich Bilder mit ShortPixel?"

#: src/admin/build/index.js:3472
msgid "Optimize Images with ShortPixel?"
msgstr "Bilder mit ShortPixel optimieren?"

#: src/admin/build/index.js:3473
msgid "Optimize images."
msgstr "Bilder optimieren."

#: src/admin/build/index.js:3473
msgid "Don't optimize images."
msgstr "Bilder nicht optimieren."

#: src/admin/build/index.js:3480
msgid "ShortPixel API Key"
msgstr "ShortPixel API Key"

#: src/admin/build/index.js:3490
msgid "Backup the original images?"
msgstr "Original-Bilder als Backup speichern?"

#: src/admin/build/index.js:3500
msgid "Restore Original Images"
msgstr "Original-Bilder wiederherstellen"

#: src/admin/build/index.js:3502
msgid "Restoring..."
msgstr "Wiederherstellen.."

#: src/admin/build/index.js:3504
msgid "Replace"
msgstr "Ersetzen"

#: src/admin/build/index.js:3505
msgid "How to replace WP default paths"
msgstr "So ersetzen Sie WP-Standardpfade"

#: src/admin/build/index.js:3510
msgid "wp-content directory"
msgstr "wp-content Verzeichnis"

#: src/admin/build/index.js:3511
msgid "Replace the \"wp-content\" directory."
msgstr "Ersetze das \"wp-content\" Verzeichnis."

#: src/admin/build/index.js:3520
msgid "wp-includes directory"
msgstr "wp-includes Verzeichnis"

#: src/admin/build/index.js:3521
msgid "Replace the \"wp-includes\" directory."
msgstr "Ersetze das \"wp-includes\" Verzeichnis."

#: src/admin/build/index.js:3530
msgid "uploads directory"
msgstr "uploads Verzeichnis"

#: src/admin/build/index.js:3531
msgid "Replace the \"wp-content/uploads\" directory."
msgstr "Ersetze das \"wp-content/uploads\" Verzeichnis."

#: src/admin/build/index.js:3541
msgid "plugins directory"
msgstr "plugins Verzeichnis"

#: src/admin/build/index.js:3542
msgid "Replace the \"wp-content/plugins\" directory."
msgstr "Ersetze das \"wp-content/plugins\" Verzeichnis."

#: src/admin/build/index.js:3552
msgid "themes directory"
msgstr "themes Verzeichnis"

#: src/admin/build/index.js:3553
msgid "Replace the \"wp-content/themes\" directory."
msgstr "Ersetze das \"wp-content/themes\" Verzeichnis."

#: src/admin/build/index.js:3563
msgid "Theme style name"
msgstr "Theme Stylesheet Name"

#: src/admin/build/index.js:3564
msgid "Replace the style.css filename."
msgstr "Ersetze den style.css Dateinamen."

#: src/admin/build/index.js:3576
msgid "Author URL"
msgstr "Autoren-URLs"

#: src/admin/build/index.js:3577
msgid "Replace the author url."
msgstr "Ersetze die Autoren-URLs."

#: src/admin/build/index.js:3588
msgid "Hide"
msgstr "Verstecken"

#: src/admin/build/index.js:3589 src/admin/build/index.js:3660
msgid "How to hide and disable WP core features"
msgstr "So verbergen und deaktivieren Sie WP-Kernfunktionen"

#: src/admin/build/index.js:3594
msgid "Hide REST API URLs"
msgstr "Verstecke Rest API URLs"

#: src/admin/build/index.js:3602
msgid "Hide Style/Script IDs"
msgstr "Verstecke Style/Script IDs"

#: src/admin/build/index.js:3610
msgid "Hide HTML Comments"
msgstr "Verstecke HTML Kommentare"

#: src/admin/build/index.js:3618
msgid "Hide WordPress Version"
msgstr "Verstecke WordPress Version"

#: src/admin/build/index.js:3626
msgid "Hide WordPress Generator Meta"
msgstr "Verstecke WordPress Generator Meta"

#: src/admin/build/index.js:3634
msgid "Hide DNS Prefetch WordPress link"
msgstr "Verstecke DNS Prefetch WordPress Link"

#: src/admin/build/index.js:3642
msgid "Hide RSD Header"
msgstr "Verstecke RSD Header"

#: src/admin/build/index.js:3650
msgid "Hide Emojis if you don't use them"
msgstr "Verstecke Emojis"

#: src/admin/build/index.js:3659
msgid "Disable"
msgstr "Deaktivieren"

#: src/admin/build/index.js:3665
msgid "Disable XML-RPC"
msgstr "Deaktiviere XML-RPC"

#: src/admin/build/index.js:3673
msgid "Disable Embed Scripts"
msgstr "Deaktiviere Embed Scripts"

#: src/admin/build/index.js:3681
msgid "Disable DB Debug in Frontend"
msgstr "Deaktiviere DB Debug"

#: src/admin/build/index.js:3689
msgid "Disable WLW Manifest Scripts"
msgstr "Deaktiviere WLW Manifest Scripts"

#: src/admin/build/index.js:3780
msgid "Use search?"
msgstr "Suche benutzen?"

#: src/admin/build/index.js:3781
msgid "Use search on your static website."
msgstr "Nutze Suche auf deiner statischen Website."

#: src/admin/build/index.js:3781
msgid "Don't use search on your static website."
msgstr "Nutze keine Suche auf deiner statischen Website."

#: src/admin/build/index.js:3789
msgid "Search Type"
msgstr "Suche-Typ"

#: src/admin/build/index.js:3791
msgid ""
"Decide wich search type you want to use. Fuse runs locally based on file and "
"Algolia is an external API service."
msgstr ""
"Entscheide dich für deinen Suche-Typ. Fuse.js nutzt eine lokale JSON Datei "
"und Algolia ist ein externer API-Service."

#: src/admin/build/index.js:3804
msgid "How to select data with meta tags"
msgstr "Wie man Daten aus Metatags selektiert"

#: src/admin/build/index.js:3806
msgid "Targeting for excerpt in the meta description tag."
msgstr "Ziel für das Excerpt im Meta-Description-Tag."

#: src/admin/build/index.js:3806
msgid "Adding such meta in the excerpt field would be:"
msgstr "Ein Beispiel dafür würde wie folgt aussehen:"

#: src/admin/build/index.js:3806
msgid "Targeting for title in the property meta tag."
msgstr "Ziel für den Title im Meta-Description-Tag."

#: src/admin/build/index.js:3806
msgid ""
"If the second item (after | ) is not <code>content</code>, we'll use it as "
"value of that attribute (<code>property=\"og:title\"</code> in this example) "
"and use <code>content</code> for value."
msgstr ""
"Wenn das zweite Element (nach | ) nicht <code>content</code> ist, nutzen wir "
"den Wert dieses Attributs (<code>property=\"og:title\"</code>in diesem "
"Beispiel <code>content</code> als Wert."

#: src/admin/build/index.js:3806
msgid "Caution: Use meta tags that exist everywhere for title."
msgstr "Warnung: Nutze Meta-Tags die überall für den Titel existieren."

#: src/admin/build/index.js:3808
msgid "Indexing"
msgstr "Indexierung"

#: src/admin/build/index.js:3811
msgid "CSS-Selector for Title"
msgstr "CSS-Selector für den Titel"

#: src/admin/build/index.js:3814
msgid "Add the CSS selector which contains the title of the page/post"
msgstr "Trage den CSS-Selector ein der den Titel der Seite enthält."

#: src/admin/build/index.js:3817 src/admin/build/index.js:3830
#: src/admin/build/index.js:3843
msgid "Or meta tags. Click for more information."
msgstr "Oder Meta-Tags. Klicke für mehr Informationen."

#: src/admin/build/index.js:3824
msgid "CSS-Selector for Content"
msgstr "CSS-Selector für den Inhalt"

#: src/admin/build/index.js:3827
msgid "Add the CSS selector which contains the content of the page/post."
msgstr "Trage den CSS-Selector ein der den Inhalt der Seite enthält."

#: src/admin/build/index.js:3837
msgid "CSS-Selector for Excerpt"
msgstr "CSS-Selector für Excerpt"

#: src/admin/build/index.js:3840
msgid "Add the CSS selector which contains the excerpt of the page/post."
msgstr "Trage den CSS-Selector ein der die Kurzbeschreibung der Seite enthält."

#: src/admin/build/index.js:3850
msgid "Exclude URLs"
msgstr "URLs ausschließen"

#: src/admin/build/index.js:3852
msgid ""
"Exclude URLs from indexing (one per line). You can use full URLs, parts of "
"an URL or plain words (like stop words)."
msgstr ""
"Schließe Seiten von der Indexierung aus (ein Eintrag pro Zeile). Du kannst "
"vollständige URLs oder nur Stichwörter verwenden."

#: src/admin/build/index.js:3860
msgid "Fuse.js"
msgstr "Fuse.js"

#: src/admin/build/index.js:3861
msgid "How to add search with FuseJS"
msgstr "So fügen Sie mit FuseJS eine Suche hinzu"

#: src/admin/build/index.js:3866 src/admin/build/index.js:3918
msgid "CSS-Selector"
msgstr "CSS-Selector"

#: src/admin/build/index.js:3868 src/admin/build/index.js:3920
msgid "Add the CSS selector of your search element here."
msgstr "Trage den CSS-Selector deines Such-Felds hier ein."

#: src/admin/build/index.js:3876
msgid "Algolia API"
msgstr "Algolia API"

#: src/admin/build/index.js:3877
msgid "How to add search with the Algolia API"
msgstr "So fügen Sie eine Suche mit der Algolia-API hinzu"

#: src/admin/build/index.js:3882
msgid "Application ID"
msgstr "Application ID"

#: src/admin/build/index.js:3884
msgid "Add your Algolia App ID."
msgstr "Trage deine Algolia App ID hier ein."

#: src/admin/build/index.js:3891
msgid "Admin API Key"
msgstr "Admin API Key"

#: src/admin/build/index.js:3893
msgid "Add your Algolia Admin API Key."
msgstr "Trage deinen Algolia Admin API Key hier ein."

#: src/admin/build/index.js:3900
msgid "Search-Only API Key"
msgstr "Search-Only API Key"

#: src/admin/build/index.js:3902
msgid ""
"Add your Algolia Search-Only API Key here. This is the only key that will be "
"visible on your static site."
msgstr "Trage deinen Algolia Search-only API Key hier ein."

#: src/admin/build/index.js:3909
msgid "Name for your index"
msgstr "Der Name des Indexes"

#: src/admin/build/index.js:3911
msgid "Add your Algolia index name here."
msgstr "Trage den Namen des Suchindexes hier ein."

#: src/admin/build/index.js:3929
msgid ""
"If you have multiple search elements with different CSS selectors, separate "
"them by a comma (,) such as: .search-field, .search-field2"
msgstr ""
"Wenn du mehrere Such-Felder hast, kannst du auch mehrere Selektoren "
"eintragen. Separiere sie mit einem Komma: .search-field, .search-field2"

#: src/admin/build/index.js:4030
msgid "Migrate Settings"
msgstr "Migrationseinstellungen"

#: src/admin/build/index.js:4030
msgid "Migrate all of your settings to Simply Static 3.0"
msgstr "Migriere alle Einstellungen zu Simply Static 3.0"

#: src/admin/build/index.js:4033
msgid "Migrate settings"
msgstr "Einstellungen migrieren"

#: src/admin/build/index.js:4041
msgid "Settings migration successfully."
msgstr "Migration erfolgreich abgeschlossen."

#: src/admin/build/index.js:4044 src/admin/build/index.js:4057
msgid "Export & Import settings"
msgstr "Export & Import von Einstellungen"

#: src/admin/build/index.js:4049
msgid "Export Settings"
msgstr "Export-Einstellungen"

#: src/admin/build/index.js:4054
msgid "Copied!"
msgstr "Kopiert!"

#: src/admin/build/index.js:4054
msgid "Copy export data"
msgstr "Kopiere Export-Daten"

#: src/admin/build/index.js:4059
msgid ""
"Paste in the JSON string you got from your export to import all settings for "
"the plugin."
msgstr ""
"Füge den JSON-String aus dem Export hier ein und importiere deine "
"Einstellungen."

#: src/admin/build/index.js:4075
msgid "Settings imported successfully."
msgstr "Einstellungen erfolgreich importiert."

#: src/admin/build/index.js:4077
msgid "Reset"
msgstr "Zurücksetzen"

#: src/admin/build/index.js:4077
msgid ""
"By clicking the \"Reset Plugin Settings\", you will reset all plugin "
"settings. This can be useful if you want to import a new set of settings or "
"you want a fresh start."
msgstr ""
"Mit einem Klick auf \"Plugin-Einstellungen zurücksetzen\" werden alle Plugin-"
"Einstellungen zurückgesetzt. Dies kann nützlich sein, wenn Sie einen neuen "
"Satz Einstellungen importieren oder einen Neustart durchführen möchten."

#: src/admin/build/index.js:4077
msgid ""
"If you click the \"Reset Database Table\" button instead, you will keep all "
"your settings, and we will only recreate our DB table."
msgstr ""
"Wenn Sie stattdessen auf die Schaltfläche „Datenbanktabelle zurücksetzen“ "
"klicken, bleiben alle Ihre Einstellungen erhalten und wir erstellen nur "
"unsere DB-Tabelle neu."

#: src/admin/build/index.js:4080
msgid "Reset Plugin Settings"
msgstr "Plugin-Einstellungen zurücksetzen"

#: src/admin/build/index.js:4086
msgid "Reset Database Table"
msgstr "Datenbanktabelle zurücksetzen"

#: src/admin/build/index.js:4094
msgid "Settings resetted successfully."
msgstr "Einstellungen erfolgreich zurückgesetzt."

#: src/admin/build/index.js:4102
msgid "Database table resetted successfully."
msgstr "Datenbank-Tabelle erfolgreich zurückgesetzt."

#: src/admin/inc/class-ss-admin-meta.php:51
#: src/admin/inc/class-ss-admin-settings.php:59
#: src/admin/inc/class-ss-admin-settings.php:60
msgid "Simply Static"
msgstr "Simply Static"

#: src/admin/inc/class-ss-admin-meta.php:72
msgid "Export static page"
msgstr "Statische Seite exportieren"

#: src/admin/inc/class-ss-admin-meta.php:74
msgid "Export posts and pages directly with "
msgstr "Exportieren Sie Beiträge und Seiten direkt mit "

#: src/admin/inc/class-ss-admin-settings.php:69
#: src/admin/inc/class-ss-admin-settings.php:70
msgid "Generate"
msgstr "Generieren"

#: src/class-ss-archive-creation-job.php:255
#, php-format
msgid "Done! Finished in %s"
msgstr "Fertig! Fertigstellt in %s"

#: src/class-ss-archive-creation-job.php:384
#, php-format
msgid "An exception occurred: %s"
msgstr "Eine Ausnahme ist eingetreten: %s"

#: src/class-ss-archive-creation-job.php:401 src/class-ss-url-fetcher.php:85
#: src/class-ss-url-fetcher.php:109
#, php-format
msgid "An error occurred: %s"
msgstr "Ein Fehler ist aufgetreten: %s"

#: src/class-ss-archive-creation-job.php:430
#, php-format
msgid "Error: %s"
msgstr "Fehler: %s"

#: src/class-ss-diagnostic.php:54
msgid "PHP Version"
msgstr "PHP Version"

#: src/class-ss-diagnostic.php:56
msgid "php-xml"
msgstr "php-xml"

#: src/class-ss-diagnostic.php:57
msgid "cURL"
msgstr "cURL"

#: src/class-ss-diagnostic.php:60
msgid "Permalinks"
msgstr "Permalinks"

#: src/class-ss-diagnostic.php:61
msgid "Indexable"
msgstr "Indexierbar"

#: src/class-ss-diagnostic.php:62
msgid "Caching"
msgstr "Caching"

#: src/class-ss-diagnostic.php:63
msgid "WP-CRON"
msgstr "WP-CRON"

#: src/class-ss-diagnostic.php:67
msgid "Temp dir readable"
msgstr "Temporäres Verzeichnis lesbar"

#: src/class-ss-diagnostic.php:68
msgid "Temp dir writeable"
msgstr "Temporäres Verzeichnis beschreibbar"

#: src/class-ss-diagnostic.php:71
msgid "DELETE"
msgstr "DELETE"

#: src/class-ss-diagnostic.php:72
msgid "INSERT"
msgstr "INSERT"

#: src/class-ss-diagnostic.php:73
msgid "SELECT"
msgstr "SELECT"

#: src/class-ss-diagnostic.php:74
msgid "CREATE"
msgstr "CREATE"

#: src/class-ss-diagnostic.php:75
msgid "ALTER"
msgstr "ALTER"

#: src/class-ss-diagnostic.php:76
msgid "DROP"
msgstr "DROP"

#: src/class-ss-diagnostic.php:81
msgid "Destination URL"
msgstr "Ziel-URL"

#: src/class-ss-diagnostic.php:85
msgid "Local Dir"
msgstr "Lokales Verzeichnis"

#: src/class-ss-diagnostic.php:128
msgid "No incompatible plugins are active on your website!"
msgstr "Auf Ihrer Website sind keine inkompatiblen Plugins aktiv!"

#: src/class-ss-diagnostic.php:129
#, php-format
msgid "%d incompatible plugins are active"
msgstr "%d inkompatible Plugins sind aktiv"

#: src/class-ss-diagnostic.php:164
#, php-format
msgid "Destination URL %s is valid"
msgstr "Ziel-URL %s ist valide"

#: src/class-ss-diagnostic.php:165
#, php-format
msgid "Destination URL %s is not valid"
msgstr "Ziel-URL %s ist nicht valide"

#: src/class-ss-diagnostic.php:175
msgid "Is a valid URL."
msgstr "Ist eine valide URL."

#: src/class-ss-diagnostic.php:176
msgid "Is not a valid URL."
msgstr "Ist keine valide URL."

#: src/class-ss-diagnostic.php:183
msgid "Not a valid path"
msgstr "Kein valider Pfad"

#: src/class-ss-diagnostic.php:186
msgid "Not readable"
msgstr "Nicht lesbar"

#: src/class-ss-diagnostic.php:194
#, php-format
msgid "Additional File/Dir %s is valid"
msgstr "Zusätzliche Datei/Verzeichnis %s ist valide"

#: src/class-ss-diagnostic.php:202
msgid "WordPress permalink structure is set"
msgstr "WordPress Permalinks sind gesetzt"

#: src/class-ss-diagnostic.php:203
msgid "WordPress permalink structure is not set"
msgstr "WordPress Permalinks sind nicht gesetzt"

#: src/class-ss-diagnostic.php:210
msgid "Discourage search engines from indexing this site is disabled"
msgstr ""
"Suchmaschinen davon abhalten, diese Site zu indizieren, ist deaktiviert"

#: src/class-ss-diagnostic.php:211
msgid "Discourage search engines from indexing this site is enabled"
msgstr "Suchmaschinen davon abhalten, diese Site zu indizieren ist aktiviert"

#: src/class-ss-diagnostic.php:224
msgid "WordPress cron is available and running"
msgstr "WP-Cron ist verfügbar und läuft"

#: src/class-ss-diagnostic.php:225
msgid "WordPress cron is not available and not running"
msgstr "Es gibt ein Problem mit WP-Cron"

#: src/class-ss-diagnostic.php:234
msgid "Caching is disabled, great!"
msgstr "Caching ist deaktiviert, super!"

#: src/class-ss-diagnostic.php:235
msgid "Please disable caching before running a static export"
msgstr ""
"Bitte deaktivieren Sie das Caching, bevor Sie einen statischen Export "
"ausführen"

#: src/class-ss-diagnostic.php:241 src/class-ss-diagnostic.php:247
#: src/class-ss-diagnostic.php:253 src/class-ss-diagnostic.php:259
#: src/class-ss-diagnostic.php:265 src/class-ss-diagnostic.php:271
#: src/class-ss-diagnostic.php:277 src/class-ss-diagnostic.php:283
#: src/class-ss-diagnostic.php:289 src/class-ss-diagnostic.php:295
#: src/class-ss-diagnostic.php:301 src/class-ss-diagnostic.php:307
#, php-format
msgid "Please disable caching (%s) before running a static export."
msgstr ""
"Bitte deaktivieren Sie das Caching (%s), bevor Sie einen statischen Export "
"ausführen."

#: src/class-ss-diagnostic.php:395
#, php-format
msgid "%s is not compatible with Simply Static."
msgstr "%s ist nicht mit Simply Static kompatibel."

#: src/class-ss-diagnostic.php:404
#, php-format
msgid "Web server can read from Temp Files Directory: %s"
msgstr ""
"Webserver Dateien aus dem temporären Verzeichnis %s können gelesen werden"

#: src/class-ss-diagnostic.php:405
#, php-format
msgid "Web server can't read from Temp Files Directory: %s"
msgstr ""
"Webserver Dateien aus dem temporären Verzeichnis %s können nicht gelesen "
"werden"

#: src/class-ss-diagnostic.php:414
#, php-format
msgid "Web server can write to Temp Files Directory: %s"
msgstr "Webserver kann temporäre Dateien in %s erstellen"

#: src/class-ss-diagnostic.php:415
#, php-format
msgid "Web server can't write to Temp Files Directory: %s"
msgstr "Webserver kann keine temporäre Dateien in %s erstellen"

#: src/class-ss-diagnostic.php:424
#, php-format
msgid "Web server can write to Local Directory: %s"
msgstr "Webserver kann ins lokale Verzeichnis %s schreiben"

#: src/class-ss-diagnostic.php:425
#, php-format
msgid "Web server can not write to Local Directory: %s"
msgstr "Webserver kann nicht ins lokale Verzeichnis %s schreiben"

#: src/class-ss-diagnostic.php:432
msgid "MySQL user has DELETE privilege"
msgstr "Prüfe ob der MySQL-Benutzer DELETE Rechte besitzt"

#: src/class-ss-diagnostic.php:433
msgid "MySQL user has no DELETE privilege"
msgstr "Prüfe ob der MySQL-Benutzer DELETE Rechte besitzt"

#: src/class-ss-diagnostic.php:440
msgid "MySQL user has INSERT privilege"
msgstr "Prüfe ob der MySQL-Benutzer NSERT Rechte besitzt"

#: src/class-ss-diagnostic.php:441
msgid "MySQL user has no INSERT privilege"
msgstr "Prüfe ob der MySQL-Benutzer INSERT Rechte besitzt"

#: src/class-ss-diagnostic.php:448
msgid "MySQL user has SELECT privilege"
msgstr "Prüfe ob der MySQL-Benutzer SELECT Rechte besitzt"

#: src/class-ss-diagnostic.php:449
msgid "MySQL user has no SELECT privilege"
msgstr "Prüfe ob der MySQL-Benutzer SELECT Rechte besitzt"

#: src/class-ss-diagnostic.php:456
msgid "MySQL user has CREATE privilege"
msgstr "Prüfe ob der MySQL-Benutzer CREATE Rechte besitzt"

#: src/class-ss-diagnostic.php:457
msgid "MySQL user has no CREATE privilege"
msgstr "Prüfe ob der MySQL-Benutzer CREATE Rechte besitzt"

#: src/class-ss-diagnostic.php:464
msgid "MySQL user has ALTER privilege"
msgstr "Prüfe ob der MySQL-Benutzer ALTER Rechte besitzt"

#: src/class-ss-diagnostic.php:465
msgid "MySQL user has no ALTER privilege"
msgstr "Prüfe ob der MySQL-Benutzer ALTER Rechte besitzt"

#: src/class-ss-diagnostic.php:472
msgid "MySQL user has DROP privilege"
msgstr "Prüfe ob der MySQL-Benutzer DROP Rechte besitzt"

#: src/class-ss-diagnostic.php:473
msgid "MySQL user has no DROP privilege"
msgstr "Prüfe ob der MySQL-Benutzer DROP Rechte besitzt"

#: src/class-ss-diagnostic.php:480
#, php-format
msgid "PHP version is >= %s"
msgstr "PHP Version >= %s"

#: src/class-ss-diagnostic.php:481
#, php-format
msgid "PHP version < %s"
msgstr "PHP Version < %s"

#: src/class-ss-diagnostic.php:488
msgid "php-xml is available"
msgstr "php-xml ist als Paket verfügbar"

#: src/class-ss-diagnostic.php:489
msgid "php-xml is not available"
msgstr "php-xml ist nicht als Paket verfügbar"

#: src/class-ss-diagnostic.php:503
msgid "cURL is available"
msgstr "cURL ist verfügbar"

#: src/class-ss-diagnostic.php:504
#, php-format
msgid "cURL version < %s"
msgstr "cURL Version < %s"

#: src/class-ss-diagnostic.php:510
msgid "Basic Auth is not enabled."
msgstr "Basic Auth ist nicht aktiviert."

#: src/class-ss-diagnostic.php:518
msgid ""
"Basic Auth is enabled, but no username or password is set in Simply Static -"
"> Settings -> Debug -> Basic Auth"
msgstr ""
"Basic Auth ist aktiviert, aber kein Benutzername oder Passwort ist in Simply "
"Static -> Einstellungen -> Debug -> Basic Auth festgelegt"

#: src/class-ss-diagnostic.php:520
msgid ""
"Basic Auth is enabled, and username and password are set in Simply Static -> "
"Settings -> Debug -> Basic Auth"
msgstr ""
"Basic Auth ist aktiviert und Benutzername und Passwort sind in Simply Static "
"-> Einstellungen -> Debug -> Basic Auth festgelegt"

#: src/class-ss-diagnostic.php:541
msgid "Not a valid url."
msgstr "Keine valide URL."

#: src/class-ss-diagnostic.php:550
#, php-format
msgid "Received a %s response. This might indicate a problem."
msgstr "%s Antwort erhalten. Das weist auf ein Problem hin."

#: src/class-ss-diagnostic.php:553
#, php-format
msgid "Received a %s response."
msgstr "Es wurde eine %s Antwort erhalten."

#: src/class-ss-plugin-compatibility.php:230
#, php-format
msgid "Site link of %s"
msgstr "Seiten-Link von %s"

#: src/class-ss-plugin-compatibility.php:232
msgid "Visit site"
msgstr "Seite ansehen"

#: src/class-ss-plugin-compatibility.php:233
msgid "(opens in a new tab)"
msgstr "(öffnet sich in einem neuen Tab)"

#: src/class-ss-plugin-compatibility.php:257
msgid "Simply Static Compatible"
msgstr "Simply Static kompatibel"

#: src/class-ss-plugin.php:220
msgid ""
"Missing Basic Auth credentials - you need to configure the Basic Auth "
"credentials in Simply Static -> Settings -> Misc -> Basic Auth to continue "
"the export."
msgstr ""
"Fehlende Basic-Auth-Anmeldeinformationen – Sie müssen die Basic-Auth-"
"Anmeldeinformationen in Simply Static -> Einstellungen -> Sonstiges -> Basic "
"Auth konfigurieren, um den Export fortzusetzen."

#: src/class-ss-plugin.php:302
#, php-format
msgid "Found on %s"
msgstr "Gefunden auf %s"

#: src/class-ss-plugin.php:418
msgid "Docs"
msgstr "Dokumentation"

#: src/class-ss-url-fetcher.php:85
msgid "Attempted to fetch a remote URL"
msgstr "Versuche die URL abzurufen: %s"

#: src/class-ss-view.php:140
#, php-format
msgid "Can't find view template: %s"
msgstr "Konnte &#8222;Template Ansicht&#8220; nicht finden: %s"

#: src/class-ss-view.php:146
#, php-format
msgid "Can't find view layout: %s"
msgstr "Konnte &#8222;Layout Ansicht&#8220; nicht finden: %s"

#: src/handlers/class-ss-rank-math-sitemap-handler.php:124
#, php-format
msgid "XML Sitemap %1$s %2$s"
msgstr "XML Sitemap %1$s %2$s"

#: src/handlers/class-ss-rank-math-sitemap-handler.php:127
#, php-format
msgid "Locations Sitemap %1$s %2$s"
msgstr "Standort Sitemap %1$s %2$s"

#: src/integrations/class-aio-seo-integration.php:15
msgid "All in One SEO"
msgstr "All in One SEO"

#: src/integrations/class-aio-seo-integration.php:16
#: src/integrations/class-seopress-integration.php:16
msgid "Adds sitemaps to generated static files."
msgstr "Fügt den generierten statischen Dateien Sitemaps hinzu."

#: src/integrations/class-aio-seo-integration.php:83
#: src/integrations/class-rank-math-integration.php:123
#: src/integrations/class-seopress-integration.php:57
#: src/integrations/class-yoast-integration.php:95
msgid "Sitemap URL"
msgstr "Sitemap URL"

#: src/integrations/class-brizy-integration.php:15
msgid "Brizy"
msgstr "Brizy"

#: src/integrations/class-brizy-integration.php:16
msgid "Makes sure images optimized by Brizy are exported as well."
msgstr ""
"Stellt sicher, dass auch von Brizy optimierte Bilder exportiert werden."

#: src/integrations/class-cookie-yes-integration.php:17
msgid "CookieYes | GDPR Cookie Consent"
msgstr "CookieYes | GDPR Cookie Consent"

#: src/integrations/class-cookie-yes-integration.php:18
msgid "Fixes scripts given by CookieYes to work on exported pages."
msgstr ""
"Korrigiert von CookieYes bereitgestellte Skripte, damit diese auf "
"exportierten Seiten funktionieren."

#: src/integrations/class-elementor-integration.php:21
msgid "Elementor"
msgstr "Elementor"

#: src/integrations/class-elementor-integration.php:22
msgid ""
"Exports assets required for Elementor widgets and prepares data used by them."
msgstr ""
"Exportiert für Elementor-Widgets erforderliche Assets und bereitet die von "
"ihnen verwendeten Daten vor."

#: src/integrations/class-elementor-integration.php:222
msgid "Elementor Asset"
msgstr "Elementor Asset"

#: src/integrations/class-elementor-pro-integration.php:16
msgid "Elementor Pro"
msgstr "Elementor Pro"

#: src/integrations/class-elementor-pro-integration.php:17
msgid ""
"Exports assets required for Elementor Pro widgets and prepares data used by "
"them."
msgstr ""
"Exportiert für Elementor Pro-Widgets erforderliche Assets und bereitet die "
"von ihnen verwendeten Daten vor."

#: src/integrations/class-elementor-pro-integration.php:69
msgid "Elementor Pro Asset"
msgstr "Elementor Pro Asset"

#: src/integrations/class-elementor-pro-integration.php:202
msgid "Elementor Pro Lottie"
msgstr "Elementor Pro Lottie"

#: src/integrations/class-jetpack-integration.php:14
msgid "Jetpack"
msgstr "Jetpack"

#: src/integrations/class-jetpack-integration.php:15
msgid "Adds scripts for carousels and sliders to the static site."
msgstr ""
"Fügt der statischen Site Skripte für Karussells und Schieberegler hinzu."

#: src/integrations/class-jetpack-integration.php:43
msgid "Jetpack Integration"
msgstr "Jetpack Integration"

#: src/integrations/class-rank-math-integration.php:16
msgid "Rank Math"
msgstr "Rank Math"

#: src/integrations/class-rank-math-integration.php:17
#: src/integrations/class-yoast-integration.php:16
msgid ""
"Automatically includes your XML sitemaps, handles URL replacements in schema."
"org markup, and creates redirects on your static site for you."
msgstr ""
"Schließt automatisch Ihre XML-Sitemaps ein, übernimmt URL-Ersetzungen im "
"Schema.org-Markup und erstellt für Sie Weiterleitungen auf Ihrer statischen "
"Site."

#: src/integrations/class-rank-math-integration.php:99
msgid "RankMath Redirection URL"
msgstr "RankMath-Umleitungs-URL"

#: src/integrations/class-seopress-integration.php:15
msgid "SEOPress"
msgstr "SEOPress"

#: src/integrations/class-ss-adminbar-integration.php:17
msgid "Admin Bar (Core)"
msgstr "Admin Bar (Core)"

#: src/integrations/class-ss-adminbar-integration.php:18
msgid ""
"Adds an admin bar integration for Simply Static to see the current status of "
"static exports."
msgstr ""
"Fügt eine Adminleisten-Integration für Simply Static hinzu, um den aktuellen "
"Status statischer Exporte anzuzeigen."

#: src/integrations/class-ss-adminbar-integration.php:39
#: src/integrations/class-ss-adminbar-integration.php:43
msgid "Static Generation: Waiting.."
msgstr "Statische Generierung: Warten..."

#: src/integrations/class-ss-adminbar-integration.php:65
msgid "Static Generation:"
msgstr "Statische Generierung:"

#: src/integrations/class-ss-adminbar-integration.php:66
msgid "Running.."
msgstr "Läuft.."

#: src/integrations/class-ss-adminbar-integration.php:67
msgid "Idle"
msgstr "Leerlauf"

#: src/integrations/class-ss-adminbar-integration.php:68
msgid "Error"
msgstr "Fehler"

#: src/integrations/class-yoast-integration.php:15
msgid "Yoast"
msgstr "Yoast"

#: src/integrations/class-yoast-integration.php:69
msgid "Yoast Redirection URL"
msgstr "Yoast-Umleitungs-URL"

#: src/integrations/pro/class-complianz-integration.php:13
msgid "Complianz | GDPR/CCPA Cookie Consent"
msgstr "Complianz | GDPR/CCPA Cookie Consent"

#: src/integrations/pro/class-complianz-integration.php:14
msgid "Integrates Complianz Cookie banner to work on the static site."
msgstr ""
"Integriert das Complianz-Cookie-Banner, damit es auf der statischen Site "
"funktioniert."

#: src/integrations/pro/class-environments-integration.php:9
msgid "Environments (Core)"
msgstr "Umgebungen (Core)"

#: src/integrations/pro/class-environments-integration.php:10
msgid ""
"Define multiple environments of Simply Static so you can easily change "
"between saved configurations."
msgstr ""
"Definieren Sie mehrere Umgebungen von Simply Static, damit Sie problemlos "
"zwischen gespeicherten Konfigurationen wechseln können."

#: src/integrations/pro/class-github-integration.php:10
msgid "Github"
msgstr "Github"

#: src/integrations/pro/class-github-integration.php:11
msgid "Used when deploying the exported sites to Github"
msgstr "Wird beim Bereitstellen der exportierten Sites auf Github verwendet"

#: src/integrations/pro/class-multilingual-integration.php:13
msgid "WPML - Multilingual"
msgstr "WPML - Multilingual"

#: src/integrations/pro/class-multilingual-integration.php:14
msgid "Integrates WPML to work with exported sites."
msgstr "Integriert WPML, um mit exportierten Sites zu arbeiten."

#: src/integrations/pro/class-redirection-integration.php:13
msgid "Redirection"
msgstr "Redirection"

#: src/integrations/pro/class-redirection-integration.php:14
msgid ""
"Integrates redirections from the \"Redirection\" Plugin automatically on "
"each export."
msgstr ""
"Integriert Weiterleitungen vom Plugin „Redirection“ automatisch bei jedem "
"Export."

#: src/integrations/pro/class-shortpixel-integration.php:11
msgid "Shortpixel"
msgstr "Shortpixel"

#: src/integrations/pro/class-shortpixel-integration.php:12
msgid "Optimizes Images before exporting them for static sites."
msgstr "Optimiert Bilder, bevor sie für statische Sites exportiert werden."

#: src/tasks/class-ss-cancel-task.php:22
msgid "Cancelling job"
msgstr "Ausführung wird abgebrochen"

#: src/tasks/class-ss-create-zip-archive.php:30
msgid "ZIP archive created: "
msgstr "ZIP Archiv erstellt: "

#: src/tasks/class-ss-create-zip-archive.php:34
msgid "Click here to download"
msgstr "Klicke hier zum Download"

#: src/tasks/class-ss-create-zip-archive.php:90
msgid "Unable to create ZIP archive"
msgstr "ZIP Archiv konnte nicht erstellt werden"

#: src/tasks/class-ss-fetch-urls-task.php:91
msgid "Do not save or follow"
msgstr "Nicht speichern oder folgen"

#: src/tasks/class-ss-fetch-urls-task.php:128
#, php-format
msgid "Fetched %d of %d pages/files"
msgstr "%d von %d Seiten/Dateien abgerufen"

#: src/tasks/class-ss-fetch-urls-task.php:165
#: src/tasks/class-ss-fetch-urls-task.php:235
msgid "Do not follow"
msgstr "Nicht folgen"

#: src/tasks/class-ss-fetch-urls-task.php:183
#: src/tasks/class-ss-fetch-urls-task.php:266
msgid "Do not save"
msgstr "Nicht speichern"

#: src/tasks/class-ss-generate-404-task.php:53
msgid "Generating 404 Page."
msgstr "Generiere 404-Fehlerseite."

#: src/tasks/class-ss-generate-404-task.php:96
msgid "404 Page generated"
msgstr "404 Fehlerseite generiert"

#: src/tasks/class-ss-setup-task.php:23
msgid "Setting up"
msgstr "Einstellen"

#: src/tasks/class-ss-setup-task.php:36
#, php-format
msgid "Cannot create archive directory %s"
msgstr "Konnte die Datei bzw. das Verzeichnis nicht erstellen: %s"

#: src/tasks/class-ss-setup-task.php:84
msgid "Additional URL"
msgstr "Zusätzliche URL"

#: src/tasks/class-ss-setup-task.php:119
msgid "Additional File"
msgstr "Zusätzliche Datei"

#: src/tasks/class-ss-setup-task.php:132
msgid "Additional Dir"
msgstr "Zusätzliches Verzeichnis"

#: src/tasks/class-ss-transfer-files-locally-task.php:58
msgid "Destination URL:"
msgstr "Ziel-URL:"

#: src/tasks/class-ss-transfer-files-locally-task.php:95
msgid "No new/updated pages to transfer"
msgstr "Keine neuen/aktualisierten Seiten zum Übertragen"

#: src/tasks/class-ss-transfer-files-locally-task.php:98
#, php-format
msgid "Transferred %d of %d files"
msgstr "%d von %d Dateien kopiert"

#: src/tasks/class-ss-wrapup-task.php:23
msgid "Wrapping up"
msgstr "Fertigstellung"

#: src/tasks/traits/trait-can-process-pages.php:133
#, php-format
msgid "Uploaded %d of %d files"
msgstr "%d von %d Seiten/Dateien aktualisiert"

#: vendor/a5hleyrich/wp-background-processing/classes/wp-background-process.php:685
msgid "Every Minute"
msgstr "Jede Minute"

#: vendor/a5hleyrich/wp-background-processing/classes/wp-background-process.php:687
#, php-format
msgid "Every %d Minutes"
msgstr "Alle %d Minuten"

#: views/redirect.php:4
msgid "Redirecting..."
msgstr "Weiterleiten.."

#: views/redirect.php:12
#, php-format
msgid "You are being redirected to %s"
msgstr "Du wirst weitergeleitet auf %s"

#~ msgid ""
#~ "You have to migrate your settings to version 3.x of Simply Static to "
#~ "ensure everything works smoothly with the new interface."
#~ msgstr ""
#~ "Du musst deine Einstellungen auf Version 3.x upgraden um sicherzustellen, "
#~ "dass die neue Admin-Oberfläche korrekt funktioniert."

#~ msgid "Misc"
#~ msgstr "Weiteres"

#~ msgid "Simply CDN"
#~ msgstr "Simply CDN"

#~ msgid "Digital Ocean Spaces"
#~ msgstr "Digital Ocean Spaces"

#~ msgid ""
#~ "The fast and easy way to bring your static website online. Simply CDN "
#~ "handles hosting, performance, security and form submissions for your "
#~ "static site."
#~ msgstr ""
#~ "Der schnelle und einfache Weg deine statische Seite online zu bringen. "
#~ "Simply CDN händelt Hosting, Performance, Sicherheit und Formulare für "
#~ "dich."

#~ msgid "Security Token"
#~ msgstr "Security Token"

#~ msgid "Copy and paste the Security Token from your project."
#~ msgstr ""
#~ "Füge hier deinen Security Token aus der Projektübersicht von Simply CDN "
#~ "ein."

#~ msgid "We will use that page as a 404 error page on your static website."
#~ msgstr "Wir verwenden diese 404 Seite auf deiner statischen Website"

#~ msgid "Use Forms?"
#~ msgstr "Formulare benutzen?"

#~ msgid "Proxy form submissions through Simply CDN."
#~ msgstr "Proxy Formular-Einträge durch Simply CDN."

#~ msgid "Don't proxy form submissions through Simply CDN."
#~ msgstr "Nutze keinen Proxy."

#~ msgid "Select a \"Thank You\" page"
#~ msgstr "Wähle eine Vielen-Dank-Seite."

#~ msgid ""
#~ "We will use that page to redirect your visitors after submitting a form "
#~ "on your static website."
#~ msgstr ""
#~ "Wir nutzen diese Seite um deinen Besucher nach Absenden des Formulars "
#~ "weiterzuleiten."

#~ msgid "404 Page"
#~ msgstr "404 Fehlerseite"

#~ msgid "Relative path to your custom 404 page."
#~ msgstr "Gib den relativen Pfad zu deiner 404 Seite ein."

#~ msgid "Spaces Key"
#~ msgstr "Spaces Key"

#~ msgid "Enter your Spaces Key from Digital Ocean."
#~ msgstr "Trage deinen Digital Ocean Spaces API Key hier ein."

#~ msgid "Secret"
#~ msgstr "Secret"

#~ msgid "Enter your Spaces Secret from Digital Ocean."
#~ msgstr "Füge dein Digital Ocean API Secret hier ein."

#~ msgid "Bucket Name"
#~ msgstr "Bucket Name"

#~ msgid "The bucket name for your space."
#~ msgstr "Trage den Namen deines Buckets hier ein."

#~ msgid "The region for your space."
#~ msgstr "Die Region deines Spaces."

#~ msgid "Your endpoint will be"
#~ msgstr "Dein Endpunkt ist"

#~ msgid ""
#~ "Your static files are temporarily saved to a directory before being "
#~ "copied to their destination or creating a ZIP."
#~ msgstr ""
#~ "Deine statischen Dateien werden temporär hier erstellt bevor sie entweder "
#~ "als ZIP erstellt oder ins lokale Verzeichnis übertragen werden."

#~ msgid "Debugging Mode"
#~ msgstr "Debug-Modus"

#~ msgid "Enable debugging mode."
#~ msgstr "Debug-Modus aktivieren."

#~ msgid "Disable debugging mode."
#~ msgstr "Debug-Modus deaktivieren."

#~ msgid "Replace Plugin Names?"
#~ msgstr "Plugin Namen ersetzen?"

#~ msgid "Replace plugin names with a random string combinations."
#~ msgstr ""
#~ "Ersetzt Verzeichnisnamen der Plugins mit einer zufälligen Zeichenkette."

#~ msgid "Keep plugin names."
#~ msgstr "Plugin Name nicht verändern"

#~ msgid "Reset Plugin Data"
#~ msgstr "Plugin-Einstellungen zurücksetzen"

#~ msgid "SSL"
#~ msgstr "SSL"

#~ msgid "VERSION"
#~ msgstr "VERSION"

#~ msgid "WP REST API"
#~ msgstr "WP REST API"

#~ msgid "Requests to itself"
#~ msgstr "Anfragen zu sich selbst"

#~ msgid "You have a valid SSL certificate."
#~ msgstr "Du hast ein SSL-Zertifikat."

#~ msgid ""
#~ "You need an SSL certificate to connect with external APIs like GitHub or "
#~ "Algolia."
#~ msgstr ""
#~ "Du benötigst ein SSL-Zertifikat für die Kommunikation mit externen APIs "
#~ "wie GitHub oder Algolia."

#~ msgid "Rest API is available and running"
#~ msgstr "Rest API ist verfügbar und aktiviert"

#~ msgid "Rest API is disabled or blocked"
#~ msgstr "Rest API ist deaktiviert oder blockiert"

#, php-format
#~ msgid "WordPress can make requests to itself from %s"
#~ msgstr "WordPress kann Anfragen ans sich selbst von %s stellen"

#, php-format
#~ msgid "WordPress can not make requests to itself from %s"
#~ msgstr "WordPress kann keine Anfragen ans sich selbst von %s stellen"

#, php-format
#~ msgid "Uploading %d of %d pages/files"
#~ msgstr "%d von %d Seiten/Dateien hochgeladen"

#~ msgid "BunnyCDN"
#~ msgstr "BunnyCDN"

#~ msgid "Connect"
#~ msgstr "Verbinden"

#~ msgid "Urls and Patterns to exclude"
#~ msgstr "Urls und Pfade ausschließen"

#~ msgid "Settings to Use:"
#~ msgstr "Einstellungen benutzen:"

#~ msgid "Use Site's Settings"
#~ msgstr "Nutze Seiten-Einstellungen"

#~ msgid "Here you can configure settings related to WordPress Multisite."
#~ msgstr "Hier kannst du Einstellungen für WordPress Multisite konfigurieren."

#~ msgid "Show subsite settings"
#~ msgstr "Aktiviere Einstellungen auf Subsites"

#~ msgid "Additional Settings"
#~ msgstr "Zusätzliches Einstellungen"

#~ msgid ""
#~ "Here you can configure some additional settings like clearing the local "
#~ "directory before running an export or activating force replacement for "
#~ "all URLs."
#~ msgstr ""
#~ "Hier kannst du zusätzliche Einstellungen konfigurieren wie die Ausführung "
#~ "mit WP-Cron oder die erweiterte URL-Ersetzen-Funktion."

#~ msgid "Your website is successfully connected to the Simply CDN."
#~ msgstr "Deine Website wurde erfolgreich mit Simply CDN verbunden."

#~ msgid "Cache cleared successfully."
#~ msgstr "Cache erfolgreich gelöscht."

#~ msgid "Configure your static website"
#~ msgstr "Konfiguriere deine statische Website"

#~ msgid ""
#~ "Once your website is connected you can configure all settings related to "
#~ "the CDN here. This includes settings up redirects, proxy URLs and setting "
#~ "up a custom 404 error page."
#~ msgstr ""
#~ "Sobald du eine Verbindung mit Simply CDN aufgebaut hast, kann du deine "
#~ "statische Seite hier konfigurieren."

#~ msgid ""
#~ "This is your static site URL. We automatically change it based on your "
#~ "project configuration on simplycdn.io"
#~ msgstr ""
#~ "Das ist deine statische URL. Wir ändern dies automatisch basierend auf "
#~ "deinen Projekt-Einstellungen auf simplycdn.io"

#~ msgid "Relative path to your 404 page"
#~ msgstr "Relativer Pfad zur deiner 404-Seite"

#~ msgid ""
#~ "We automatically send form submissions to the configured e-mail address "
#~ "of your <NAME_EMAIL>."
#~ msgstr ""
#~ "Wir senden dir automatisch eine E-Mail sobald jemand dein Formular "
#~ "ausfüllt."

#~ msgid ""
#~ "Make sure to add your form token as a hidden field to each form of your "
#~ "website."
#~ msgstr ""
#~ "Stelle sicher, dass du den Formular-Token als verstecktes Feld in deinem "
#~ "Formular hinterlegst."

#~ msgid "Your form token: "
#~ msgstr "Dein Formular-Token: "

#~ msgid "Use Forms integration"
#~ msgstr "Nutze Formular-Integration"

#~ msgid "Automation & Utilities"
#~ msgstr "Automatisierung & Werkzeuge"

#~ msgid ""
#~ "Automatically updates a post/page on your static website once you saved "
#~ "it in WordPress."
#~ msgstr ""
#~ "Aktualisiere deine statische Seite sobald du einen Beitrag oder Seite in "
#~ "WordPress speicherst."

#~ msgid "Use Auto-Publish"
#~ msgstr "Nutze Auto-Publishing"

#~ msgid ""
#~ "The CDN cache is cleared automatically after each static export. "
#~ "Sometimes you want to clear the cache manually to make sure you get the "
#~ "latest results in your browser."
#~ msgstr ""
#~ "Der CDN-Cache leert sich automatisch nach jedem statischen Export. Wenn "
#~ "du den Cache manuell löschen möchtest, kannst du das hier tun."

#~ msgid "Clear Cache"
#~ msgstr "Cache löschen"

#~ msgid "View static URL"
#~ msgstr "Statische URL ansehen"

#~ msgid "There is something wrong with that security token."
#~ msgstr "Etwas stimmt mit deinem Security Token nicht."

#~ msgid ""
#~ "Enter your Access Key from AWS. You can find your API-Key as described "
#~ "here."
#~ msgstr "Trage deinen Access Key von AWS S3 hier ein."

#~ msgid ""
#~ "Enter your Secret Key from AWS. You can find your API-Key as described "
#~ "here."
#~ msgstr "Trage deinen Secret Key von AWS S3 hier ein."

#~ msgid "Generate (new)"
#~ msgstr "Generieren (new)"

#~ msgid "Generate Static Site"
#~ msgstr "Generiere eine Statische Seite"

#~ msgid "Not permitted"
#~ msgstr "Nicht berechtigt"

#~ msgid "Code"
#~ msgstr "Code"

#~ msgid "URL"
#~ msgstr "URL"

#~ msgid "Notes"
#~ msgstr "Hinweise"

#, php-format
#~ msgid "Errors (%d)"
#~ msgstr "Fehler(%d)"

#~ msgid "1xx Informational:"
#~ msgstr "1xx Informationen:"

#~ msgid "2xx Success:"
#~ msgstr "2xx Erfolgreich:"

#~ msgid "4xx Client Error:"
#~ msgstr "4xx Fehler:"

#~ msgid "5xx Server Error:"
#~ msgstr "5xx: Server-Fehler:"

#~ msgid ""
#~ "<a href='https://en.wikipedia.org/wiki/List_of_HTTP_status_codes'>More "
#~ "info on HTTP status codes</a>"
#~ msgstr ""
#~ "<a href='https://en.wikipedia.org/wiki/List_of_HTTP_status_codes'>Mehr "
#~ "über HTTP status codes</a>"

#, php-format
#~ msgid "%d URLs"
#~ msgstr "%d URLs"

#~ msgid "Simply Static &rsaquo; Generate"
#~ msgstr "Simply Static &rsaquo; Generieren"

#~ msgid "OK"
#~ msgstr "OK"

#~ msgid "FAIL"
#~ msgstr "Fehlgeschlagen"

#, php-format
#~ msgid "Checking if Destination URL <code>%s</code> is valid"
#~ msgstr "Prüfe ob Ziel-URL <code>%s</code> valide ist."

#~ msgid "Checking if website has an SSL certificate (HTTPS)"
#~ msgstr "Prüfe, ob die Website ein SSL-Zertifikat hat (HTTPS)"

#, fuzzy, php-format
#~| msgid "An Additional URL does not start with <code>%s</code>: %s"
#~ msgid "Checking if Additional URL <code>%s</code> is valid"
#~ msgstr "Eine zusätzliche URL darf nicht mit <code>%s</code> beginnen: %s"

#~ msgid "Checking for cURL support"
#~ msgstr "Prüfe ob cURL installiert ist"

#~ msgid "Simply Static Diagnostics"
#~ msgstr "Simply Static Diagnose"

#~ msgid "Your changes have been saved."
#~ msgstr "Deine Einstellungen wurden gespeichert."

#~ msgid "Unable to create a ZIP of the debug log."
#~ msgstr "ZIP Archiv für den Debug-Log konnte nicht erstellt werden."

#, php-format
#~ msgid "Debug log successfully sent to: %s"
#~ msgstr "Debug-Log erfolgreich gesendet zu %s"

#~ msgid "We encountered an error when attempting to send out the debug log."
#~ msgstr "Es gab einen Fehler beim versenden des Debug-Logs."

#~ msgid "Plugin reset complete."
#~ msgstr "Plugin-Reset erfolgreich ausgeführt."

#~ msgid "View what changed in this version"
#~ msgstr "Sieh dir den Changelog an"

#~ msgid "Simply Static &rsaquo; Diagnostics"
#~ msgstr "Simply Static &rsaquo; Diagnose"

#~ msgid "Yes"
#~ msgstr "Ja"

#~ msgid "No"
#~ msgstr "Nein"

#~ msgid "Plugin URL"
#~ msgstr "Plugin URL"

#~ msgid "Debugging Options"
#~ msgstr "Debug-Optionen"

#~ msgid ""
#~ "When enabled, a debug log will be created when generating static files."
#~ msgstr ""
#~ "Wenn aktiviert, protokolliert Simply Static jeden Schritt der statischen "
#~ "Generierung in einem Logfile."

#~ msgid "Save Changes"
#~ msgstr "Änderungen speichern"

#, php-format
#~ msgid "You have created <a href='%s'>a debug log</a>."
#~ msgstr "Du hast einen <a href='%s'>Debug-Log</a>. erstellt."

#~ msgid "You have not created a debug log yet."
#~ msgstr "Du hast bisher noch keinen Debug-Log erstellt."

#~ msgid "www.example.com/"
#~ msgstr "www.example.com/"

#~ msgid "Save for offline use"
#~ msgstr "Für Offline-Nutzung speichern"

#~ msgid ""
#~ "Saving your static files to a ZIP archive is Simply Static's default "
#~ "delivery method. After generating your static files you will be provided "
#~ "with a link to download the ZIP archive."
#~ msgstr ""
#~ "Speichere deine statischen Dateien als ZIP-Archiv. Du erhälst einen "
#~ "Download-Link sobald der statische Export abgeschlossen ist."

#~ msgid ""
#~ "Saving your static files to a local directory is useful if you want to "
#~ "serve your static files from the same server as your WordPress "
#~ "installation. WordPress can live on a subdomain (e.g. wordpress.example."
#~ "com) while your static files are served from your primary domain (e.g. "
#~ "www.example.com)."
#~ msgstr ""
#~ "Speichere deine statischen Dateien in einem lokalen Verzeichnis. Dies ist "
#~ "sinnvoll wenn du dynamische und statische Seite auf dem selben Server "
#~ "verwalten möchtest. WordPress kann dann auf einer Subdomain (wp.example."
#~ "com) liegen und die Hauptdomain verweist auf das Verzeichnis deiner "
#~ "statischen Website (www.example.com)."

#, php-format
#~ msgid "Example: <code>%s</code>"
#~ msgstr "Beispiel: <code>%s</code>"

#, php-format
#~ msgid "Examples: <code>%s</code> or <code>%s</code>"
#~ msgstr "Beispiele: <code>%s</code> oder <code>%s</code>"

#~ msgid "/hidden-page/"
#~ msgstr "/hidden-page/"

#~ msgid "/images/secret.jpg"
#~ msgstr "/images/secret.jpg"

#~ msgid "additional-directory/"
#~ msgstr "additional-directory/"

#~ msgid "fancy.pdf"
#~ msgstr "fancy.pdf"

#~ msgid "Remove"
#~ msgstr "Entfernen"

#~ msgid "Add URL to Exclude"
#~ msgstr "Füge URLs hinzu die du ausschließen möchtest"

#~ msgid "<b>Do not save</b>: do not save a static copy of the page/file"
#~ msgstr ""
#~ "<b>Nicht speichern</b> keine statische Kopie dieser Seite/Datei speichern"

#~ msgid ""
#~ "<b>Do not follow</b>: do not use this page to find additional URLs for "
#~ "processing"
#~ msgstr ""
#~ "<b>Nicht folgen</b> diese Seite nicht benutzen, um nach weiteren URLs für "
#~ "den Export zu suchen"

#, php-format
#~ msgid ""
#~ "Example: <code>%s</code> would exclude <code>%s</code> and other files "
#~ "containing <code>%s</code> from processing"
#~ msgstr ""
#~ "Beispiel: <code>%s</code> würde exkludieren <code>%s</code> und andere "
#~ "Dateien die das Muster enthalten: <code>%s</code>"

#~ msgid ".jpg"
#~ msgstr ".jpg"

#~ msgid "/wp-content/uploads/image.jpg"
#~ msgstr "/wp-content/uploads/image.jpg"

#, php-format
#~ msgid "Default: <code>%s</code>"
#~ msgstr "Standard: <code>%s</code>"

#~ msgid "HTTP Basic Authentication"
#~ msgstr "HTTP Basic Authentifizierung"

#~ msgid ""
#~ "Your basic auth credentials have been saved. To disable basic auth or set "
#~ "a new username/password, <a href='#'>click here</a>."
#~ msgstr ""
#~ "Deine Basic Auth Zugangsdaten wurden gespeichert. Um Basic Auth zu "
#~ "deaktivieren oder die Daten anzupassen, <a href='#'>klicke hier</a>."

#~ msgid "Run with WP-Cron"
#~ msgstr "Mit WP-Cron ausführen"

#~ msgid "This will reset Simply Static's settings back to their defaults."
#~ msgstr ""
#~ "Dies setze alle Simply Static Einstellungen auf die Standardeinstellungen "
#~ "zurück."

#~ msgid "Contact Support"
#~ msgstr "Support kontaktieren"

#~ msgid "Enjoying Simply Static? Add your [stars] on wordpress.org."
#~ msgstr ""
#~ "Die gefällt Simply Static? Füge deine [stars] auf wordpress.org hinzu!"

#~ msgid ""
#~ "If you want to export a large site with Simply Static you may want to use "
#~ "WP-Cron for that."
#~ msgstr ""
#~ "Wenn du eine große Seite mit Simply Static exportieren möchtest, "
#~ "empfiehlt sich die Verwendung von WP-Cron. Prüfe bitte gründlich, ob "
#~ "deine Cron-Jobs funktionieren, bevor du diese Option aktivierst."

#~ msgid "Use WP-Cron"
#~ msgstr "WP-Cron benutzen"

#~ msgid ""
#~ "An Additional File or Directory is not located within an expected "
#~ "directory: %s<br />It should be in one of these directories (or a "
#~ "subdirectory):<br  /><code>%s</code><br /> <code>%s</code><br /> "
#~ "<code>%s</code>"
#~ msgstr ""
#~ "Eine zusätzliche Datei oder Verzeichnis ist nicht in dem erwarteten "
#~ "Verzeichnis: %s<br />Diese sollten in einem dieser Verzeichnisse (oder "
#~ "einem Untervezeichnis) sein:<br  /><code>%s</code><br /> <code>%s</"
#~ "code><br /> <code>%s</code>"

#~ msgid "Could not delete temporary file or directory: %s"
#~ msgstr "Konnte die temporäre Datei bzw. das Verzeichnis nicht löschen: %s"

#~ msgid "Temporary Files Directory is not writeable: %s"
#~ msgstr "Das Verzeichnis für temporäre Dateien ist nicht beschreibbar: %s"

#~ msgid "Temporary Files Directory does not exist: %s"
#~ msgstr "Das Verzeichnis für temporäre Dateien existiert nicht: %s"

#~ msgid ""
#~ "Your site does not have a permalink structure set. You can select one on "
#~ "<a href='%s'>the Permalink Settings page</a>."
#~ msgstr ""
#~ "Deine Website hat keine Permalink Struktur gesetzt. Du kannst eine solche "
#~ "unter <a href='%s'>Einstellungen &gt; Permalinks</a> auswählen und "
#~ "speichern."

#~ msgid ""
#~ "Your server does not have the PHP zip extension enabled. Please visit <a "
#~ "href='http://www.php.net/manual/en/book.zip.php'>the PHP zip extension "
#~ "page</a> for more information on how to enable it."
#~ msgstr ""
#~ "Dein Server hat die PHP zip Erweiterung nicht aktiviert. Schau dir bitte "
#~ "die <a href='http://www.php.net/manual/en/book.zip.php'>PHP zip-"
#~ "Erweiterungs-Seite (engl.)</a> für weiterführende Informationen an, und "
#~ "wie du diese Erweiterung aktivieren kannst."

#~ msgid "Local Directory is not writeable: %s"
#~ msgstr "Das lokale Verzeichnis ist nicht beschreibbar: %s"

#~ msgid "Local Directory does not exist: %s"
#~ msgstr "Das lokale Verzeichnis existiert nicht: %s"

#~ msgid "http://codeofconduct.co/simply-static"
#~ msgstr "http://codeofconduct.co/simply-static"

#~ msgid ""
#~ "Produces a static HTML version of your WordPress install and adjusts URLs "
#~ "accordingly."
#~ msgstr ""
#~ "Erstellt von deiner WordPress Installation eine statische HTML Version, "
#~ "und passt die URLS entsprechend an."

#~ msgid "Code of Conduct"
#~ msgstr "Code of Conduct"

#~ msgid "http://codeofconduct.co/"
#~ msgstr "http://codeofconduct.co/"
