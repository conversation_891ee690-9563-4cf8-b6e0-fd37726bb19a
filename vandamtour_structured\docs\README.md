# VẠN DẶM TOUR - Structured Website

## 📁 Project Structure

```
vandamtour_structured/
├── index.html                 # Main HTML file
├── assets/                    # All assets organized
│   ├── css/                   # Stylesheets
│   │   ├── fontawesome.css    # FontAwesome icons
│   │   ├── theme-flatsome.css # Main theme styles
│   │   ├── contact-form.css   # Contact form styles
│   │   └── button-contact.css # Contact button styles
│   ├── js/                    # JavaScript files
│   │   ├── jquery.min.js      # jQuery library
│   │   ├── jquery-migrate.min.js # jQuery migrate
│   │   ├── theme-flatsome.js  # Theme functionality
│   │   └── contact-form.js    # Form handling
│   ├── images/                # Images organized by category
│   │   ├── services/          # Service related images
│   │   ├── banners/           # Banner/slider images
│   │   ├── icons/             # Icon images
│   │   ├── branding/          # Logo and branding
│   │   └── gallery/           # Other images
│   └── fonts/                 # Font files
├── components/                # Reusable components (future)
├── pages/                     # Additional pages (future)
└── docs/                      # Documentation
    ├── README.md              # This file
    └── structure.json         # Project structure data
```

## 🎯 Features

- ✅ **Clean HTML structure** - Semantic and maintainable
- ✅ **Organized CSS** - Modular and categorized
- ✅ **Structured JavaScript** - Clean and documented
- ✅ **Categorized images** - Organized by purpose
- ✅ **Professional structure** - Industry standard layout
- ✅ **Documentation** - Complete project docs

## 📊 Statistics

- **HTML files**: 1
- **CSS files**: 7
- **JavaScript files**: 10
- **Image files**: 22
- **Total size**: 2,185,126 bytes

## 🚀 Usage

1. Open `index.html` in a web browser
2. All assets are properly linked with relative paths
3. Website works offline with all functionality

## 🛠️ Development

### CSS Files
- assets/css/style-1.css
- assets/css/contact-form.css
- assets/css/button-contact.css
- assets/css/fontawesome.css
- assets/css/theme-flatsome.css
- assets/css/theme-flatsome.css
- assets/css/fontawesome.css

### JavaScript Files  
- assets/js/jquery.min.js
- assets/js/jquery-migrate.min.js
- assets/js/wp-hooks.js
- assets/js/wp-i18n.js
- assets/js/contact-form.js
- assets/js/contact-form.js
- assets/js/theme-flatsome.js
- assets/js/script-8.js
- assets/js/theme-flatsome.js
- assets/js/theme-flatsome.js

### Image Categories
- **Services**: Taxi service images
- **Banners**: Slider and banner images  
- **Icons**: Contact and UI icons
- **Branding**: Logo and brand assets
- **Gallery**: Other images

## 📝 Notes

- All external dependencies have been downloaded locally
- CSS is organized by functionality
- JavaScript maintains original structure
- Images are categorized for easy management
- HTML is clean and semantic

---

**Source**: https://vandamtour.vn  
**Generated**: 2025-07-30 21:14:31  
**Tool**: Structured Website Cloner v4.0.0
