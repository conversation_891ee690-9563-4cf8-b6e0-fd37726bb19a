0 verbose cli C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@11.2.0
2 info using node@v22.14.0
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:c:\xampp\htdocs\duan\wordpress-taxi\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm ls
8 verbose argv "ls" "--depth" "0" "--package-lock-only" "--omit" "dev" "--json"
9 verbose logfile logs-max:10 dir:C:\xampp\htdocs\duan\wordpress-taxi\UsersAdminAppDataRoamingnpm-cache\_logs\2025-07-30T11_06_45_314Z-
10 verbose logfile C:\xampp\htdocs\duan\wordpress-taxi\UsersAdminAppDataRoamingnpm-cache\_logs\2025-07-30T11_06_45_314Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly packumentCache heap:2197815296 maxSize:********* maxEntrySize:*********
13 silly logfile done cleaning log files
14 verbose cwd c:\xampp\htdocs\duan\wordpress-taxi
15 verbose os Windows_NT 10.0.26100
16 verbose node v22.14.0
17 verbose npm  v11.2.0
18 verbose exit 0
19 info ok
