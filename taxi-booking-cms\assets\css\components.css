/* ===== SERVICE CARD COMPONENT ===== */
.service-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.service-card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), #3d6db0);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-2xl);
}

.service-card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
}

.service-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.service-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.service-card-price {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.service-card-features {
    list-style: none;
    margin-bottom: var(--spacing-lg);
}

.service-card-features li {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.service-card-features li::before {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--success-color);
    font-size: var(--font-size-sm);
}

.service-card-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
}

/* ===== CONTACT INFO COMPONENT ===== */
.contact-info-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: var(--transition);
}

.contact-info-item:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-medium);
}

.contact-info-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #3d6db0);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.contact-info-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.contact-info-content p {
    color: var(--text-secondary);
    margin: 0;
}

.contact-info-content a {
    color: var(--primary-color);
    font-weight: 600;
}

.contact-info-content a:hover {
    color: var(--secondary-color);
}

/* ===== FOOTER SECTION COMPONENT ===== */
.footer-section h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: white;
}

.footer-section p {
    color: rgba(255,255,255,0.8);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: rgba(255,255,255,0.8);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.footer-section ul li a:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.footer-section ul li a i {
    width: 20px;
    text-align: center;
}

/* ===== STATS COMPONENT ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
    margin: var(--spacing-3xl) 0;
}

.stat-item {
    text-align: center;
    padding: var(--spacing-xl);
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-color);
    display: block;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: 600;
}

/* ===== TESTIMONIAL COMPONENT ===== */
.testimonial-card {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    position: relative;
    margin-bottom: var(--spacing-xl);
}

.testimonial-card::before {
    content: '\f10d';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    opacity: 0.3;
}

.testimonial-content {
    font-style: italic;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    padding-left: var(--spacing-2xl);
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-info h4 {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.testimonial-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* ===== FEATURE LIST COMPONENT ===== */
.feature-list {
    list-style: none;
    margin: var(--spacing-lg) 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.feature-content h4 {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.feature-content p {
    color: var(--text-secondary);
    margin: 0;
    font-size: var(--font-size-sm);
}

/* ===== PRICING CARD COMPONENT ===== */
.pricing-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.pricing-card.featured {
    transform: scale(1.05);
    box-shadow: var(--shadow-heavy);
}

.pricing-card.featured::before {
    content: 'Phổ biến';
    position: absolute;
    top: var(--spacing-lg);
    right: -30px;
    background: var(--secondary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-2xl);
    font-size: var(--font-size-sm);
    font-weight: 700;
    transform: rotate(45deg);
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.pricing-price {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.pricing-unit {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xl);
}

.pricing-features {
    list-style: none;
    margin-bottom: var(--spacing-xl);
}

.pricing-features li {
    padding: var(--spacing-sm) 0;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.pricing-features li::before {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--success-color);
    font-size: var(--font-size-sm);
}

/* ===== BREADCRUMB COMPONENT ===== */
.breadcrumb {
    background: var(--bg-secondary);
    padding: var(--spacing-lg) 0;
    margin-bottom: var(--spacing-xl);
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    list-style: none;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.breadcrumb-item:not(:last-child)::after {
    content: '\f054';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

.breadcrumb-item a {
    color: var(--text-secondary);
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 600;
}

/* ===== PROGRESS BAR COMPONENT ===== */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin: var(--spacing-md) 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== ALERT COMPONENT ===== */
.alert {
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    border: 1px solid var(--success-color);
    color: var(--success-color);
}

.alert-error {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid var(--danger-color);
    color: var(--danger-color);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    border: 1px solid var(--warning-color);
    color: var(--warning-color);
}

.alert-info {
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid var(--info-color);
    color: var(--info-color);
}

.alert-icon {
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.alert-message {
    margin: 0;
    opacity: 0.9;
}

/* ===== BADGE COMPONENT ===== */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 20px;
    font-size: var(--font-size-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--primary-color);
    color: white;
}

.badge-secondary {
    background: var(--secondary-color);
    color: white;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-info {
    background: var(--info-color);
    color: white;
}

.badge-outline {
    background: transparent;
    border: 1px solid currentColor;
}

/* ===== CARD COMPONENT ===== */
.card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-medium);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.card-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

/* ===== ANIMATION UTILITIES ===== */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

.slide-down {
    animation: slideDown 0.6s ease-out;
}

.slide-left {
    animation: slideLeft 0.6s ease-out;
}

.slide-right {
    animation: slideRight 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideLeft {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideRight {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}
