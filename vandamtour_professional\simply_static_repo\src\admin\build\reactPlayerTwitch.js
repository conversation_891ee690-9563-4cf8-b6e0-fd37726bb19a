(globalThis.webpackChunksimplystatic_settings=globalThis.webpackChunksimplystatic_settings||[]).push([[42],{400:(e,t,s)=>{var a,r=Object.create,l=Object.defineProperty,i=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,h=(e,t,s,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of n(t))p.call(e,r)||r===s||l(e,r,{get:()=>t[r],enumerable:!(a=i(t,r))||a.enumerable});return e},y=(e,t,s)=>(((e,t,s)=>{t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s})(e,"symbol"!=typeof t?t+"":t,s),s),d={};((e,t)=>{for(var s in t)l(e,s,{get:t[s],enumerable:!0})})(d,{default:()=>P}),e.exports=(a=d,h(l({},"__esModule",{value:!0}),a));var c=((e,t,s)=>(s=null!=e?r(o(e)):{},h(e&&e.__esModule?s:l(s,"default",{value:e,enumerable:!0}),e)))(s(609)),u=s(635),m=s(327);class P extends c.Component{constructor(){super(...arguments),y(this,"callPlayer",u.callPlayer),y(this,"playerID",this.props.config.playerId||`twitch-player-${(0,u.randomString)()}`),y(this,"mute",(()=>{this.callPlayer("setMuted",!0)})),y(this,"unmute",(()=>{this.callPlayer("setMuted",!1)}))}componentDidMount(){this.props.onMount&&this.props.onMount(this)}load(e,t){const{playsinline:s,onError:a,config:r,controls:l}=this.props,i=m.MATCH_URL_TWITCH_CHANNEL.test(e),n=i?e.match(m.MATCH_URL_TWITCH_CHANNEL)[1]:e.match(m.MATCH_URL_TWITCH_VIDEO)[1];t?i?this.player.setChannel(n):this.player.setVideo("v"+n):(0,u.getSDK)("https://player.twitch.tv/js/embed/v1.js","Twitch").then((t=>{this.player=new t.Player(this.playerID,{video:i?"":n,channel:i?n:"",height:"100%",width:"100%",playsinline:s,autoplay:this.props.playing,muted:this.props.muted,controls:!!i||l,time:(0,u.parseStartTime)(e),...r.options});const{READY:a,PLAYING:o,PAUSE:p,ENDED:h,ONLINE:y,OFFLINE:d,SEEK:c}=t.Player;this.player.addEventListener(a,this.props.onReady),this.player.addEventListener(o,this.props.onPlay),this.player.addEventListener(p,this.props.onPause),this.player.addEventListener(h,this.props.onEnded),this.player.addEventListener(c,this.props.onSeek),this.player.addEventListener(y,this.props.onLoaded),this.player.addEventListener(d,this.props.onLoaded)}),a)}play(){this.callPlayer("play")}pause(){this.callPlayer("pause")}stop(){this.callPlayer("pause")}seekTo(e,t=!0){this.callPlayer("seek",e),t||this.pause()}setVolume(e){this.callPlayer("setVolume",e)}getDuration(){return this.callPlayer("getDuration")}getCurrentTime(){return this.callPlayer("getCurrentTime")}getSecondsLoaded(){return null}render(){return c.default.createElement("div",{style:{width:"100%",height:"100%"},id:this.playerID})}}y(P,"displayName","Twitch"),y(P,"canPlay",m.canPlay.twitch),y(P,"loopOnEnded",!0)}}]);