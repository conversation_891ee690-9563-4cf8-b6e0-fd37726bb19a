{"jquery.min.js": {"url": "https://vandamtour.vn/wp-includes/js/jquery/jquery.min.js?ver=3.7.1", "functions": ["J", "B", "P", "Ge", "A", "Ie", "te", "it", "T", "e", "Ut", "ne", "gt", "re", "N", "at", "z", "Ct", "Y", "ee", "q", "L", "Tt", "I", "K", "W", "Ye", "yt", "c", "Ee", "Ae", "t", "dt", "V", "_", "ht", "F", "Vt", "o", "kt", "rt", "ot", "x", "We", "G", "X", "Se", "Q", "fe", "m", "n", "He", "Be", "vt", "p", "Re", "Gt", "Le", "U", "Ne", "Fe", "Ze", "Pt", "qe", "l", "Z"], "variables": ["H", "Mt", "pe", "tn", "d", "e", "Et", "a", "r", "ne", "E", "xe", "ee", "je", "Jt", "qt", "ke", "Oe", "c", "et", "Qe", "Dt", "t", "_", "oe", "s", "o", "f", "mt", "G", "St", "De", "O", "n", "M", "_e", "D", "i", "v", "ye", "st", "bt", "en", "k", "Yt", "u"], "frameworks": ["j<PERSON><PERSON><PERSON>"], "apis": ["XMLHttpRequest", ".ajaxs*(", ".gets*("], "size": 87553, "minified": true}, "jquery-migrate.min.js": {"url": "https://vandamtour.vn/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1", "functions": ["i", "T", "e", "a", "c", "r", "x", "u", "t"], "variables": ["D", "S", "F", "M", "o", "d", "e", "n", "a", "r", "j", "Q", "t"], "frameworks": ["j<PERSON><PERSON><PERSON>"], "apis": [], "size": 13577, "minified": true}, "hooks.min.js": {"url": "https://vandamtour.vn/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6", "functions": [], "variables": ["i", "t", "h", "o", "s", "e", "a", "r", "c", "l", "u", "n"], "frameworks": [], "apis": [], "size": 4776, "minified": true}, "i18n.min.js": {"url": "https://vandamtour.vn/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6", "functions": ["o", "d", "a", "c", "r"], "variables": ["i", "p", "t", "h", "o", "s", "f", "e", "a", "r", "n"], "frameworks": [], "apis": [], "size": 9141, "minified": true}, "index.js": {"url": "https://vandamtour.vn/wp-content/plugins/contact-form-7/includes/js/index.js?ver=6.1", "functions": ["o", "f", "p", "w"], "variables": ["p", "o", "s", "f", "d", "e", "n", "a", "r", "c", "u", "t"], "frameworks": [], "apis": ["fetchs*(", ".gets*("], "size": 13452, "minified": true}, "flatsome-live-search.js": {"url": "https://vandamtour.vn/wp-content/themes/flatsome/inc/extensions/flatsome-live-search/flatsome-live-search.js?ver=3.19.6", "functions": ["r"], "variables": ["pattern", "i", "html", "s", "o", "append", "searchForm", "e", "g", "n", "search_categories", "product_cat", "l", "serviceUrl", "t"], "frameworks": ["j<PERSON><PERSON><PERSON>"], "apis": [".ajaxs*(", ".gets*("], "size": 16237, "minified": true}, "hoverIntent.min.js": {"url": "https://vandamtour.vn/wp-includes/js/hoverIntent.min.js?ver=1.10.2", "functions": ["o", "u"], "variables": ["e", "i", "u"], "frameworks": ["j<PERSON><PERSON><PERSON>"], "apis": [], "size": 1499, "minified": true}, "flatsome.js": {"url": "https://vandamtour.vn/wp-content/themes/flatsome/assets/js/flatsome.js?ver=8e60d746741250b4dd4e", "functions": ["P", "H", "S", "A", "b", "T", "e", "a", "r", "E", "z", "L", "I", "c", "j", "t", "V", "_", "F", "o", "s", "R", "X", "Q", "m", "n", "O", "M", "i", "v", "l"], "variables": ["B", "y", "h", "d", "e", "a", "r", "N", "w", "q", "C", "W", "c", "t", "o", "s", "f", "x", "n", "D", "i", "p", "U", "v", "k", "l", "u"], "frameworks": ["j<PERSON><PERSON><PERSON>"], "apis": [".ajaxs*(", ".gets*("], "size": 52859, "minified": true}, "packery.pkgd.min.js": {"url": "https://vandamtour.vn/wp-content/themes/flatsome/assets/libs/packery.pkgd.min.js?ver=3.19.6", "functions": ["i", "h", "o", "s", "n", "e", "a", "r", "u", "t"], "variables": ["i", "m", "h", "o", "s", "f", "d", "T", "e", "n", "a", "c", "r", "l", "u", "t"], "frameworks": ["j<PERSON><PERSON><PERSON>"], "apis": [], "size": 33043, "minified": true}}