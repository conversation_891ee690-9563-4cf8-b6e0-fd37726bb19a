{"name": "VẠN DẶM TOUR", "description": "<PERSON><PERSON><PERSON> là có - <PERSON><PERSON> là thích ", "url": "https://vandamtour.vn", "home": "https://vandamtour.vn", "gmt_offset": "7", "timezone_string": "", "page_for_posts": 0, "page_on_front": 2, "show_on_front": "page", "namespaces": ["oembed/1.0", "contact-form-7/v1", "font-awesome/v1", "wp/v2", "wp-site-health/v1", "wp-block-editor/v1"], "authentication": {"application-passwords": {"endpoints": {"authorization": "https://vandamtour.vn/wp-admin/authorize-application.php"}}}, "routes": {"/": {"namespace": "", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/"}]}}, "/batch/v1": {"namespace": "", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"validation": {"type": "string", "enum": ["require-all-validate", "normal"], "default": "normal", "required": false}, "requests": {"type": "array", "maxItems": 25, "items": {"type": "object", "properties": {"method": {"type": "string", "enum": ["POST", "PUT", "PATCH", "DELETE"], "default": "POST"}, "path": {"type": "string", "required": true}, "body": {"type": "object", "properties": [], "additionalProperties": true}, "headers": {"type": "object", "properties": [], "additionalProperties": {"type": ["string", "array"], "items": {"type": "string"}}}}}, "required": true}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/batch/v1"}]}}, "/oembed/1.0": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "oembed/1.0", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/oembed/1.0"}]}}, "/oembed/1.0/embed": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "URL của tài nguyên cần lấy dữ liệu oEmbed.", "type": "string", "format": "uri", "required": true}, "format": {"default": "json", "required": false}, "maxwidth": {"default": 600, "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/oembed/1.0/embed"}]}}, "/oembed/1.0/proxy": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "URL của tài nguyên cần lấy dữ liệu oEmbed.", "type": "string", "format": "uri", "required": true}, "format": {"description": "<PERSON><PERSON><PERSON> dạng oEmbed cần sử dụng.", "type": "string", "default": "json", "enum": ["json", "xml"], "required": false}, "maxwidth": {"description": "<PERSON><PERSON>u rộng tối đa của khung nhúng tính bằng đơn vị pixels.", "type": "integer", "default": 600, "required": false}, "maxheight": {"description": "<PERSON><PERSON>u cao tối đa của khung nhúng tính bằng đơn vị pixels.", "type": "integer", "required": false}, "discover": {"description": "<PERSON><PERSON> nên thực hiện yêu cầu khám phá oEmbed cho các nhà cung cấp chưa đư<PERSON><PERSON> cấp phép hay không.", "type": "boolean", "default": true, "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/oembed/1.0/proxy"}]}}, "/contact-form-7/v1": {"namespace": "contact-form-7/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "contact-form-7/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/contact-form-7/v1"}]}}, "/contact-form-7/v1/contact-forms": {"namespace": "contact-form-7/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/contact-form-7/v1/contact-forms"}]}}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)": {"namespace": "contact-form-7/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}, {"methods": ["DELETE"], "args": []}]}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)/feedback": {"namespace": "contact-form-7/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}]}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)/feedback/schema": {"namespace": "contact-form-7/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/contact-form-7/v1/contact-forms/(?P<id>\\d+)/refill": {"namespace": "contact-form-7/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/font-awesome/v1": {"namespace": "font-awesome/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "font-awesome/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/font-awesome/v1"}]}}, "/font-awesome/v1/api": {"namespace": "font-awesome/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/font-awesome/v1/api"}]}}, "/font-awesome/v1/api/token": {"namespace": "font-awesome/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/font-awesome/v1/api/token"}]}}, "/font-awesome/v1/config": {"namespace": "font-awesome/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/font-awesome/v1/config"}]}}, "/font-awesome/v1/preference-check": {"namespace": "font-awesome/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/font-awesome/v1/preference-check"}]}}, "/font-awesome/v1/conflict-detection/until": {"namespace": "font-awesome/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/font-awesome/v1/conflict-detection/until"}]}}, "/font-awesome/v1/conflict-detection/conflicts": {"namespace": "font-awesome/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": []}, {"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/font-awesome/v1/conflict-detection/conflicts"}]}}, "/font-awesome/v1/conflict-detection/conflicts/blocklist": {"namespace": "font-awesome/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/font-awesome/v1/conflict-detection/conflicts/blocklist"}]}}, "/wp/v2": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp/v2", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2"}]}}, "/wp/v2/posts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "after": {"description": "Giới hạn phản hồi chỉ cho các bài viết đư<PERSON><PERSON>ất bản sau một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết đư<PERSON><PERSON> sửa đổi sau một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Gi<PERSON>i hạn kết quả là các bài viết của các tác giả cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "<PERSON><PERSON><PERSON> bảo rằng kết quả đã loại bỏ những bài viết của các tác giả cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Giới hạn phản hồi chỉ cho các bài viết được Xuất bản trước một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết được sửa đổi trước một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "<PERSON><PERSON><PERSON> diễn g<PERSON><PERSON>i thông tin tìm kiếm.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của bài viết.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "<PERSON><PERSON><PERSON> tên cột cần tìm kiếm.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Giới hạn kết quả thiết lập cho bài viết với một hoặc nhiều đường dẫn cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Gi<PERSON>i hạn kết quả cho các bài viết được gán một hoặc nhiều trạng thái.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "Gi<PERSON>i hạn kết quả dựa trên mối quan hệ giữa nhiều nguyên tắc phân loại.", "type": "string", "enum": ["AND", "OR"], "required": false}, "categories": {"description": "Giới hạn bộ kết quả cho các mục có thuật ngữ cụ thể được chỉ định trong phân loại categories.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> ng<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> các thuật ngữ với các ID được liệt kê.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON><PERSON> v<PERSON>n <PERSON> lo<PERSON>i theo ID Thuật ngữ", "description": "<PERSON><PERSON><PERSON><PERSON> hiện một truy vấn thuật ngữ nâng cao.", "type": "object", "properties": {"terms": {"description": "ID của thuật ng<PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "<PERSON><PERSON> bao gồm các thuật ngữ con trong các thuật ngữ giới hạn bộ kết quả hay không.", "type": "boolean", "default": false}, "operator": {"description": "Cho dù các mục đều được gán hoặc bất kỳ mục cụ thể nào.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "categories_exclude": {"description": "Giới hạn bộ kết quả cho các mục ngoại trừ những mục có thuật ngữ cụ thể được gán trong phân loại categories.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> ng<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> các thuật ngữ với các ID được liệt kê.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON><PERSON> v<PERSON>n <PERSON> lo<PERSON>i theo ID Thuật ngữ", "description": "<PERSON><PERSON><PERSON><PERSON> hiện một truy vấn thuật ngữ nâng cao.", "type": "object", "properties": {"terms": {"description": "ID của thuật ng<PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "<PERSON><PERSON> bao gồm các thuật ngữ con trong các thuật ngữ giới hạn bộ kết quả hay không.", "type": "boolean", "default": false}}, "additionalProperties": false}], "required": false}, "tags": {"description": "G<PERSON><PERSON><PERSON> hạn bộ kết quả cho các mục có thuật ngữ cụ thể được chỉ định trong phân loại tags.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> ng<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> các thuật ngữ với các ID được liệt kê.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON><PERSON> v<PERSON>n <PERSON> lo<PERSON>i theo ID Thuật ngữ", "description": "<PERSON><PERSON><PERSON><PERSON> hiện một truy vấn thuật ngữ nâng cao.", "type": "object", "properties": {"terms": {"description": "ID của thuật ng<PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "Cho dù các mục đều được gán hoặc bất kỳ mục cụ thể nào.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "tags_exclude": {"description": "Giớ<PERSON> hạn bộ kết quả cho các mục ngoại trừ những mục có thuật ngữ cụ thể được gán trong phân loại tags.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> ng<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> các thuật ngữ với các ID được liệt kê.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON><PERSON> v<PERSON>n <PERSON> lo<PERSON>i theo ID Thuật ngữ", "description": "<PERSON><PERSON><PERSON><PERSON> hiện một truy vấn thuật ngữ nâng cao.", "type": "object", "properties": {"terms": {"description": "ID của thuật ng<PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "sticky": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả gồm những bài viết đ<PERSON><PERSON><PERSON> ghim.", "type": "boolean", "required": false}, "ignore_sticky": {"description": "<PERSON><PERSON> bỏ qua bài đăng nổi bật hay không.", "type": "boolean", "default": true, "required": false}, "format": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn tập kết quả cho các mục được gán một hoặc nhiều định dạng nhất định.", "type": "array", "uniqueItems": true, "items": {"enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "ID của tác giả của bài viết.", "type": "integer", "required": false}, "excerpt": {"description": "<PERSON><PERSON> tả ngắn của bài viết.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả ngắn củ<PERSON> bà<PERSON> vi<PERSON>, n<PERSON><PERSON> đư<PERSON><PERSON> lưu trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON><PERSON> đo<PERSON>n HTML của bài vi<PERSON>, dành cho hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không trích đoạn đư<PERSON><PERSON> bảo vệ bởi mật khẩu.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "comment_status": {"description": "<PERSON><PERSON>u trong bài viết có mở hay không mục bình luận.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "<PERSON><PERSON><PERSON> bài viết có thể được ping hay không.", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "<PERSON><PERSON><PERSON> dạng cho bài viết.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "<PERSON><PERSON><PERSON> bài viế<PERSON> đ<PERSON><PERSON><PERSON> ghim nổi bật hay không.", "type": "boolean", "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}, "categories": {"description": "Các term đ<PERSON><PERSON><PERSON> gán cho một bài viết của taxonomy category.", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "Các term đ<PERSON><PERSON><PERSON> gán cho một bài viết của taxonomy post_tag.", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/posts"}]}}, "/wp/v2/posts/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "<PERSON><PERSON> đè độ dài đoạn trích mặc định.", "type": "integer", "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> khẩu của bài viết đó nếu nó được bảo vệ.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "ID của tác giả của bài viết.", "type": "integer", "required": false}, "excerpt": {"description": "<PERSON><PERSON> tả ngắn của bài viết.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả ngắn củ<PERSON> bà<PERSON> vi<PERSON>, n<PERSON><PERSON> đư<PERSON><PERSON> lưu trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON><PERSON> đo<PERSON>n HTML của bài vi<PERSON>, dành cho hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không trích đoạn đư<PERSON><PERSON> bảo vệ bởi mật khẩu.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "comment_status": {"description": "<PERSON><PERSON>u trong bài viết có mở hay không mục bình luận.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "<PERSON><PERSON><PERSON> bài viết có thể được ping hay không.", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "<PERSON><PERSON><PERSON> dạng cho bài viết.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "<PERSON><PERSON><PERSON> bài viế<PERSON> đ<PERSON><PERSON><PERSON> ghim nổi bật hay không.", "type": "boolean", "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}, "categories": {"description": "Các term đ<PERSON><PERSON><PERSON> gán cho một bài viết của taxonomy category.", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "Các term đ<PERSON><PERSON><PERSON> gán cho một bài viết của taxonomy post_tag.", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp danh sách bằng thuộc tính của đối tượng.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> cầu b<PERSON><PERSON> buộc phải đúng vì bản thảo không hỗ trợ bỏ thùng rác.", "required": false}}}]}, "/wp/v2/posts/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "ID của tác giả của bài viết.", "type": "integer", "required": false}, "excerpt": {"description": "<PERSON><PERSON> tả ngắn của bài viết.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả ngắn củ<PERSON> bà<PERSON> vi<PERSON>, n<PERSON><PERSON> đư<PERSON><PERSON> lưu trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON><PERSON> đo<PERSON>n HTML của bài vi<PERSON>, dành cho hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không trích đoạn đư<PERSON><PERSON> bảo vệ bởi mật khẩu.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "comment_status": {"description": "<PERSON><PERSON>u trong bài viết có mở hay không mục bình luận.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "<PERSON><PERSON><PERSON> bài viết có thể được ping hay không.", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "<PERSON><PERSON><PERSON> dạng cho bài viết.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "<PERSON><PERSON><PERSON> bài viế<PERSON> đ<PERSON><PERSON><PERSON> ghim nổi bật hay không.", "type": "boolean", "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}, "categories": {"description": "Các term đ<PERSON><PERSON><PERSON> gán cho một bài viết của taxonomy category.", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "Các term đ<PERSON><PERSON><PERSON> gán cho một bài viết của taxonomy post_tag.", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "id": {"description": "ID của phiên bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/pages": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "after": {"description": "Giới hạn phản hồi chỉ cho các bài viết đư<PERSON><PERSON>ất bản sau một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết đư<PERSON><PERSON> sửa đổi sau một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Gi<PERSON>i hạn kết quả là các bài viết của các tác giả cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "<PERSON><PERSON><PERSON> bảo rằng kết quả đã loại bỏ những bài viết của các tác giả cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Giới hạn phản hồi chỉ cho các bài viết được Xuất bản trước một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết được sửa đổi trước một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "menu_order": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho bài viết có giá trị menu_order cụ thể.", "type": "integer", "required": false}, "search_semantics": {"description": "<PERSON><PERSON><PERSON> diễn g<PERSON><PERSON>i thông tin tìm kiếm.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của bài viết.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "parent": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho những bài viết thuộ<PERSON> về những ID cha cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả trừ những bài viết không thuộc về một ID cha cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "<PERSON><PERSON><PERSON> tên cột cần tìm kiếm.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Giới hạn kết quả thiết lập cho bài viết với một hoặc nhiều đường dẫn cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Gi<PERSON>i hạn kết quả cho các bài viết được gán một hoặc nhiều trạng thái.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "parent": {"description": "ID cha của bài viết.", "type": "integer", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "ID của tác giả của bài viết.", "type": "integer", "required": false}, "excerpt": {"description": "<PERSON><PERSON> tả ngắn của bài viết.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả ngắn củ<PERSON> bà<PERSON> vi<PERSON>, n<PERSON><PERSON> đư<PERSON><PERSON> lưu trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON><PERSON> đo<PERSON>n HTML của bài vi<PERSON>, dành cho hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không trích đoạn đư<PERSON><PERSON> bảo vệ bởi mật khẩu.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "comment_status": {"description": "<PERSON><PERSON>u trong bài viết có mở hay không mục bình luận.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "<PERSON><PERSON><PERSON> bài viết có thể được ping hay không.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "Thứ tự của bài viết tính theo sự liên quan với các bài viết khác.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/pages"}]}}, "/wp/v2/pages/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "<PERSON><PERSON> đè độ dài đoạn trích mặc định.", "type": "integer", "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> khẩu của bài viết đó nếu nó được bảo vệ.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "parent": {"description": "ID cha của bài viết.", "type": "integer", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "ID của tác giả của bài viết.", "type": "integer", "required": false}, "excerpt": {"description": "<PERSON><PERSON> tả ngắn của bài viết.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả ngắn củ<PERSON> bà<PERSON> vi<PERSON>, n<PERSON><PERSON> đư<PERSON><PERSON> lưu trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON><PERSON> đo<PERSON>n HTML của bài vi<PERSON>, dành cho hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không trích đoạn đư<PERSON><PERSON> bảo vệ bởi mật khẩu.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "comment_status": {"description": "<PERSON><PERSON>u trong bài viết có mở hay không mục bình luận.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "<PERSON><PERSON><PERSON> bài viết có thể được ping hay không.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "Thứ tự của bài viết tính theo sự liên quan với các bài viết khác.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp danh sách bằng thuộc tính của đối tượng.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> cầu b<PERSON><PERSON> buộc phải đúng vì bản thảo không hỗ trợ bỏ thùng rác.", "required": false}}}]}, "/wp/v2/pages/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "ID cha của bài viết.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "ID của tác giả của bài viết.", "type": "integer", "required": false}, "excerpt": {"description": "<PERSON><PERSON> tả ngắn của bài viết.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả ngắn củ<PERSON> bà<PERSON> vi<PERSON>, n<PERSON><PERSON> đư<PERSON><PERSON> lưu trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON><PERSON> đo<PERSON>n HTML của bài vi<PERSON>, dành cho hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không trích đoạn đư<PERSON><PERSON> bảo vệ bởi mật khẩu.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "comment_status": {"description": "<PERSON><PERSON>u trong bài viết có mở hay không mục bình luận.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "<PERSON><PERSON><PERSON> bài viết có thể được ping hay không.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "Thứ tự của bài viết tính theo sự liên quan với các bài viết khác.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "id": {"description": "ID của phiên bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/media": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "after": {"description": "Giới hạn phản hồi chỉ cho các bài viết đư<PERSON><PERSON>ất bản sau một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết đư<PERSON><PERSON> sửa đổi sau một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Gi<PERSON>i hạn kết quả là các bài viết của các tác giả cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "<PERSON><PERSON><PERSON> bảo rằng kết quả đã loại bỏ những bài viết của các tác giả cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Giới hạn phản hồi chỉ cho các bài viết được Xuất bản trước một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết được sửa đổi trước một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "<PERSON><PERSON><PERSON> diễn g<PERSON><PERSON>i thông tin tìm kiếm.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của bài viết.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "parent": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho những bài viết thuộ<PERSON> về những ID cha cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả trừ những bài viết không thuộc về một ID cha cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "<PERSON><PERSON><PERSON> tên cột cần tìm kiếm.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Giới hạn kết quả thiết lập cho bài viết với một hoặc nhiều đường dẫn cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "inherit", "description": "Gi<PERSON>i hạn kết quả cho các bài viết được gán một hoặc nhiều trạng thái.", "type": "array", "items": {"enum": ["inherit", "private", "trash"], "type": "string"}, "required": false}, "media_type": {"default": null, "description": "Gi<PERSON>i hạn kết quả chỉ gồm attachments có kiểu MIME cụ thể.", "type": "string", "enum": ["image", "video", "text", "application", "audio"], "required": false}, "mime_type": {"default": null, "description": "Gi<PERSON>i hạn kết quả chỉ gồm attachments có kiểu MIME cụ thể.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "ID của tác giả của bài viết.", "type": "integer", "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "comment_status": {"description": "<PERSON><PERSON>u trong bài viết có mở hay không mục bình luận.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "<PERSON><PERSON><PERSON> bài viết có thể được ping hay không.", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}, "alt_text": {"description": "Chữ hiện thị thay thế khi tệp tin đính kèm không thể hiển thị.", "type": "string", "required": false}, "caption": {"description": "<PERSON><PERSON> thích của tệp tin đ<PERSON>h k<PERSON>m.", "type": "object", "properties": {"raw": {"description": "Tiêu đề cho tệp tin đ<PERSON> kè<PERSON>, vì nó tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON>i<PERSON><PERSON> đề HTML cho tệp tin đ<PERSON>, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "<PERSON><PERSON><PERSON> tả của tệp tin đ<PERSON>h k<PERSON>m.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả cho file đ<PERSON>h k<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON> tả HTML cho file đ<PERSON><PERSON> k<PERSON>, dùng để hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "ID của bài viết đ<PERSON><PERSON><PERSON> đư<PERSON><PERSON> đ<PERSON>h với tệp tin đính kèm.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/media"}]}}, "/wp/v2/media/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "ID của tác giả của bài viết.", "type": "integer", "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "comment_status": {"description": "<PERSON><PERSON>u trong bài viết có mở hay không mục bình luận.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "<PERSON><PERSON><PERSON> bài viết có thể được ping hay không.", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}, "alt_text": {"description": "Chữ hiện thị thay thế khi tệp tin đính kèm không thể hiển thị.", "type": "string", "required": false}, "caption": {"description": "<PERSON><PERSON> thích của tệp tin đ<PERSON>h k<PERSON>m.", "type": "object", "properties": {"raw": {"description": "Tiêu đề cho tệp tin đ<PERSON> kè<PERSON>, vì nó tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON>i<PERSON><PERSON> đề HTML cho tệp tin đ<PERSON>, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "<PERSON><PERSON><PERSON> tả của tệp tin đ<PERSON>h k<PERSON>m.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả cho file đ<PERSON>h k<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON> tả HTML cho file đ<PERSON><PERSON> k<PERSON>, dùng để hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "ID của bài viết đ<PERSON><PERSON><PERSON> đư<PERSON><PERSON> đ<PERSON>h với tệp tin đính kèm.", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/post-process": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "ID định danh duy nhất của file đ<PERSON>h kèm.", "type": "integer", "required": false}, "action": {"type": "string", "enum": ["create-image-subsizes"], "required": true}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/edit": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"src": {"description": "URL tới file ảnh đã sửa.", "type": "string", "format": "uri", "required": true}, "modifiers": {"description": "<PERSON><PERSON>ng chỉnh sửa hình <PERSON>nh.", "type": "array", "minItems": 1, "items": {"description": "Chỉnh sửa hình <PERSON>nh.", "type": "object", "required": ["type", "args"], "oneOf": [{"title": "Xoay", "properties": {"type": {"description": "<PERSON><PERSON><PERSON>.", "type": "string", "enum": ["rotate"]}, "args": {"description": "<PERSON>ham số xoay vòng.", "type": "object", "required": ["angle"], "properties": {"angle": {"description": "<PERSON><PERSON><PERSON> xoay theo chiều kim đồng hồ theo độ.", "type": "number"}}}}}, {"title": "<PERSON><PERSON><PERSON>", "properties": {"type": {"description": "<PERSON><PERSON><PERSON>.", "type": "string", "enum": ["crop"]}, "args": {"description": "<PERSON><PERSON> số cắt.", "type": "object", "required": ["left", "top", "width", "height"], "properties": {"left": {"description": "<PERSON><PERSON> trí nằm ngang từ bên trái để bắt đầu cắt theo tỷ lệ phần trăm của chiều rộng hình ảnh.", "type": "number"}, "top": {"description": "<PERSON><PERSON> trí dọc từ trên xuống để bắt đầu cắt theo tỷ lệ phần trăm của chiều cao hình ảnh.", "type": "number"}, "width": {"description": "<PERSON><PERSON>u rộng của vùng cắt theo tỷ lệ phần trăm của chiều rộng hình ảnh.", "type": "number"}, "height": {"description": "<PERSON><PERSON>u cao của vùng cắt theo tỷ lệ phần trăm của chiều cao hình ảnh.", "type": "number"}}}}}]}, "required": false}, "rotation": {"description": "<PERSON><PERSON> độ xoay ảnh theo chiều kim đồng hồ. KHÔNG DÙNG NỮA: Thay vào đó, hãy dùng `modifiers`.", "type": "integer", "minimum": 0, "exclusiveMinimum": true, "maximum": 360, "exclusiveMaximum": true, "required": false}, "x": {"description": "<PERSON><PERSON><PERSON> theo phần trăm của hình ảnh, vị trí x để bắt đầu cắt xén. ĐÃ BỎ QUA: Thay vào đó, hãy sử dụng `modifiers`.", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "y": {"description": "<PERSON><PERSON><PERSON> theo phần trăm của hình ảnh, vị trí y để bắt đầu cắt xén. ĐÃ BỎ QUA: Thay vào đó, hãy sử dụng `modifiers`.", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "width": {"description": "<PERSON><PERSON><PERSON> theo phần trăm của hình ảnh, chiều rộng để cắt hình ảnh. ĐÃ BỎ QUA: <PERSON>hay vào đó, hãy sử dụng `modifiers`.", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "height": {"description": "<PERSON><PERSON><PERSON> theo phần trăm của hình ảnh, chiều cao để cắt hình ảnh. ĐÃ BỎ QUA: <PERSON>hay vào đó, hãy sử dụng `modifiers`.", "type": "number", "minimum": 0, "maximum": 100, "required": false}}}]}, "/wp/v2/menu-items": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "after": {"description": "Giới hạn phản hồi chỉ cho các bài viết đư<PERSON><PERSON>ất bản sau một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết đư<PERSON><PERSON> sửa đổi sau một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Giới hạn phản hồi chỉ cho các bài viết được Xuất bản trước một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết được sửa đổi trước một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "<PERSON><PERSON><PERSON> diễn g<PERSON><PERSON>i thông tin tìm kiếm.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp danh sách bằng thuộc tính của đối tượng.", "type": "string", "default": "menu_order", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "search_columns": {"default": [], "description": "<PERSON><PERSON><PERSON> tên cột cần tìm kiếm.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Giới hạn kết quả thiết lập cho bài viết với một hoặc nhiều đường dẫn cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Gi<PERSON>i hạn kết quả cho các bài viết được gán một hoặc nhiều trạng thái.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "Gi<PERSON>i hạn kết quả dựa trên mối quan hệ giữa nhiều nguyên tắc phân loại.", "type": "string", "enum": ["AND", "OR"], "required": false}, "menus": {"description": "Giới hạn bộ kết quả cho các mục có thuật ngữ cụ thể được chỉ định trong phân loại menus.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> ng<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> các thuật ngữ với các ID được liệt kê.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON><PERSON> v<PERSON>n <PERSON> lo<PERSON>i theo ID Thuật ngữ", "description": "<PERSON><PERSON><PERSON><PERSON> hiện một truy vấn thuật ngữ nâng cao.", "type": "object", "properties": {"terms": {"description": "ID của thuật ng<PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "Cho dù các mục đều được gán hoặc bất kỳ mục cụ thể nào.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "menus_exclude": {"description": "Giới hạn bộ kết quả cho các mục ngoại trừ những mục có thuật ngữ cụ thể được gán trong phân loại menus.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> ng<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> các thuật ngữ với các ID được liệt kê.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON><PERSON> v<PERSON>n <PERSON> lo<PERSON>i theo ID Thuật ngữ", "description": "<PERSON><PERSON><PERSON><PERSON> hiện một truy vấn thuật ngữ nâng cao.", "type": "object", "properties": {"terms": {"description": "ID của thuật ng<PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "menu_order": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho bài viết có giá trị menu_order cụ thể.", "type": "integer", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"title": {"description": "<PERSON>i<PERSON><PERSON> đề cho đối tượ<PERSON>.", "type": ["string", "object"], "properties": {"raw": {"description": "Tiêu đề cho đối tư<PERSON>, như nó đã tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON><PERSON> đề HTML cho đố<PERSON>, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"default": "custom", "description": "<PERSON>ộ đối tượng được đại diện ban đầu, chẳng hạn như \"post_type\" hoặc \"taxonomy\".", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"default": "publish", "description": "Trạng thái được đặt tên cho đối tượng.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "parent": {"default": 0, "description": "ID mẹ của đối tượng.", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "<PERSON><PERSON><PERSON> dung cho thuộc tính tiêu đề của thành phần liên kết cho mục menu này.", "type": "string", "required": false}, "classes": {"description": "<PERSON><PERSON><PERSON> lớp cho thành phần liên kết của mục menu này.", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "<PERSON><PERSON> tả của mục menu này.", "type": "string", "required": false}, "menu_order": {"default": 1, "description": "ID DB của nav_menu_item là nguồn gốc menu của mục nà<PERSON>, nếu có, nếu không thì là 0.", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "<PERSON><PERSON>i đối tượng được đại di<PERSON>, chẳng hạn như \"danh mục\", \"bài viết\" hoặc \"tệp tin đính kèm\".", "type": "string", "required": false}, "object_id": {"default": 0, "description": "ID cơ sở dữ liệu của đối tượng ban đầu mà mục menu này đại diện, ví dụ: ID cho các bài viết và term_id cho các danh mục.", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đích của thành phần liên kết cho mục menu này.", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "URL mà mục menu này trỏ đến.", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "<PERSON><PERSON><PERSON> quan hệ XFN được thể hiện trong liên kết của mục menu này.", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "Thu<PERSON>t ngữ được gán cho một đối tượng của phân loại bài viết nav_menu.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/menu-items"}]}}, "/wp/v2/menu-items/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho đối tượ<PERSON>.", "type": ["string", "object"], "properties": {"raw": {"description": "Tiêu đề cho đối tư<PERSON>, như nó đã tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON><PERSON> đề HTML cho đố<PERSON>, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "<PERSON>ộ đối tượng được đại diện ban đầu, chẳng hạn như \"post_type\" hoặc \"taxonomy\".", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "Trạng thái được đặt tên cho đối tượng.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "parent": {"description": "ID mẹ của đối tượng.", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "<PERSON><PERSON><PERSON> dung cho thuộc tính tiêu đề của thành phần liên kết cho mục menu này.", "type": "string", "required": false}, "classes": {"description": "<PERSON><PERSON><PERSON> lớp cho thành phần liên kết của mục menu này.", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "<PERSON><PERSON> tả của mục menu này.", "type": "string", "required": false}, "menu_order": {"description": "ID DB của nav_menu_item là nguồn gốc menu của mục nà<PERSON>, nếu có, nếu không thì là 0.", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "<PERSON><PERSON>i đối tượng được đại di<PERSON>, chẳng hạn như \"danh mục\", \"bài viết\" hoặc \"tệp tin đính kèm\".", "type": "string", "required": false}, "object_id": {"description": "ID cơ sở dữ liệu của đối tượng ban đầu mà mục menu này đại diện, ví dụ: ID cho các bài viết và term_id cho các danh mục.", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đích của thành phần liên kết cho mục menu này.", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "URL mà mục menu này trỏ đến.", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "<PERSON><PERSON><PERSON> quan hệ XFN được thể hiện trong liên kết của mục menu này.", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "Thu<PERSON>t ngữ được gán cho một đối tượng của phân loại bài viết nav_menu.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/menu-items/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "ID mẹ của đối tượng.", "type": "integer", "minimum": 0, "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho đối tượ<PERSON>.", "type": ["string", "object"], "properties": {"raw": {"description": "Tiêu đề cho đối tư<PERSON>, như nó đã tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON><PERSON> đề HTML cho đố<PERSON>, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "<PERSON>ộ đối tượng được đại diện ban đầu, chẳng hạn như \"post_type\" hoặc \"taxonomy\".", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "Trạng thái được đặt tên cho đối tượng.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "attr_title": {"description": "<PERSON><PERSON><PERSON> dung cho thuộc tính tiêu đề của thành phần liên kết cho mục menu này.", "type": "string", "required": false}, "classes": {"description": "<PERSON><PERSON><PERSON> lớp cho thành phần liên kết của mục menu này.", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "<PERSON><PERSON> tả của mục menu này.", "type": "string", "required": false}, "menu_order": {"description": "ID DB của nav_menu_item là nguồn gốc menu của mục nà<PERSON>, nếu có, nếu không thì là 0.", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "<PERSON><PERSON>i đối tượng được đại di<PERSON>, chẳng hạn như \"danh mục\", \"bài viết\" hoặc \"tệp tin đính kèm\".", "type": "string", "required": false}, "object_id": {"description": "ID cơ sở dữ liệu của đối tượng ban đầu mà mục menu này đại diện, ví dụ: ID cho các bài viết và term_id cho các danh mục.", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đích của thành phần liên kết cho mục menu này.", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "URL mà mục menu này trỏ đến.", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "<PERSON><PERSON><PERSON> quan hệ XFN được thể hiện trong liên kết của mục menu này.", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "Thu<PERSON>t ngữ được gán cho một đối tượng của phân loại bài viết nav_menu.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}]}, "/wp/v2/menu-items/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "id": {"description": "ID của phiên bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/blocks": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "after": {"description": "Giới hạn phản hồi chỉ cho các bài viết đư<PERSON><PERSON>ất bản sau một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết đư<PERSON><PERSON> sửa đổi sau một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Giới hạn phản hồi chỉ cho các bài viết được Xuất bản trước một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết được sửa đổi trước một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "<PERSON><PERSON><PERSON> diễn g<PERSON><PERSON>i thông tin tìm kiếm.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của bài viết.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "<PERSON><PERSON><PERSON> tên cột cần tìm kiếm.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Giới hạn kết quả thiết lập cho bài viết với một hoặc nhiều đường dẫn cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Gi<PERSON>i hạn kết quả cho các bài viết được gán một hoặc nhiều trạng thái.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "Gi<PERSON>i hạn kết quả dựa trên mối quan hệ giữa nhiều nguyên tắc phân loại.", "type": "string", "enum": ["AND", "OR"], "required": false}, "wp_pattern_category": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn bộ kết quả cho các mục có thuật ngữ cụ thể được chỉ định trong phân loại wp_pattern_category.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> ng<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> các thuật ngữ với các ID được liệt kê.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON><PERSON> v<PERSON>n <PERSON> lo<PERSON>i theo ID Thuật ngữ", "description": "<PERSON><PERSON><PERSON><PERSON> hiện một truy vấn thuật ngữ nâng cao.", "type": "object", "properties": {"terms": {"description": "ID của thuật ng<PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "Cho dù các mục đều được gán hoặc bất kỳ mục cụ thể nào.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "wp_pattern_category_exclude": {"description": "Giớ<PERSON> hạn bộ kết quả cho các mục ngoại trừ những mục có thuật ngữ cụ thể được gán trong phân loại wp_pattern_category.", "type": ["object", "array"], "oneOf": [{"title": "<PERSON><PERSON> ng<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> các thuật ngữ với các ID được liệt kê.", "type": "array", "items": {"type": "integer"}}, {"title": "<PERSON><PERSON><PERSON> v<PERSON>n <PERSON> lo<PERSON>i theo ID Thuật ngữ", "description": "<PERSON><PERSON><PERSON><PERSON> hiện một truy vấn thuật ngữ nâng cao.", "type": "object", "properties": {"terms": {"description": "ID của thuật ng<PERSON>.", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "<PERSON><PERSON> tả ngắn của bài viết.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả ngắn củ<PERSON> bà<PERSON> vi<PERSON>, n<PERSON><PERSON> đư<PERSON><PERSON> lưu trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON><PERSON> đo<PERSON>n HTML của bài vi<PERSON>, dành cho hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không trích đoạn đư<PERSON><PERSON> bảo vệ bởi mật khẩu.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}, "wp_pattern_category": {"description": "Các term đ<PERSON><PERSON><PERSON> gán cho một bài viết của taxonomy wp_pattern_category.", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/blocks"}]}}, "/wp/v2/blocks/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "<PERSON><PERSON> đè độ dài đoạn trích mặc định.", "type": "integer", "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> khẩu của bài viết đó nếu nó được bảo vệ.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "<PERSON><PERSON> tả ngắn của bài viết.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả ngắn củ<PERSON> bà<PERSON> vi<PERSON>, n<PERSON><PERSON> đư<PERSON><PERSON> lưu trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON><PERSON> đo<PERSON>n HTML của bài vi<PERSON>, dành cho hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không trích đoạn đư<PERSON><PERSON> bảo vệ bởi mật khẩu.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}, "wp_pattern_category": {"description": "Các term đ<PERSON><PERSON><PERSON> gán cho một bài viết của taxonomy wp_pattern_category.", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp danh sách bằng thuộc tính của đối tượng.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> cầu b<PERSON><PERSON> buộc phải đúng vì bản thảo không hỗ trợ bỏ thùng rác.", "required": false}}}]}, "/wp/v2/blocks/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "<PERSON><PERSON> tả ngắn của bài viết.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON> tả ngắn củ<PERSON> bà<PERSON> vi<PERSON>, n<PERSON><PERSON> đư<PERSON><PERSON> lưu trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON><PERSON> đo<PERSON>n HTML của bài vi<PERSON>, dành cho hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không trích đoạn đư<PERSON><PERSON> bảo vệ bởi mật khẩu.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}, "wp_pattern_category": {"description": "Các term đ<PERSON><PERSON><PERSON> gán cho một bài viết của taxonomy wp_pattern_category.", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "id": {"description": "ID của phiên bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp danh sách bằng thuộc tính của đối tượng.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> cầu b<PERSON><PERSON> buộc phải đúng vì bản thảo không hỗ trợ bỏ thùng rác.", "required": false}}}]}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "slug": {"description": "Slug duy nhất định danh cho mẫu.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "<PERSON><PERSON><PERSON> danh giao diện của mẫu.", "type": "string", "required": false}, "type": {"description": "Loại mẫu.", "type": "string", "required": false}, "content": {"description": "<PERSON><PERSON>i dung của mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung cho mẫu, vì nó tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản của định dạng khối nội dung được sử dụng bởi mẫu.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "<PERSON>ên mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "Tên template, trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Tiêu đề HTML cho mẫu, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "<PERSON><PERSON> tả của mẫu.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái của mẫu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "ID cho tác giả của mẫu.", "type": "integer", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "id": {"description": "ID của phiên bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "Giới hạn đối với id bài viết được chỉ định.", "type": "integer", "required": false}, "area": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn vùng chứa thành phần mẫu trang cụ thể.", "type": "string", "required": false}, "post_type": {"description": "<PERSON><PERSON><PERSON> bài viết sẽ được nhận mẫu.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "Slug duy nhất định danh cho mẫu.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "<PERSON><PERSON><PERSON> danh giao diện của mẫu.", "type": "string", "required": false}, "type": {"description": "Loại mẫu.", "type": "string", "required": false}, "content": {"default": "", "description": "<PERSON><PERSON>i dung của mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung cho mẫu, vì nó tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản của định dạng khối nội dung được sử dụng bởi mẫu.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "<PERSON>ên mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "Tên template, trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Tiêu đề HTML cho mẫu, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "<PERSON><PERSON> tả của mẫu.", "type": "string", "required": false}, "status": {"default": "publish", "description": "Tr<PERSON>ng thái của mẫu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "ID cho tác giả của mẫu.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/templates"}]}}, "/wp/v2/templates/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "Slug của mẫu để lấy phần dự phòng cho", "type": "string", "required": true}, "is_custom": {"description": "Chỉ ra nếu một mẫu là tùy chỉnh hoặc là một phần của hệ thống mẫu", "type": "boolean", "required": false}, "template_prefix": {"description": "Tiền tố mẫu cho mẫu đã tạo. Đi<PERSON>u này được sử dụng để trích xuất loại mẫu chính, ví dụ: trong `sách phân loại` trích xuất `phân loại`", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/templates/lookup"}]}}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "slug": {"description": "Slug duy nhất định danh cho mẫu.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "<PERSON><PERSON><PERSON> danh giao diện của mẫu.", "type": "string", "required": false}, "type": {"description": "Loại mẫu.", "type": "string", "required": false}, "content": {"description": "<PERSON><PERSON>i dung của mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung cho mẫu, vì nó tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản của định dạng khối nội dung được sử dụng bởi mẫu.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "<PERSON>ên mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "Tên template, trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Tiêu đề HTML cho mẫu, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "<PERSON><PERSON> tả của mẫu.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái của mẫu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "ID cho tác giả của mẫu.", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp danh sách bằng thuộc tính của đối tượng.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> cầu b<PERSON><PERSON> buộc phải đúng vì bản thảo không hỗ trợ bỏ thùng rác.", "required": false}}}]}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "slug": {"description": "Slug duy nhất định danh cho mẫu.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "<PERSON><PERSON><PERSON> danh giao diện của mẫu.", "type": "string", "required": false}, "type": {"description": "Loại mẫu.", "type": "string", "required": false}, "content": {"description": "<PERSON><PERSON>i dung của mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung cho mẫu, vì nó tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản của định dạng khối nội dung được sử dụng bởi mẫu.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "<PERSON>ên mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "Tên template, trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Tiêu đề HTML cho mẫu, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "<PERSON><PERSON> tả của mẫu.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái của mẫu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "ID cho tác giả của mẫu.", "type": "integer", "required": false}, "area": {"description": "<PERSON><PERSON><PERSON> thành phần mẫu trang được dự định sử dụng (đầu trang, chân trang, v.v.)", "type": "string", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "id": {"description": "ID của phiên bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/template-parts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "Giới hạn đối với id bài viết được chỉ định.", "type": "integer", "required": false}, "area": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn vùng chứa thành phần mẫu trang cụ thể.", "type": "string", "required": false}, "post_type": {"description": "<PERSON><PERSON><PERSON> bài viết sẽ được nhận mẫu.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "Slug duy nhất định danh cho mẫu.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "<PERSON><PERSON><PERSON> danh giao diện của mẫu.", "type": "string", "required": false}, "type": {"description": "Loại mẫu.", "type": "string", "required": false}, "content": {"default": "", "description": "<PERSON><PERSON>i dung của mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung cho mẫu, vì nó tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản của định dạng khối nội dung được sử dụng bởi mẫu.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "<PERSON>ên mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "Tên template, trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Tiêu đề HTML cho mẫu, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "<PERSON><PERSON> tả của mẫu.", "type": "string", "required": false}, "status": {"default": "publish", "description": "Tr<PERSON>ng thái của mẫu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "ID cho tác giả của mẫu.", "type": "integer", "required": false}, "area": {"description": "<PERSON><PERSON><PERSON> thành phần mẫu trang được dự định sử dụng (đầu trang, chân trang, v.v.)", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/template-parts"}]}}, "/wp/v2/template-parts/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "Slug của mẫu để lấy phần dự phòng cho", "type": "string", "required": true}, "is_custom": {"description": "Chỉ ra nếu một mẫu là tùy chỉnh hoặc là một phần của hệ thống mẫu", "type": "boolean", "required": false}, "template_prefix": {"description": "Tiền tố mẫu cho mẫu đã tạo. Đi<PERSON>u này được sử dụng để trích xuất loại mẫu chính, ví dụ: trong `sách phân loại` trích xuất `phân loại`", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/template-parts/lookup"}]}}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "slug": {"description": "Slug duy nhất định danh cho mẫu.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "<PERSON><PERSON><PERSON> danh giao diện của mẫu.", "type": "string", "required": false}, "type": {"description": "Loại mẫu.", "type": "string", "required": false}, "content": {"description": "<PERSON><PERSON>i dung của mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung cho mẫu, vì nó tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản của định dạng khối nội dung được sử dụng bởi mẫu.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "<PERSON>ên mẫu.", "type": ["object", "string"], "properties": {"raw": {"description": "Tên template, trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Tiêu đề HTML cho mẫu, đ<PERSON><PERSON><PERSON> chuyển đổi để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "<PERSON><PERSON> tả của mẫu.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái của mẫu.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "ID cho tác giả của mẫu.", "type": "integer", "required": false}, "area": {"description": "<PERSON><PERSON><PERSON> thành phần mẫu trang được dự định sử dụng (đầu trang, chân trang, v.v.)", "type": "string", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của cha mẹ của bản sửa đổi kiểu chung.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON> định danh duy nhất cho bản sửa đổi kiểu toàn cầu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[\\/\\s%\\w\\.\\(\\)\\[\\]\\@_\\-]+)/variations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "<PERSON>ư<PERSON><PERSON> xác định chủ đề", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "<PERSON>ư<PERSON><PERSON> xác định chủ đề", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/(?P<id>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"id": {"description": "Id c<PERSON>a mẫu", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": false}, "args": {"styles": {"description": "<PERSON><PERSON><PERSON> dáng <PERSON> c<PERSON>.", "type": ["object"], "required": false}, "settings": {"description": "<PERSON><PERSON><PERSON> h<PERSON>nh chung.", "type": ["object"], "required": false}, "title": {"description": "Tiêu đề cho biến thể kiểu toàn cục.", "type": ["object", "string"], "properties": {"raw": {"description": "Tựa đề cho biến thể kiểu toàn cục, vì nó tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}}}]}, "/wp/v2/navigation": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "after": {"description": "Giới hạn phản hồi chỉ cho các bài viết đư<PERSON><PERSON>ất bản sau một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết đư<PERSON><PERSON> sửa đổi sau một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Giới hạn phản hồi chỉ cho các bài viết được Xuất bản trước một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết được sửa đổi trước một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "<PERSON><PERSON><PERSON> diễn g<PERSON><PERSON>i thông tin tìm kiếm.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của bài viết.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "<PERSON><PERSON><PERSON> tên cột cần tìm kiếm.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Giới hạn kết quả thiết lập cho bài viết với một hoặc nhiều đường dẫn cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Gi<PERSON>i hạn kết quả cho các bài viết được gán một hoặc nhiều trạng thái.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/navigation"}]}}, "/wp/v2/navigation/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> khẩu của bài viết đó nếu nó được bảo vệ.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp danh sách bằng thuộc tính của đối tượng.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> cầu b<PERSON><PERSON> buộc phải đúng vì bản thảo không hỗ trợ bỏ thùng rác.", "required": false}}}]}, "/wp/v2/navigation/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "id": {"description": "ID của phiên bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/font-families": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "<PERSON><PERSON><PERSON> diễn g<PERSON><PERSON>i thông tin tìm kiếm.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của bài viết.", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}, "slug": {"description": "Giới hạn kết quả thiết lập cho bài viết với một hoặc nhiều đường dẫn cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "args": {"theme_json_version": {"description": "<PERSON><PERSON><PERSON> bản c<PERSON><PERSON> lư<PERSON><PERSON> đồ theme.j<PERSON> được sử dụng cho cài đặt kiểu chữ.", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "<PERSON><PERSON> b<PERSON><PERSON> họ phông chữ ở định dạng theme.<PERSON><PERSON>, đ<PERSON><PERSON><PERSON> mã hóa dưới dạng chuỗi.", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/font-families"}]}}, "/wp/v2/font-families/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "theme_json_version": {"description": "<PERSON><PERSON><PERSON> bản c<PERSON><PERSON> lư<PERSON><PERSON> đồ theme.j<PERSON> được sử dụng cho cài đặt kiểu chữ.", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "<PERSON><PERSON> b<PERSON><PERSON> họ phông chữ ở định dạng theme.<PERSON><PERSON>, đ<PERSON><PERSON><PERSON> mã hóa dưới dạng chuỗi.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "ID của họ phông chữ gốc của mặt phông chữ.", "type": "integer", "required": true}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "<PERSON><PERSON><PERSON> diễn g<PERSON><PERSON>i thông tin tìm kiếm.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của bài viết.", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}}}, {"methods": ["POST"], "args": {"font_family_id": {"description": "ID của họ phông chữ gốc của mặt phông chữ.", "type": "integer", "required": true}, "theme_json_version": {"description": "<PERSON><PERSON><PERSON> bản c<PERSON><PERSON> lư<PERSON><PERSON> đồ theme.j<PERSON> được sử dụng cho cài đặt kiểu chữ.", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_face_settings": {"description": "<PERSON><PERSON> báo phông chữ ở định dạng theme.<PERSON><PERSON>, đ<PERSON><PERSON><PERSON> mã hóa dưới dạng chuỗi.", "type": "string", "required": true}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "ID của họ phông chữ gốc của mặt phông chữ.", "type": "integer", "required": true}, "id": {"description": "<PERSON><PERSON> định danh duy nhất cho mặt phông chữ.", "type": "integer", "required": true}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"font_family_id": {"description": "ID của họ phông chữ gốc của mặt phông chữ.", "type": "integer", "required": true}, "id": {"description": "<PERSON><PERSON> định danh duy nhất cho mặt phông chữ.", "type": "integer", "required": true}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/ux-blocks": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "after": {"description": "Giới hạn phản hồi chỉ cho các bài viết đư<PERSON><PERSON>ất bản sau một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết đư<PERSON><PERSON> sửa đổi sau một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Giới hạn phản hồi chỉ cho các bài viết được Xuất bản trước một ngày theo định dạng ISO8601.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn phản hồi cho các bài viết được sửa đổi trước một ngày tuân thủ ISO8601 đã cho.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "<PERSON><PERSON><PERSON> diễn g<PERSON><PERSON>i thông tin tìm kiếm.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của bài viết.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "parent": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho những bài viết thuộ<PERSON> về những ID cha cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả trừ những bài viết không thuộc về một ID cha cụ thể.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "<PERSON><PERSON><PERSON> tên cột cần tìm kiếm.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Giới hạn kết quả thiết lập cho bài viết với một hoặc nhiều đường dẫn cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Gi<PERSON>i hạn kết quả cho các bài viết được gán một hoặc nhiều trạng thái.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "parent": {"description": "ID cha của bài viết.", "type": "integer", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/ux-blocks"}]}}, "/wp/v2/ux-blocks/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> khẩu của bài viết đó nếu nó được bảo vệ.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "parent": {"description": "ID cha của bài viết.", "type": "integer", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bài viết.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}}}]}, "/wp/v2/ux-blocks/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp danh sách bằng thuộc tính của đối tượng.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/ux-blocks/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "ID của phiên bản bản thảo cha.", "type": "integer", "required": false}, "id": {"description": "<PERSON><PERSON><PERSON> riêng c<PERSON>a bản thảo.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> cầu b<PERSON><PERSON> buộc phải đúng vì bản thảo không hỗ trợ bỏ thùng rác.", "required": false}}}]}, "/wp/v2/ux-blocks/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "ID cha của bài viết.", "type": "integer", "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> bà<PERSON> vi<PERSON><PERSON> đ<PERSON><PERSON><PERSON>u<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hành bài vi<PERSON>, theo giờ GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho bài viết duy nhất theo loại của bài viết.", "type": "string", "required": false}, "status": {"description": "Tr<PERSON>ng thái có tên của bài viết.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u để bảo vệ quyền truy cập vào nội dung và mô tả ngắn.", "type": "string", "required": false}, "title": {"description": "<PERSON>i<PERSON><PERSON> đề cho bài viết.", "type": "object", "properties": {"raw": {"description": "Tiêu đề của bài viế<PERSON>, nh<PERSON> có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "Ti<PERSON>u đề HTML của bà<PERSON> viế<PERSON>, dùng để hiển thị. ", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung của bài viế<PERSON>.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung của b<PERSON><PERSON> vi<PERSON>, nếu nó có sẵn trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML củ<PERSON> bà<PERSON> v<PERSON>, dư<PERSON><PERSON> dạng hiển thị.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "<PERSON><PERSON><PERSON> bản định dạng của nội dung khối được sử dụng bởi bài viết.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "<PERSON><PERSON> <PERSON> không nội dung đư<PERSON><PERSON> bảo vệ bởi mật khẩu. ", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "ID ảnh đại diện của bài viết.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "File của giao diện sử dụng để hiển thị bài viết.", "type": "string", "required": false}}}]}, "/wp/v2/ux-blocks/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "ID cha của bản tự động lưu.", "type": "integer", "required": false}, "id": {"description": "ID của phiên bản tự động lưu.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/types"}]}}, "/wp/v2/types/(?P<type>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"type": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho loại bài viết.", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/statuses": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/statuses"}]}}, "/wp/v2/statuses/(?P<status>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "<PERSON><PERSON><PERSON> danh chữ và số cho trạng thái.", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/taxonomies": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "type": {"description": "Giới hạn kết quả cho các taxonomy đư<PERSON>c gán cho một loại bài viết cụ thể.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/taxonomies"}]}}, "/wp/v2/taxonomies/(?P<taxonomy>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"taxonomy": {"description": "Chữ và số xác nhận để phân loại.", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/categories": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xế<PERSON> bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của term.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Ẩn hay không các term được không được gán cho bất kỳ bài viết nào.", "type": "boolean", "default": false, "required": false}, "parent": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho các term đư<PERSON>c gán cho term cha cụ thể.", "type": "integer", "required": false}, "post": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho các term đư<PERSON><PERSON> gán cho một bài viết cụ thể.", "type": "integer", "default": null, "required": false}, "slug": {"description": "Giới hạn kết quả cho những terms với một hoặc nhiều slugs cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "<PERSON><PERSON> tả HTML cho tài nguyên.", "type": "string", "required": false}, "name": {"description": "Tiêu đề HTML cho term.", "type": "string", "required": true}, "slug": {"description": "Một alphanumeric identifier cho term duy nhất tới loại của nó.", "type": "string", "required": false}, "parent": {"description": "<PERSON> thuật ngữ gốc.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/categories"}]}}, "/wp/v2/categories/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "description": {"description": "<PERSON><PERSON> tả HTML cho tài nguyên.", "type": "string", "required": false}, "name": {"description": "Tiêu đề HTML cho term.", "type": "string", "required": false}, "slug": {"description": "Một alphanumeric identifier cho term duy nhất tới loại của nó.", "type": "string", "required": false}, "parent": {"description": "<PERSON> thuật ngữ gốc.", "type": "integer", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON>, c<PERSON><PERSON> thu<PERSON>t ngữ không thể bị xóa.", "required": false}}}]}, "/wp/v2/tags": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xế<PERSON> bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của term.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Ẩn hay không các term được không được gán cho bất kỳ bài viết nào.", "type": "boolean", "default": false, "required": false}, "post": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho các term đư<PERSON><PERSON> gán cho một bài viết cụ thể.", "type": "integer", "default": null, "required": false}, "slug": {"description": "Giới hạn kết quả cho những terms với một hoặc nhiều slugs cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "<PERSON><PERSON> tả HTML cho tài nguyên.", "type": "string", "required": false}, "name": {"description": "Tiêu đề HTML cho term.", "type": "string", "required": true}, "slug": {"description": "Một alphanumeric identifier cho term duy nhất tới loại của nó.", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/tags"}]}}, "/wp/v2/tags/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "description": {"description": "<PERSON><PERSON> tả HTML cho tài nguyên.", "type": "string", "required": false}, "name": {"description": "Tiêu đề HTML cho term.", "type": "string", "required": false}, "slug": {"description": "Một alphanumeric identifier cho term duy nhất tới loại của nó.", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON>, c<PERSON><PERSON> thu<PERSON>t ngữ không thể bị xóa.", "required": false}}}]}, "/wp/v2/menus": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xế<PERSON> bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của term.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Ẩn hay không các term được không được gán cho bất kỳ bài viết nào.", "type": "boolean", "default": false, "required": false}, "post": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho các term đư<PERSON><PERSON> gán cho một bài viết cụ thể.", "type": "integer", "default": null, "required": false}, "slug": {"description": "Giới hạn kết quả cho những terms với một hoặc nhiều slugs cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "<PERSON><PERSON> tả HTML cho tài nguyên.", "type": "string", "required": false}, "name": {"description": "Tiêu đề HTML cho term.", "type": "string", "required": true}, "slug": {"description": "Một alphanumeric identifier cho term duy nhất tới loại của nó.", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}, "locations": {"description": "Những vị trí chỉ định cho menu.", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "<PERSON><PERSON> tự động thêm các trang mẹ vào menu này hay không.", "type": "boolean", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/menus"}]}}, "/wp/v2/menus/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "description": {"description": "<PERSON><PERSON> tả HTML cho tài nguyên.", "type": "string", "required": false}, "name": {"description": "Tiêu đề HTML cho term.", "type": "string", "required": false}, "slug": {"description": "Một alphanumeric identifier cho term duy nhất tới loại của nó.", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}, "locations": {"description": "Những vị trí chỉ định cho menu.", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "<PERSON><PERSON> tự động thêm các trang mẹ vào menu này hay không.", "type": "boolean", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON>, c<PERSON><PERSON> thu<PERSON>t ngữ không thể bị xóa.", "required": false}}}]}, "/wp/v2/wp_pattern_category": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xế<PERSON> bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của term.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Ẩn hay không các term được không được gán cho bất kỳ bài viết nào.", "type": "boolean", "default": false, "required": false}, "post": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho các term đư<PERSON><PERSON> gán cho một bài viết cụ thể.", "type": "integer", "default": null, "required": false}, "slug": {"description": "Giới hạn kết quả cho những terms với một hoặc nhiều slugs cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "<PERSON><PERSON> tả HTML cho tài nguyên.", "type": "string", "required": false}, "name": {"description": "Tiêu đề HTML cho term.", "type": "string", "required": true}, "slug": {"description": "Một alphanumeric identifier cho term duy nhất tới loại của nó.", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/wp_pattern_category"}]}}, "/wp/v2/wp_pattern_category/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "description": {"description": "<PERSON><PERSON> tả HTML cho tài nguyên.", "type": "string", "required": false}, "name": {"description": "Tiêu đề HTML cho term.", "type": "string", "required": false}, "slug": {"description": "Một alphanumeric identifier cho term duy nhất tới loại của nó.", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> xác nhận cho thuật ngữ", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON>, c<PERSON><PERSON> thu<PERSON>t ngữ không thể bị xóa.", "required": false}}}]}, "/wp/v2/users": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"default": "asc", "description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "enum": ["asc", "desc"], "type": "string", "required": false}, "orderby": {"default": "name", "description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h ngư<PERSON>i dùng.", "enum": ["id", "include", "name", "registered_date", "slug", "include_slugs", "email", "url"], "type": "string", "required": false}, "slug": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho những người dùng có một hoặc nhiều slugs cụ thể.", "type": "array", "items": {"type": "string"}, "required": false}, "roles": {"description": "Giới hạn kết quả cho những người dùng có ít nhất một vai trò cụ thể được cung cấp. Chấp nhận danh sách csv hoặc một vai trò đơn lẻ.", "type": "array", "items": {"type": "string"}, "required": false}, "capabilities": {"description": "Giới hạn kết quả được đặt cho người dùng phù hợp với ít nhất một khả năng cụ thể được cung cấp. Chấp nhận danh sách csv hoặc khả năng đơn lẻ.", "type": "array", "items": {"type": "string"}, "required": false}, "who": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả cho những người dùng được xem là tác giả.", "type": "string", "enum": ["authors"], "required": false}, "has_published_posts": {"description": "<PERSON><PERSON><PERSON>i hạn kết quả được đặt cho những người dùng đã từng xuất bản bài.", "type": ["boolean", "array"], "items": {"type": "string", "enum": {"post": "post", "page": "page", "attachment": "attachment", "nav_menu_item": "nav_menu_item", "wp_block": "wp_block", "wp_template": "wp_template", "wp_template_part": "wp_template_part", "wp_global_styles": "wp_global_styles", "wp_navigation": "wp_navigation", "wp_font_family": "wp_font_family", "wp_font_face": "wp_font_face", "blocks": "blocks"}}, "required": false}, "search_columns": {"default": [], "description": "<PERSON><PERSON><PERSON> tên cột cần tìm kiếm.", "type": "array", "items": {"enum": ["email", "name", "id", "username", "slug"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"username": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập cho người dùng.", "type": "string", "required": true}, "name": {"description": "<PERSON><PERSON><PERSON> hiển thị.", "type": "string", "required": false}, "first_name": {"description": "<PERSON><PERSON><PERSON><PERSON> sử dụng.", "type": "string", "required": false}, "last_name": {"description": "<PERSON><PERSON> ng<PERSON>ời sử dụng.", "type": "string", "required": false}, "email": {"description": "Địa chỉ email cho tài nguyên.", "type": "string", "format": "email", "required": true}, "url": {"description": "Đường dẫn của người sử dụng", "type": "string", "format": "uri", "required": false}, "description": {"description": "<PERSON><PERSON> tả của ngư<PERSON>i dùng.", "type": "string", "required": false}, "locale": {"description": "<PERSON><PERSON><PERSON> ngữ cho người sử dụng", "type": "string", "enum": ["", "en_US", "vi"], "required": false}, "nickname": {"description": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>u cho người dùng.", "type": "string", "required": false}, "slug": {"description": "Một ID bằng chữ và số cho thành viên.", "type": "string", "required": false}, "roles": {"description": "<PERSON><PERSON> (roles) của người dùng", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u cho người sử dụng (không bao giờ bao gồm).", "type": "string", "required": true}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "<PERSON><PERSON><PERSON> và giờ các tùy chọn đ<PERSON><PERSON><PERSON> cập nh<PERSON>.", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/users"}]}}, "/wp/v2/users/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho các tài nguyên.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho các tài nguyên.", "type": "integer", "required": false}, "username": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập cho người dùng.", "type": "string", "required": false}, "name": {"description": "<PERSON><PERSON><PERSON> hiển thị.", "type": "string", "required": false}, "first_name": {"description": "<PERSON><PERSON><PERSON><PERSON> sử dụng.", "type": "string", "required": false}, "last_name": {"description": "<PERSON><PERSON> ng<PERSON>ời sử dụng.", "type": "string", "required": false}, "email": {"description": "Địa chỉ email cho tài nguyên.", "type": "string", "format": "email", "required": false}, "url": {"description": "Đường dẫn của người sử dụng", "type": "string", "format": "uri", "required": false}, "description": {"description": "<PERSON><PERSON> tả của ngư<PERSON>i dùng.", "type": "string", "required": false}, "locale": {"description": "<PERSON><PERSON><PERSON> ngữ cho người sử dụng", "type": "string", "enum": ["", "en_US", "vi"], "required": false}, "nickname": {"description": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>u cho người dùng.", "type": "string", "required": false}, "slug": {"description": "Một ID bằng chữ và số cho thành viên.", "type": "string", "required": false}, "roles": {"description": "<PERSON><PERSON> (roles) của người dùng", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u cho người sử dụng (không bao giờ bao gồm).", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "<PERSON><PERSON><PERSON> và giờ các tùy chọn đ<PERSON><PERSON><PERSON> cập nh<PERSON>.", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho các tài nguyên.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> buộ<PERSON> là true, vì người sử dụng không hỗ trợ đưa vào thùng rác.", "required": false}, "reassign": {"type": "integer", "description": "<PERSON><PERSON> lại bài viết và các liên kết của người dùng bị xóa cho người dùng với ID này.", "required": true}}}]}, "/wp/v2/users/me": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"username": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập cho người dùng.", "type": "string", "required": false}, "name": {"description": "<PERSON><PERSON><PERSON> hiển thị.", "type": "string", "required": false}, "first_name": {"description": "<PERSON><PERSON><PERSON><PERSON> sử dụng.", "type": "string", "required": false}, "last_name": {"description": "<PERSON><PERSON> ng<PERSON>ời sử dụng.", "type": "string", "required": false}, "email": {"description": "Địa chỉ email cho tài nguyên.", "type": "string", "format": "email", "required": false}, "url": {"description": "Đường dẫn của người sử dụng", "type": "string", "format": "uri", "required": false}, "description": {"description": "<PERSON><PERSON> tả của ngư<PERSON>i dùng.", "type": "string", "required": false}, "locale": {"description": "<PERSON><PERSON><PERSON> ngữ cho người sử dụng", "type": "string", "enum": ["", "en_US", "vi"], "required": false}, "nickname": {"description": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON>u cho người dùng.", "type": "string", "required": false}, "slug": {"description": "Một ID bằng chữ và số cho thành viên.", "type": "string", "required": false}, "roles": {"description": "<PERSON><PERSON> (roles) của người dùng", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>u cho người sử dụng (không bao giờ bao gồm).", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "<PERSON><PERSON><PERSON> và giờ các tùy chọn đ<PERSON><PERSON><PERSON> cập nh<PERSON>.", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}}}, {"methods": ["DELETE"], "args": {"force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON><PERSON> buộ<PERSON> là true, vì người sử dụng không hỗ trợ đưa vào thùng rác.", "required": false}, "reassign": {"type": "integer", "description": "<PERSON><PERSON> lại bài viết và các liên kết của người dùng bị xóa cho người dùng với ID này.", "required": true}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/users/me"}]}}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords": {"namespace": "wp/v2", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"app_id": {"description": "UUID được cung cấp bởi các ứng dụng để xác định nó duy nhất. Bạn nên sử dụng UUID v5 với URL hoặc namespace DNS.", "type": "string", "format": "uuid", "required": false}, "name": {"description": "<PERSON><PERSON><PERSON> c<PERSON>a mật khẩu ứng dụng.", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": true}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/introspect": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/(?P<uuid>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"app_id": {"description": "UUID được cung cấp bởi các ứng dụng để xác định nó duy nhất. Bạn nên sử dụng UUID v5 với URL hoặc namespace DNS.", "type": "string", "format": "uuid", "required": false}, "name": {"description": "<PERSON><PERSON><PERSON> c<PERSON>a mật khẩu ứng dụng.", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": false}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/comments": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "after": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn những bình luận Xuất bản sau ngày tiêu chuẩn ISO8601", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Giới hạn kết quả chỉ gồm những bình luận của một số người dùng cụ thể. <PERSON><PERSON><PERSON> cầu xác thực tài k<PERSON>n.", "type": "array", "items": {"type": "integer"}, "required": false}, "author_exclude": {"description": "<PERSON><PERSON><PERSON> bảo rằng kết quả đã loại bỏ những bình luận của một số người dùng nhất định. <PERSON><PERSON><PERSON> cầu xác thực tài k<PERSON>n.", "type": "array", "items": {"type": "integer"}, "required": false}, "author_email": {"default": null, "description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả từ một tác gi<PERSON> có email cụ thể. <PERSON><PERSON><PERSON> cầu xác thực tài kho<PERSON>n.", "format": "email", "type": "string", "required": false}, "before": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn những bình luận Xu<PERSON>t bản trước ngày tiêu chuẩn ISO8601", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp danh sách bằng thuộc tính của bình luận.", "type": "string", "default": "date_gmt", "enum": ["date", "date_gmt", "id", "include", "post", "parent", "type"], "required": false}, "parent": {"default": [], "description": "<PERSON><PERSON><PERSON> bảo rằng kết quả đã loại bỏ những bình luận có IDs cha cụ thể.", "type": "array", "items": {"type": "integer"}, "required": false}, "parent_exclude": {"default": [], "description": "<PERSON><PERSON><PERSON> bảo rằng kết quả đã loại bỏ những IDs cha đ<PERSON><PERSON><PERSON> chọn.", "type": "array", "items": {"type": "integer"}, "required": false}, "post": {"default": [], "description": "Giới hạn kết quả chỉ gồm những bình luận của một số bài viết nhất định.", "type": "array", "items": {"type": "integer"}, "required": false}, "status": {"default": "approve", "description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả là các bình luận có một trạng thái cụ thể. <PERSON><PERSON><PERSON> cầu xác thực tài k<PERSON>n.", "type": "string", "required": false}, "type": {"default": "comment", "description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả là các bình luận theo một loại cụ thể. <PERSON><PERSON><PERSON> cầu xác thực tài k<PERSON>n.", "type": "string", "required": false}, "password": {"description": "<PERSON><PERSON><PERSON> khẩu của bài viết đó nếu nó được bảo vệ.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"author": {"description": "ID của đối tượng người dùng, nế<PERSON> tác gi<PERSON> là một người dùng.", "type": "integer", "required": false}, "author_email": {"description": "Địa chỉ email của tác gi<PERSON> bình luận.", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "Địa chỉ IP của tác giả bình luận.", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "<PERSON><PERSON><PERSON> thị tên của tác giả bình luận.", "type": "string", "required": false}, "author_url": {"description": "URL của tác gi<PERSON> bình luận", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "<PERSON><PERSON><PERSON><PERSON> của tác gi<PERSON> bình luận.", "type": "string", "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung cho bình luận.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung cho bình <PERSON>, như đã tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML cho bình lu<PERSON>, dùng để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> b<PERSON>nh luận đ<PERSON><PERSON><PERSON> xu<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> b<PERSON>nh luận đ<PERSON><PERSON><PERSON> công b<PERSON>, theo g<PERSON>ờ GMT.", "type": "string", "format": "date-time", "required": false}, "parent": {"default": 0, "description": "ID cha của b<PERSON>nh luận.", "type": "integer", "required": false}, "post": {"default": 0, "description": "ID của đối tượng bài viết liên quan.", "type": "integer", "required": false}, "status": {"description": "<PERSON><PERSON><PERSON><PERSON> thái của bình luận.", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/comments"}]}}, "/wp/v2/comments/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bình luận.", "type": "integer", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "<PERSON><PERSON>t khẩu cho bài viết nơi bình luận này được đăng (nếu bài viết đượ<PERSON> bảo vệ bởi mật khẩu).", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bình luận.", "type": "integer", "required": false}, "author": {"description": "ID của đối tượng người dùng, nế<PERSON> tác gi<PERSON> là một người dùng.", "type": "integer", "required": false}, "author_email": {"description": "Địa chỉ email của tác gi<PERSON> bình luận.", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "Địa chỉ IP của tác giả bình luận.", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "<PERSON><PERSON><PERSON> thị tên của tác giả bình luận.", "type": "string", "required": false}, "author_url": {"description": "URL của tác gi<PERSON> bình luận", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "<PERSON><PERSON><PERSON><PERSON> của tác gi<PERSON> bình luận.", "type": "string", "required": false}, "content": {"description": "<PERSON><PERSON><PERSON> dung cho bình luận.", "type": "object", "properties": {"raw": {"description": "<PERSON><PERSON><PERSON> dung cho bình <PERSON>, như đã tồn tại trong cơ sở dữ liệu.", "type": "string", "context": ["edit"]}, "rendered": {"description": "<PERSON><PERSON><PERSON> dung HTML cho bình lu<PERSON>, dùng để hiển thị.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "<PERSON><PERSON><PERSON> b<PERSON>nh luận đ<PERSON><PERSON><PERSON> xu<PERSON> b<PERSON>, theo múi gi<PERSON> của trang web.", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "<PERSON><PERSON><PERSON> b<PERSON>nh luận đ<PERSON><PERSON><PERSON> công b<PERSON>, theo g<PERSON>ờ GMT.", "type": "string", "format": "date-time", "required": false}, "parent": {"description": "ID cha của b<PERSON>nh luận.", "type": "integer", "required": false}, "post": {"description": "ID của đối tượng bài viết liên quan.", "type": "integer", "required": false}, "status": {"description": "<PERSON><PERSON><PERSON><PERSON> thái của bình luận.", "type": "string", "required": false}, "meta": {"description": "<PERSON><PERSON><PERSON> tr<PERSON>ng meta.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "<PERSON><PERSON><PERSON> danh duy nhất cho bình luận.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "<PERSON><PERSON> x<PERSON>a v<PERSON>nh vi<PERSON><PERSON> hay không.", "required": false}, "password": {"description": "<PERSON><PERSON>t khẩu cho bài viết nơi bình luận này được đăng (nếu bài viết đượ<PERSON> bảo vệ bởi mật khẩu).", "type": "string", "required": false}}}]}, "/wp/v2/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "type": {"default": "post", "description": "Giới hạn kết quả theo các item của một kiểu đối tượng.", "type": "string", "enum": ["post", "term", "post-format"], "required": false}, "subtype": {"default": "any", "description": "Giới hạn kết quả theo các item của một hoặc nhiều kiểu phụ của đối tượng.", "type": "array", "items": {"enum": ["post", "page", "blocks", "category", "post_tag", "any"], "type": "string"}, "required": false}, "exclude": {"description": "<PERSON><PERSON><PERSON> chắn kết quả loại trừ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả chỉ những ID cố định.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/search"}]}}, "/wp/v2/block-renderer/(?P<name>[a-z0-9-]+/[a-z0-9-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET", "POST"], "args": {"name": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>ng kí duy nhất cho khối.", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["edit"], "default": "view", "required": false}, "attributes": {"description": "<PERSON><PERSON><PERSON> th<PERSON> t<PERSON>h cho kh<PERSON>i.", "type": "object", "default": [], "required": false}, "post_id": {"description": "ID của ngữ cảnh bài đăng.", "type": "integer", "required": false}}}]}, "/wp/v2/block-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "Namespace của k<PERSON>i.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/block-types"}]}}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "Namespace của k<PERSON>i.", "type": "string", "required": false}}}]}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)/(?P<name>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"name": {"description": "<PERSON><PERSON><PERSON>.", "type": "string", "required": false}, "namespace": {"description": "Namespace của k<PERSON>i.", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/settings": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"title": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "description": "Tiêu đề trang web.", "type": "string", "required": false}, "description": {"title": "<PERSON>òng mô tả", "description": "Dòng mô tả trang web.", "type": "string", "required": false}, "url": {"title": "", "description": "Đường dẫn của trang", "type": "string", "format": "uri", "required": false}, "email": {"title": "", "description": "Đ<PERSON><PERSON> chỉ này được dùng cho các mục đích liên quan đến quản trị viên, ví dụ như thông báo có người dùng mới.", "type": "string", "format": "email", "required": false}, "timezone": {"title": "", "description": "<PERSON><PERSON><PERSON> thành phố chung múi giờ với bạn.", "type": "string", "required": false}, "date_format": {"title": "", "description": "<PERSON><PERSON><PERSON> dạng ngày cho tất cả các chuỗi ngày tháng.", "type": "string", "required": false}, "time_format": {"title": "", "description": "<PERSON><PERSON><PERSON> dạng thời gian cho tất cả các chuỗi thời gian.", "type": "string", "required": false}, "start_of_week": {"title": "", "description": "<PERSON><PERSON><PERSON> (thứ) b<PERSON>t đ<PERSON>u trong tuần.", "type": "integer", "required": false}, "language": {"title": "", "description": "Mã quốc gia WordPress.", "type": "string", "required": false}, "use_smilies": {"title": "", "description": "Chuyển biểu tượng cảm xúc như :-) và :-P thành đồ họa khi hiển thị.", "type": "boolean", "required": false}, "default_category": {"title": "", "description": "<PERSON><PERSON> mục mặc định cho bài viết.", "type": "integer", "required": false}, "default_post_format": {"title": "", "description": "<PERSON><PERSON><PERSON> định dạng mặc định.", "type": "string", "required": false}, "posts_per_page": {"title": "<PERSON><PERSON><PERSON> viết tối đa trên mỗi trang", "description": "<PERSON><PERSON><PERSON><PERSON> trang blog đ<PERSON><PERSON><PERSON> hiển thị nhiều nhất.", "type": "integer", "required": false}, "show_on_front": {"title": "<PERSON><PERSON><PERSON> thị trên trang nhất", "description": "<PERSON><PERSON><PERSON> mục hiển thị ở trang chủ", "type": "string", "required": false}, "page_on_front": {"title": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "description": "ID của trang sẽ được hiển thị trên trang chủ", "type": "integer", "required": false}, "page_for_posts": {"title": "", "description": "ID của trang nên hiển thị những bài viết mới nhất", "type": "integer", "required": false}, "default_ping_status": {"title": "", "description": "<PERSON> phép thông b<PERSON><PERSON> li<PERSON><PERSON> kết (pingback và trackback) từ các blog khác trên các bài viết mới.", "type": "string", "enum": ["open", "closed"], "required": false}, "default_comment_status": {"title": "<PERSON> phép bình luận trên bài viết mới", "description": "<PERSON> phép mọi người gửi bình luận về bài viết mới.", "type": "string", "enum": ["open", "closed"], "required": false}, "site_logo": {"title": "Logo", "description": "Logo trang web.", "type": "integer", "required": false}, "site_icon": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> của web.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/settings"}]}}, "/wp/v2/themes": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "G<PERSON><PERSON>i hạn kết quả được đặt cho giao diện được gán một hoặc nhiều trạng thái.", "type": "array", "items": {"enum": ["active", "inactive"], "type": "string"}, "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/themes"}]}}, "/wp/v2/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"stylesheet": {"description": "<PERSON><PERSON><PERSON> định kiểu của giao diện. <PERSON>ần này xác định duy nhất giao diện.", "type": "string", "required": false}}}]}, "/wp/v2/plugins": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "required": false}, "status": {"description": "Gi<PERSON>i hạn kết quả chỉ các plugin với trạng thái nhất định.", "type": "array", "items": {"type": "string", "enum": ["inactive", "active"]}, "required": false}}}, {"methods": ["POST"], "args": {"slug": {"type": "string", "description": "Đường dẫn plugin trong thư viện WordPress.org.", "pattern": "[\\w\\-]+", "required": true}, "status": {"description": "Tr<PERSON>ng thái kích ho<PERSON>t của plugin.", "type": "string", "enum": ["inactive", "active"], "default": "inactive", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/plugins"}]}}, "/wp/v2/plugins/(?P<plugin>[^.\\/]+(?:\\/[^.\\/]+)?)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}, "status": {"description": "Tr<PERSON>ng thái kích ho<PERSON>t của plugin.", "type": "string", "enum": ["inactive", "active"], "required": false}}}, {"methods": ["DELETE"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}]}, "/wp/v2/sidebars": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/sidebars"}]}}, "/wp/v2/sidebars/(?P<id>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Id c<PERSON><PERSON> cột bên đã đăng ký", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"widgets": {"description": "<PERSON><PERSON><PERSON> ti<PERSON>n ích lồng nhau.", "type": "array", "items": {"type": ["object", "string"]}, "required": false}}}]}, "/wp/v2/widget-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/widget-types"}]}}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Id c<PERSON>a kiểu tiện ích.", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/encode": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Id c<PERSON>a kiểu tiện ích.", "type": "string", "required": true}, "instance": {"description": "Tr<PERSON><PERSON> thái thiết lập hiện tại của tiện ích.", "type": "object", "required": false}, "form_data": {"description": "Dữ liệu biểu mẫu tiện ích được tuần tự hóa để mã hóa thành cài đặt phiên bản.", "type": "string", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/render": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Id c<PERSON>a kiểu tiện ích.", "type": "string", "required": true}, "instance": {"description": "Tr<PERSON><PERSON> thái thiết lập hiện tại của tiện ích.", "type": "object", "required": false}}}]}, "/wp/v2/widgets": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "sidebar": {"description": "<PERSON><PERSON><PERSON> bên để trả các tiện ích về.", "type": "string", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> định danh duy nhất cho tiện ích.", "type": "string", "required": false}, "id_base": {"description": "Loại tiện ích. Tương ứng với ID trong điểm cuối loại tiện ích.", "type": "string", "required": false}, "sidebar": {"default": "wp_inactive_widgets", "description": "<PERSON><PERSON> bên mà tiện ích thu<PERSON> về.", "type": "string", "required": true}, "instance": {"description": "Tr<PERSON>ng thái cấu hình của tiện ích, nế<PERSON> được hỗ trợ.", "type": "object", "properties": {"encoded": {"description": "Mã hóa base64 của thiết lập trạng thái.", "type": "string", "context": ["edit"]}, "hash": {"description": "<PERSON><PERSON> đư<PERSON><PERSON> mã hóa của trạng thái thiết lập.", "type": "string", "context": ["edit"]}, "raw": {"description": "Cài đặt phiên bản chưa được mã hóa, nế<PERSON> được hỗ trợ.", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "Dữ liệu biểu mẫu được mã hóa URL từ biểu mẫu quản trị tiện ích. Đư<PERSON><PERSON> sử dụng để cập nhật tiện ích không hỗ trợ trạng thái. Chỉ cho ghi.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/widgets"}]}}, "/wp/v2/widgets/(?P<id>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "<PERSON><PERSON> định danh duy nhất cho tiện ích.", "type": "string", "required": false}, "id_base": {"description": "Loại tiện ích. Tương ứng với ID trong điểm cuối loại tiện ích.", "type": "string", "required": false}, "sidebar": {"description": "<PERSON><PERSON> bên mà tiện ích thu<PERSON> về.", "type": "string", "required": false}, "instance": {"description": "Tr<PERSON>ng thái cấu hình của tiện ích, nế<PERSON> được hỗ trợ.", "type": "object", "properties": {"encoded": {"description": "Mã hóa base64 của thiết lập trạng thái.", "type": "string", "context": ["edit"]}, "hash": {"description": "<PERSON><PERSON> đư<PERSON><PERSON> mã hóa của trạng thái thiết lập.", "type": "string", "context": ["edit"]}, "raw": {"description": "Cài đặt phiên bản chưa được mã hóa, nế<PERSON> được hỗ trợ.", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "Dữ liệu biểu mẫu được mã hóa URL từ biểu mẫu quản trị tiện ích. Đư<PERSON><PERSON> sử dụng để cập nhật tiện ích không hỗ trợ trạng thái. Chỉ cho ghi.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"force": {"description": "<PERSON><PERSON><PERSON> có thể xóa tiện ích, hay chuyển nó tới danh sách tiện ích không sử dụng.", "type": "boolean", "required": false}}}]}, "/wp/v2/block-directory/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "term": {"description": "<PERSON><PERSON><PERSON>i hạn kết quả chỉ các khối theo kết quả tìm kiếm.", "type": "string", "minLength": 1, "required": true}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/block-directory/search"}]}}, "/wp/v2/pattern-directory/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một chuỗi.", "type": "string", "minLength": 1, "required": false}, "category": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong một ID danh mục.", "type": "integer", "minimum": 1, "required": false}, "keyword": {"description": "<PERSON><PERSON><PERSON><PERSON> hạn kết quả phù hợp trong ID từ khoá.", "type": "integer", "minimum": 1, "required": false}, "slug": {"description": "<PERSON>i<PERSON>i hạn kết quả đối với những kết quả phù hợp với một mẫu (slug).", "type": "array", "required": false}, "offset": {"description": "Offset kết quả được thiết lập bởi một số lượng cụ thể.", "type": "integer", "required": false}, "order": {"description": "<PERSON><PERSON><PERSON> xếp thuộc t<PERSON>h tăng dần hoặc giảm dần.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "<PERSON><PERSON><PERSON> xếp bộ sưu tập theo thu<PERSON><PERSON> t<PERSON>h của bài viết.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "favorite_count"], "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/pattern-directory/patterns"}]}}, "/wp/v2/block-patterns/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/block-patterns/patterns"}]}}, "/wp/v2/block-patterns/categories": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/block-patterns/categories"}]}}, "/wp-site-health/v1": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-site-health/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-site-health/v1"}]}}, "/wp-site-health/v1/tests/background-updates": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-site-health/v1/tests/background-updates"}]}}, "/wp-site-health/v1/tests/loopback-requests": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-site-health/v1/tests/loopback-requests"}]}}, "/wp-site-health/v1/tests/https-status": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-site-health/v1/tests/https-status"}]}}, "/wp-site-health/v1/tests/dotorg-communication": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-site-health/v1/tests/dotorg-communication"}]}}, "/wp-site-health/v1/tests/authorization-header": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-site-health/v1/tests/authorization-header"}]}}, "/wp-site-health/v1/directory-sizes": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-site-health/v1/directory-sizes"}]}}, "/wp-site-health/v1/tests/page-cache": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-site-health/v1/tests/page-cache"}]}}, "/wp-block-editor/v1": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-block-editor/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-block-editor/v1"}]}}, "/wp-block-editor/v1/url-details": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "URL xử lý.", "type": "string", "format": "uri", "required": true}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-block-editor/v1/url-details"}]}}, "/wp/v2/menu-locations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/menu-locations"}]}}, "/wp/v2/menu-locations/(?P<location>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"location": {"description": "Chỉ dùng chữ và số cho vị trí menu.", "type": "string", "required": false}, "context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp-block-editor/v1/export": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-block-editor/v1/export"}]}}, "/wp-block-editor/v1/navigation-fallback": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp-block-editor/v1/navigation-fallback"}]}}, "/wp/v2/font-collections": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "<PERSON><PERSON> hiện tại của bộ sưu tập.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Số lượng kết quả được trả về một lần.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}}}], "_links": {"self": [{"href": "https://vandamtour.vn/wp-json/wp/v2/font-collections"}]}}, "/wp/v2/font-collections/(?P<slug>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope của request; quyết định trường nào sẽ được trả về ở response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}}, "site_logo": 0, "site_icon": 140, "site_icon_url": "https://vandamtour.vn/wp-content/uploads/2025/07/cropped-z6839990068883_fe9a5555d3e1c1c08323d80b3419de95.jpg", "_links": {"help": [{"href": "https://developer.wordpress.org/rest-api/"}], "wp:featuredmedia": [{"embeddable": true, "type": "site_icon", "href": "https://vandamtour.vn/wp-json/wp/v2/media/140"}], "curies": [{"name": "wp", "href": "https://api.w.org/{rel}", "templated": true}]}}