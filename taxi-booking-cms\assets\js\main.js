/**
 * Main JavaScript - Taxi Booking CMS
 * Xử lý tương tác và logic chính của website
 */

// Global variables
let selectedServiceId = null;

/**
 * Document Ready
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize Application
 */
function initializeApp() {
    // Show loading
    showLoading();
    
    // Render all components
    setTimeout(() => {
        window.uiComponents.renderAll();
        hideLoading();
        initializeEventListeners();
        initializeAnimations();
    }, 500);
}

/**
 * Show/Hide Loading
 */
function showLoading() {
    document.getElementById('loading').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loading').classList.add('hidden');
}

/**
 * Initialize Event Listeners
 */
function initializeEventListeners() {
    // Booking form submission
    const bookingForm = document.getElementById('booking-form');
    if (bookingForm) {
        bookingForm.addEventListener('submit', handleBookingSubmit);
    }

    // Contact form submission
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactSubmit);
    }

    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', toggleMobileMenu);
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(link => {
        link.addEventListener('click', handleSmoothScroll);
    });

    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });

    // Form input focus effects
    document.querySelectorAll('input, textarea, select').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentNode.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentNode.classList.remove('focused');
        });
    });
}

/**
 * Handle Booking Form Submit
 */
function handleBookingSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const bookingData = Object.fromEntries(formData.entries());
    
    // Validation rules
    const rules = {
        customerName: {
            required: true,
            label: 'Họ và tên',
            minLength: 2
        },
        customerPhone: {
            required: true,
            label: 'Số điện thoại',
            pattern: /^[0-9]{10,11}$/
        },
        pickupLocation: {
            required: true,
            label: 'Điểm đón',
            minLength: 5
        },
        destination: {
            required: true,
            label: 'Điểm đến',
            minLength: 5
        },
        serviceType: {
            required: true,
            label: 'Loại dịch vụ'
        }
    };

    // Validate form
    const validation = window.uiComponents.validateForm(bookingData, rules);
    
    if (!validation.isValid) {
        window.uiComponents.showFormErrors(validation.errors);
        window.uiComponents.showNotification('Vui lòng kiểm tra lại thông tin', 'error');
        return;
    }

    // Clear errors
    window.uiComponents.clearFormErrors();

    // Add booking
    const success = window.dataManager.addBooking(bookingData);
    
    if (success) {
        window.uiComponents.showNotification('Đặt xe thành công! Chúng tôi sẽ liên hệ với bạn sớm nhất.', 'success');
        e.target.reset();
        
        // Auto call if phone number provided
        if (bookingData.customerPhone) {
            setTimeout(() => {
                const confirmCall = confirm('Bạn có muốn gọi ngay để xác nhận đặt xe?');
                if (confirmCall) {
                    window.open(`tel:${window.dataManager.getSettings().phone}`);
                }
            }, 2000);
        }
    } else {
        window.uiComponents.showNotification('Có lỗi xảy ra, vui lòng thử lại', 'error');
    }
}

/**
 * Handle Contact Form Submit
 */
function handleContactSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const messageData = Object.fromEntries(formData.entries());
    
    // Validation rules
    const rules = {
        name: {
            required: true,
            label: 'Họ và tên',
            minLength: 2
        },
        email: {
            required: true,
            label: 'Email',
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        },
        subject: {
            required: true,
            label: 'Tiêu đề',
            minLength: 5
        },
        message: {
            required: true,
            label: 'Nội dung',
            minLength: 10
        }
    };

    // Validate form
    const validation = window.uiComponents.validateForm(messageData, rules);
    
    if (!validation.isValid) {
        window.uiComponents.showFormErrors(validation.errors);
        window.uiComponents.showNotification('Vui lòng kiểm tra lại thông tin', 'error');
        return;
    }

    // Clear errors
    window.uiComponents.clearFormErrors();

    // Add message
    const success = window.dataManager.addMessage(messageData);
    
    if (success) {
        window.uiComponents.showNotification('Gửi tin nhắn thành công! Chúng tôi sẽ phản hồi sớm nhất.', 'success');
        e.target.reset();
    } else {
        window.uiComponents.showNotification('Có lỗi xảy ra, vui lòng thử lại', 'error');
    }
}

/**
 * Select Service
 */
function selectService(serviceId) {
    selectedServiceId = serviceId;
    
    // Update service select in booking form
    const serviceSelect = document.getElementById('service-type');
    if (serviceSelect) {
        serviceSelect.value = serviceId;
    }
    
    // Scroll to booking form
    scrollToElement('#booking');
    
    // Highlight selected service
    document.querySelectorAll('.service-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    const selectedCard = document.querySelector(`[onclick="selectService(${serviceId})"]`).closest('.service-card');
    if (selectedCard) {
        selectedCard.classList.add('selected');
    }
    
    window.uiComponents.showNotification('Đã chọn dịch vụ, vui lòng điền thông tin đặt xe', 'info');
}

/**
 * Scroll to Services
 */
function scrollToServices() {
    scrollToElement('#services');
}

/**
 * Scroll to Element
 */
function scrollToElement(selector) {
    const element = document.querySelector(selector);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

/**
 * Handle Smooth Scroll
 */
function handleSmoothScroll(e) {
    e.preventDefault();
    const href = e.target.getAttribute('href');
    if (href && href.startsWith('#')) {
        scrollToElement(href);
    }
}

/**
 * Toggle Mobile Menu
 */
function toggleMobileMenu() {
    const navMenu = document.getElementById('nav-menu');
    const toggle = document.querySelector('.mobile-menu-toggle i');
    
    navMenu.classList.toggle('active');
    
    if (navMenu.classList.contains('active')) {
        toggle.className = 'fas fa-times';
    } else {
        toggle.className = 'fas fa-bars';
    }
}

/**
 * Open Booking Modal
 */
function openBookingModal() {
    const modal = document.getElementById('booking-modal');
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Close Booking Modal
 */
function closeBookingModal() {
    const modal = document.getElementById('booking-modal');
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

/**
 * Close Modal
 */
function closeModal(modal) {
    modal.classList.remove('active');
    document.body.style.overflow = '';
}

/**
 * Submit Quick Booking
 */
function submitQuickBooking() {
    const phone = document.getElementById('quick-phone').value;
    const location = document.getElementById('quick-location').value;
    
    if (!phone || !location) {
        window.uiComponents.showNotification('Vui lòng điền đầy đủ thông tin', 'warning');
        return;
    }
    
    // Validate phone
    if (!/^[0-9]{10,11}$/.test(phone)) {
        window.uiComponents.showNotification('Số điện thoại không đúng định dạng', 'error');
        return;
    }
    
    // Create quick booking
    const quickBooking = {
        customerPhone: phone,
        pickupLocation: location,
        destination: 'Sẽ xác nhận qua điện thoại',
        serviceType: 'quick',
        notes: 'Đặt xe nhanh từ modal'
    };
    
    const success = window.dataManager.addBooking(quickBooking);
    
    if (success) {
        closeBookingModal();
        window.uiComponents.showNotification('Đặt xe thành công! Chúng tôi sẽ gọi lại ngay.', 'success');
        
        // Clear form
        document.getElementById('quick-phone').value = '';
        document.getElementById('quick-location').value = '';
        
        // Auto call
        setTimeout(() => {
            window.open(`tel:${window.dataManager.getSettings().phone}`);
        }, 1000);
    } else {
        window.uiComponents.showNotification('Có lỗi xảy ra, vui lòng thử lại', 'error');
    }
}

/**
 * Initialize Animations
 */
function initializeAnimations() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);
    
    // Observe elements
    document.querySelectorAll('.service-card, .contact-info-item, .section-header').forEach(el => {
        observer.observe(el);
    });
}
