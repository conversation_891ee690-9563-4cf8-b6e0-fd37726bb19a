
// Component Loader for VẠN DẶM TOUR
document.addEventListener('DOMContentLoaded', function () {
    console.log('Loading components...');

    // Load components with error handling
    loadComponent('header-placeholder', 'components/header.html');
    loadComponent('footer-placeholder', 'components/footer.html');

    // Load all navigation placeholders
    for (let i = 0; i < 5; i++) {
        loadComponent('nav-placeholder-' + i, 'components/navigation.html');
    }

    // Load all contact button placeholders
    for (let i = 0; i < 10; i++) {
        loadComponent('contact-placeholder-' + i, 'components/contact-buttons.html');
    }

    // Load all form placeholders
    for (let i = 0; i < 5; i++) {
        loadComponent('form-placeholder-' + i, 'components/booking-form.html');
    }

    function loadComponent(placeholderId, componentPath) {
        const placeholder = document.getElementById(placeholderId);
        if (placeholder) {
            console.log('Loading component:', placeholderId, 'from', componentPath);

            fetch(componentPath)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Component not found: ' + componentPath);
                    }
                    return response.text();
                })
                .then(html => {
                    placeholder.outerHTML = html;
                    console.log('✓ Loaded:', placeholderId);
                })
                .catch(error => {
                    console.log('✗ Failed to load component:', placeholderId, error.message);
                    // Keep placeholder visible for debugging
                    placeholder.style.display = 'block';
                    placeholder.style.padding = '10px';
                    placeholder.style.background = '#f0f0f0';
                    placeholder.style.border = '1px dashed #ccc';
                    placeholder.innerHTML = '⚠️ Component not loaded: ' + componentPath;
                });
        }
    }

    // Initialize website functionality after components load
    setTimeout(function () {
        initializeWebsite();
    }, 1000);

    function initializeWebsite() {
        console.log('Initializing website functionality...');

        // Re-initialize any JavaScript that was in the original components
        if (typeof jQuery !== 'undefined') {
            jQuery(document).ready(function ($) {
                // Form handling
                $('.wpcf7-form').on('submit', function (e) {
                    e.preventDefault();
                    alert('Form submitted! (Demo mode)');
                });

                // Mobile menu
                $('.mobile-menu-link').on('click', function (e) {
                    e.preventDefault();
                    $('.mobile-sidebar').toggleClass('active');
                });

                console.log('✓ Website functionality initialized');
            });
        }
    }
});
