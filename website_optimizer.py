#!/usr/bin/env python3
"""
Website Optimizer - Professional Website Optimization Tool
Công cụ tối ưu hóa website chuyên nghiệp cho VẠN DẶM TOUR

Author: AI Assistant
Version: 1.0.0
Date: 2025-07-30
"""

import os
import re
import json
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from bs4 import BeautifulSoup
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('website_optimizer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WebsiteOptimizer:
    """
    Professional Website Optimizer
    Tối ưu hóa website một cách chuyên nghiệp và an toàn
    """
    
    def __init__(self, source_dir: str, output_dir: str = None):
        """
        Initialize the optimizer
        
        Args:
            source_dir: <PERSON><PERSON><PERSON> ch<PERSON> website gốc
            output_dir: Th<PERSON> mục output (mặc định là source_dir)
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir) if output_dir else self.source_dir
        self.stats = {
            'original_size': 0,
            'optimized_size': 0,
            'css_files_combined': 0,
            'js_files_kept': 0,
            'inline_styles_kept': 0,
            'external_links_kept': 0
        }
        
        logger.info(f"Initializing WebsiteOptimizer")
        logger.info(f"Source: {self.source_dir}")
        logger.info(f"Output: {self.output_dir}")
    
    def analyze_html_file(self, html_file: Path) -> Dict:
        """
        Phân tích file HTML để hiểu cấu trúc
        
        Args:
            html_file: Đường dẫn đến file HTML
            
        Returns:
            Dict chứa thông tin phân tích
        """
        logger.info(f"Analyzing HTML file: {html_file}")
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        analysis = {
            'total_size': len(content),
            'css_links': [],
            'js_scripts': [],
            'inline_styles': [],
            'external_resources': []
        }
        
        # Phân tích CSS links
        for link in soup.find_all('link', rel='stylesheet'):
            href = link.get('href', '')
            if href:
                css_info = {
                    'href': href,
                    'is_external': href.startswith('http'),
                    'is_local': not href.startswith('http') and 'css/' in href,
                    'element': link
                }
                analysis['css_links'].append(css_info)
        
        # Phân tích JavaScript
        for script in soup.find_all('script', src=True):
            src = script.get('src', '')
            if src:
                js_info = {
                    'src': src,
                    'is_external': src.startswith('http'),
                    'is_local': not src.startswith('http'),
                    'element': script
                }
                analysis['js_scripts'].append(js_info)
        
        # Phân tích inline styles
        for style in soup.find_all('style'):
            if style.string:
                analysis['inline_styles'].append({
                    'id': style.get('id', 'unnamed'),
                    'content_length': len(style.string),
                    'element': style
                })
        
        logger.info(f"Analysis complete:")
        logger.info(f"  - CSS links: {len(analysis['css_links'])}")
        logger.info(f"  - JS scripts: {len(analysis['js_scripts'])}")
        logger.info(f"  - Inline styles: {len(analysis['inline_styles'])}")
        
        return analysis
    
    def combine_css_files(self) -> Optional[Path]:
        """
        Gộp tất cả file CSS local thành một file
        
        Returns:
            Path đến file CSS đã gộp
        """
        logger.info("Combining CSS files...")
        
        css_dir = self.source_dir / 'css'
        if not css_dir.exists():
            logger.warning("CSS directory not found")
            return None
        
        # Danh sách file CSS theo thứ tự ưu tiên
        priority_files = [
            'wp-content_uploads_font-awesome_v5.15.4_css_svg-with-js.css',
            'wp-content_themes_flatsome_assets_css_flatsome.css',
            'wp-content_themes_flatsome-child_style.css',
            'extracted-inline.css'
        ]
        
        combined_content = "/* VẠN DẶM TOUR - Combined CSS */\n"
        combined_content += "/* Generated by Website Optimizer v1.0.0 */\n\n"
        
        files_combined = 0
        
        # Thêm file theo thứ tự ưu tiên
        for filename in priority_files:
            css_file = css_dir / filename
            if css_file.exists():
                logger.info(f"  Adding priority file: {filename}")
                with open(css_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                combined_content += f"/* === {filename} === */\n"
                combined_content += content + "\n\n"
                files_combined += 1
        
        # Thêm các file còn lại
        for css_file in css_dir.glob('*.css'):
            if css_file.name not in priority_files and css_file.name not in ['combined.css', 'combined.min.css']:
                logger.info(f"  Adding additional file: {css_file.name}")
                with open(css_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                combined_content += f"/* === {css_file.name} === */\n"
                combined_content += content + "\n\n"
                files_combined += 1
        
        # Thêm CSS tối ưu hóa
        combined_content += self._get_optimization_css()
        
        # Lưu file combined
        combined_file = css_dir / 'combined.css'
        with open(combined_file, 'w', encoding='utf-8') as f:
            f.write(combined_content)
        
        # Tạo file minified
        minified_content = self._minify_css(combined_content)
        minified_file = css_dir / 'combined.min.css'
        with open(minified_file, 'w', encoding='utf-8') as f:
            f.write(minified_content)
        
        self.stats['css_files_combined'] = files_combined
        
        logger.info(f"CSS combination complete:")
        logger.info(f"  - Files combined: {files_combined}")
        logger.info(f"  - Combined file: {combined_file}")
        logger.info(f"  - Minified file: {minified_file}")
        logger.info(f"  - Original size: {len(combined_content):,} bytes")
        logger.info(f"  - Minified size: {len(minified_content):,} bytes")
        
        return minified_file
    
    def _get_optimization_css(self) -> str:
        """Trả về CSS tối ưu hóa"""
        return """
/* === OPTIMIZATION CSS === */

/* Performance optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
    loading: lazy;
}

html {
    scroll-behavior: smooth;
}

/* Loading animations */
.loading {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

/* Button improvements */
.button, .btn {
    transition: all 0.3s ease;
    cursor: pointer;
}

.button:hover, .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .hide-for-mobile {
        display: none !important;
    }
}

/* Form improvements */
.wpcf7-form input,
.wpcf7-form textarea,
.wpcf7-form select {
    border-radius: 4px;
    border: 1px solid #ddd;
    padding: 10px;
    transition: border-color 0.3s ease;
}

.wpcf7-form input:focus,
.wpcf7-form textarea:focus,
.wpcf7-form select:focus {
    border-color: #007cba;
    outline: none;
    box-shadow: 0 0 5px rgba(0,124,186,0.3);
}

/* Contact buttons improvements */
.button-contact {
    z-index: 9999;
    position: fixed;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
}
"""
    
    def _minify_css(self, css_content: str) -> str:
        """
        Minify CSS content
        
        Args:
            css_content: CSS content to minify
            
        Returns:
            Minified CSS content
        """
        # Remove comments
        css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
        
        # Remove extra whitespace
        css_content = re.sub(r'\s+', ' ', css_content)
        
        # Remove spaces around certain characters
        css_content = re.sub(r'\s*([{}:;,>+~])\s*', r'\1', css_content)
        
        # Remove trailing semicolons before }
        css_content = re.sub(r';\s*}', '}', css_content)
        
        return css_content.strip()
    
    def optimize_html(self, html_file: Path) -> Path:
        """
        Tối ưu hóa file HTML
        
        Args:
            html_file: File HTML cần tối ưu
            
        Returns:
            Path đến file HTML đã tối ưu
        """
        logger.info(f"Optimizing HTML file: {html_file}")
        
        # Phân tích file HTML
        analysis = self.analyze_html_file(html_file)
        self.stats['original_size'] = analysis['total_size']
        
        # Đọc nội dung HTML
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # 1. Gộp CSS files
        combined_css = self.combine_css_files()
        
        # 2. Thay thế local CSS links bằng combined CSS
        local_css_removed = 0
        for css_info in analysis['css_links']:
            if css_info['is_local']:
                css_info['element'].decompose()
                local_css_removed += 1
        
        # 3. Thêm combined CSS link
        head = soup.find('head')
        if head and combined_css:
            css_link = soup.new_tag('link', rel='stylesheet', href='css/combined.min.css', type='text/css')
            # Thêm sau external CSS links
            external_links = [link for link in soup.find_all('link', rel='stylesheet') 
                            if link.get('href', '').startswith('http')]
            if external_links:
                external_links[-1].insert_after(css_link)
            else:
                head.insert(0, css_link)
        
        # 4. Giữ nguyên inline styles và JavaScript
        self.stats['inline_styles_kept'] = len(analysis['inline_styles'])
        self.stats['js_files_kept'] = len(analysis['js_scripts'])
        self.stats['external_links_kept'] = len([css for css in analysis['css_links'] if css['is_external']])
        
        # 5. Thêm meta tags tối ưu hóa
        if head:
            meta_tags = [
                ('name', 'theme-color', 'content', '#ffffff'),
                ('name', 'apple-mobile-web-app-capable', 'content', 'yes'),
                ('name', 'apple-mobile-web-app-status-bar-style', 'content', 'default')
            ]
            
            for attr1, val1, attr2, val2 in meta_tags:
                if not head.find('meta', attrs={attr1: val1}):
                    meta_tag = soup.new_tag('meta')
                    meta_tag[attr1] = val1
                    meta_tag[attr2] = val2
                    head.append(meta_tag)
        
        # 6. Lưu file tối ưu
        output_file = self.output_dir / 'index-optimized.html'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(str(soup))
        
        self.stats['optimized_size'] = os.path.getsize(output_file)
        
        logger.info(f"HTML optimization complete:")
        logger.info(f"  - Local CSS removed: {local_css_removed}")
        logger.info(f"  - Inline styles kept: {self.stats['inline_styles_kept']}")
        logger.info(f"  - JS files kept: {self.stats['js_files_kept']}")
        logger.info(f"  - External CSS kept: {self.stats['external_links_kept']}")
        logger.info(f"  - Output file: {output_file}")
        
        return output_file
    
    def generate_report(self) -> Dict:
        """
        Tạo báo cáo tối ưu hóa
        
        Returns:
            Dict chứa báo cáo chi tiết
        """
        savings = self.stats['original_size'] - self.stats['optimized_size']
        savings_percent = (savings / self.stats['original_size'] * 100) if self.stats['original_size'] > 0 else 0
        
        report = {
            'optimization_summary': {
                'original_size': self.stats['original_size'],
                'optimized_size': self.stats['optimized_size'],
                'savings_bytes': savings,
                'savings_percent': round(savings_percent, 2)
            },
            'css_optimization': {
                'files_combined': self.stats['css_files_combined'],
                'inline_styles_kept': self.stats['inline_styles_kept'],
                'external_links_kept': self.stats['external_links_kept']
            },
            'javascript': {
                'files_kept': self.stats['js_files_kept']
            },
            'recommendations': []
        }
        
        # Thêm khuyến nghị
        if savings_percent > 15:
            report['recommendations'].append("Excellent optimization achieved!")
        elif savings_percent > 5:
            report['recommendations'].append("Good optimization results")
        else:
            report['recommendations'].append("Consider additional optimization techniques")
        
        return report
    
    def run_optimization(self) -> bool:
        """
        Chạy toàn bộ quá trình tối ưu hóa
        
        Returns:
            True nếu thành công, False nếu thất bại
        """
        try:
            logger.info("Starting website optimization process...")
            
            # Tìm file HTML chính
            html_file = self.source_dir / 'index.html'
            if not html_file.exists():
                logger.error(f"HTML file not found: {html_file}")
                return False
            
            # Tối ưu hóa HTML
            optimized_file = self.optimize_html(html_file)
            
            # Tạo báo cáo
            report = self.generate_report()
            
            # Lưu báo cáo
            report_file = self.output_dir / 'optimization_report.json'
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            # In kết quả
            self._print_results(report, optimized_file)
            
            logger.info("Website optimization completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Optimization failed: {e}")
            return False
    
    def _print_results(self, report: Dict, optimized_file: Path):
        """In kết quả tối ưu hóa"""
        print("\n" + "="*60)
        print("🎉 WEBSITE OPTIMIZATION COMPLETED!")
        print("="*60)
        
        summary = report['optimization_summary']
        print(f"📊 OPTIMIZATION RESULTS:")
        print(f"   Original size: {summary['original_size']:,} bytes")
        print(f"   Optimized size: {summary['optimized_size']:,} bytes")
        print(f"   Savings: {summary['savings_bytes']:,} bytes ({summary['savings_percent']:.1f}%)")
        
        css = report['css_optimization']
        print(f"\n🎨 CSS OPTIMIZATION:")
        print(f"   Files combined: {css['files_combined']}")
        print(f"   Inline styles kept: {css['inline_styles_kept']}")
        print(f"   External links kept: {css['external_links_kept']}")
        
        js = report['javascript']
        print(f"\n⚡ JAVASCRIPT:")
        print(f"   Files kept: {js['files_kept']}")
        
        print(f"\n📁 OUTPUT:")
        print(f"   Optimized file: {optimized_file}")
        print(f"   Report file: {self.output_dir / 'optimization_report.json'}")
        
        print(f"\n🌐 USAGE:")
        print(f"   Open: http://localhost/duan/vandamtour/index-optimized.html")
        
        print("\n" + "="*60)

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python website_optimizer.py <website_directory>")
        print("Example: python website_optimizer.py vandamtour")
        sys.exit(1)
    
    website_dir = sys.argv[1]
    
    if not os.path.exists(website_dir):
        print(f"Directory not found: {website_dir}")
        sys.exit(1)
    
    # Tạo optimizer và chạy
    optimizer = WebsiteOptimizer(website_dir)
    success = optimizer.run_optimization()
    
    if success:
        print("\n✅ Optimization successful!")
    else:
        print("\n❌ Optimization failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
