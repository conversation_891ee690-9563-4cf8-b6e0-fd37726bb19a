#!/usr/bin/env python3
"""
Create Correct Optimized Website
T<PERSON>o phiên bản tối ưu đúng cách - chỉ gộp CSS external, giữ nguyên inline CSS
"""

import os
import re
from pathlib import Path
from bs4 import BeautifulSoup

def create_correct_optimized_website(source_html, output_html):
    """Create correctly optimized website"""
    print(f"Creating correctly optimized website...")
    print(f"Source: {source_html}")
    print(f"Output: {output_html}")
    
    # Read source HTML
    with open(source_html, 'r', encoding='utf-8') as f:
        content = f.read()
    
    soup = BeautifulSoup(content, 'html.parser')
    
    # 1. Replace only LOCAL CSS links with combined CSS
    # Keep external CSS links (FontAwesome, etc.)
    css_links = soup.find_all('link', rel='stylesheet')
    local_css_removed = 0
    
    for link in css_links:
        href = link.get('href', '')
        # Only remove local CSS files (not external ones)
        if href and not href.startswith('http') and 'css/' in href:
            link.decompose()
            local_css_removed += 1
    
    print(f"✓ Removed {local_css_removed} local CSS links")
    
    # 2. Keep ALL inline style tags (they are important for layout)
    style_tags = soup.find_all('style')
    print(f"✓ Keeping {len(style_tags)} inline style tags")
    
    # 3. Add single combined CSS link for local styles
    head = soup.find('head')
    if head:
        # Insert combined CSS after the last external CSS link
        external_links = head.find_all('link', rel='stylesheet')
        if external_links:
            # Insert after last external link
            last_external = external_links[-1]
            css_link = soup.new_tag('link', rel='stylesheet', href='css/combined.min.css', type='text/css')
            last_external.insert_after(css_link)
        else:
            # Insert at beginning of head
            css_link = soup.new_tag('link', rel='stylesheet', href='css/combined.min.css', type='text/css')
            head.insert(0, css_link)
        
        print("✓ Added combined CSS link")
    
    # 4. Keep ALL JavaScript files (they might be needed)
    script_tags = soup.find_all('script', src=True)
    print(f"✓ Keeping {len(script_tags)} JavaScript files")
    
    # 5. Add minimal performance optimizations (without breaking existing styles)
    performance_meta = """
<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">
<meta name="theme-color" content="#ffffff">
"""
    
    if head:
        # Only add if not already present
        if not head.find('meta', attrs={'name': 'theme-color'}):
            head.append(BeautifulSoup(performance_meta, 'html.parser'))
            print("✓ Added performance meta tags")
    
    # 6. Save optimized HTML
    with open(output_html, 'w', encoding='utf-8') as f:
        f.write(str(soup))
    
    print(f"✓ Correctly optimized website created: {output_html}")
    
    # Show file sizes
    original_size = os.path.getsize(source_html)
    optimized_size = os.path.getsize(output_html)
    savings = original_size - optimized_size
    
    print(f"\nFile Size Comparison:")
    print(f"Original: {original_size:,} bytes")
    print(f"Optimized: {optimized_size:,} bytes")
    if savings > 0:
        print(f"Savings: {savings:,} bytes ({savings/original_size*100:.1f}%)")
    else:
        print(f"Size increase: {abs(savings):,} bytes (due to keeping all styles)")
    
    return output_html

def main():
    website_dir = Path("vandamtour")
    source_html = website_dir / "index.html"
    output_html = website_dir / "index-correct.html"
    
    if not source_html.exists():
        print(f"Source file not found: {source_html}")
        return
    
    try:
        # Create correctly optimized website
        result = create_correct_optimized_website(source_html, output_html)
        
        print(f"\n✅ Success!")
        print(f"📁 Correctly optimized website: {result}")
        print(f"🌐 Open: http://localhost/duan/vandamtour/index-correct.html")
        
        print(f"\n📋 What was done:")
        print(f"   ✓ Replaced local CSS files with combined.min.css")
        print(f"   ✓ Kept all inline styles (important for layout)")
        print(f"   ✓ Kept all external CSS links (FontAwesome, etc.)")
        print(f"   ✓ Kept all JavaScript files")
        print(f"   ✓ Added minimal performance optimizations")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
