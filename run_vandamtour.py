#!/usr/bin/env python3
"""
Run crawler specifically for vandamtour.vn
"""

from web_crawler import AdvancedWebCrawler

def main():
    url = "vandamtour.vn"
    output_dir = "vandamtour_crawled"
    
    print(f"🕷️ Crawling VanDam Tour website: {url}")
    print("=" * 60)
    
    crawler = AdvancedWebCrawler(url, output_dir)
    crawler.crawl_website()
    
    print(f"\n✅ Crawl completed! Check '{output_dir}' folder for results.")
    print("\n📋 Analysis files created:")
    print("  - analysis/html_structure.json")
    print("  - analysis/css_analysis.json") 
    print("  - analysis/js_analysis.json")
    print("  - analysis/reconstruction_guide.md")

if __name__ == "__main__":
    main()
