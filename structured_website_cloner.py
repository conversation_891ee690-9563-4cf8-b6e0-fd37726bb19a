#!/usr/bin/env python3
"""
Structured Website Cloner
Tạo website với cấu trúc code chuyên nghiệp và rõ ràng

Cấu trúc output:
- HTML files: Clean và semantic
- CSS files: Organized và modular  
- JS files: Structured và maintainable
- Images: Optimized và organized
- Fonts: Properly structured

Author: AI Assistant
Version: 4.0.0
Date: 2025-07-30
"""

import os
import sys
import time
import json
import re
from pathlib import Path
from typing import Dict, List, Set, Optional
import logging

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, unquote

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('structured_cloner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StructuredWebsiteCloner:
    """
    Structured Website Cloner
    Tạo website với cấu trúc code chuyên nghiệp
    """
    
    def __init__(self, url: str, output_dir: str = "vandamtour_structured"):
        self.url = url
        self.domain = urlparse(url).netloc
        self.output_dir = Path(output_dir)
        
        # Statistics
        self.stats = {
            'html_files': 0,
            'css_files': 0,
            'js_files': 0,
            'image_files': 0,
            'font_files': 0,
            'total_size': 0
        }
        
        # Session for downloads
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        logger.info(f"Structured Website Cloner initialized")
        logger.info(f"Target: {self.url}")
        logger.info(f"Output: {self.output_dir.absolute()}")
    
    def create_directory_structure(self):
        """Tạo cấu trúc thư mục chuyên nghiệp"""
        directories = [
            self.output_dir,
            self.output_dir / 'assets',
            self.output_dir / 'assets' / 'css',
            self.output_dir / 'assets' / 'js',
            self.output_dir / 'assets' / 'images',
            self.output_dir / 'assets' / 'fonts',
            self.output_dir / 'assets' / 'icons',
            self.output_dir / 'components',
            self.output_dir / 'pages',
            self.output_dir / 'docs'
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        logger.info("✓ Created professional directory structure")
    
    def download_and_organize_css(self, soup: BeautifulSoup) -> List[str]:
        """Download và tổ chức CSS files"""
        logger.info("Downloading and organizing CSS files...")
        
        css_files = []
        css_links = soup.find_all('link', rel='stylesheet')
        
        for i, link in enumerate(css_links):
            href = link.get('href')
            if not href:
                continue
            
            css_url = urljoin(self.url, href)
            
            try:
                response = self.session.get(css_url, timeout=30)
                response.raise_for_status()
                
                # Determine filename
                if 'fontawesome' in href.lower():
                    filename = 'fontawesome.css'
                elif 'flatsome' in href.lower():
                    filename = 'theme-flatsome.css'
                elif 'contact-form' in href.lower():
                    filename = 'contact-form.css'
                elif 'button-contact' in href.lower():
                    filename = 'button-contact.css'
                else:
                    filename = f'style-{i+1}.css'
                
                # Save CSS file
                css_path = self.output_dir / 'assets' / 'css' / filename
                with open(css_path, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                css_files.append(f'assets/css/{filename}')
                self.stats['css_files'] += 1
                self.stats['total_size'] += len(response.text)
                
                logger.info(f"✓ Downloaded CSS: {filename}")
                
                # Update link in HTML
                link['href'] = f'assets/css/{filename}'
                
            except Exception as e:
                logger.warning(f"✗ Failed to download CSS {css_url}: {e}")
        
        return css_files
    
    def download_and_organize_js(self, soup: BeautifulSoup) -> List[str]:
        """Download và tổ chức JavaScript files"""
        logger.info("Downloading and organizing JavaScript files...")
        
        js_files = []
        script_tags = soup.find_all('script', src=True)
        
        for i, script in enumerate(script_tags):
            src = script.get('src')
            if not src:
                continue
            
            js_url = urljoin(self.url, src)
            
            try:
                response = self.session.get(js_url, timeout=30)
                response.raise_for_status()
                
                # Determine filename
                if 'jquery.min.js' in src:
                    filename = 'jquery.min.js'
                elif 'jquery-migrate' in src:
                    filename = 'jquery-migrate.min.js'
                elif 'flatsome' in src.lower():
                    filename = 'theme-flatsome.js'
                elif 'contact-form' in src.lower():
                    filename = 'contact-form.js'
                elif 'hooks' in src:
                    filename = 'wp-hooks.js'
                elif 'i18n' in src:
                    filename = 'wp-i18n.js'
                else:
                    filename = f'script-{i+1}.js'
                
                # Save JS file
                js_path = self.output_dir / 'assets' / 'js' / filename
                with open(js_path, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                
                js_files.append(f'assets/js/{filename}')
                self.stats['js_files'] += 1
                self.stats['total_size'] += len(response.text)
                
                logger.info(f"✓ Downloaded JS: {filename}")
                
                # Update script in HTML
                script['src'] = f'assets/js/{filename}'
                
            except Exception as e:
                logger.warning(f"✗ Failed to download JS {js_url}: {e}")
        
        return js_files
    
    def download_and_organize_images(self, soup: BeautifulSoup) -> List[str]:
        """Download và tổ chức images"""
        logger.info("Downloading and organizing images...")
        
        image_files = []
        img_tags = soup.find_all('img', src=True)
        
        for i, img in enumerate(img_tags):
            src = img.get('src')
            if not src or src.startswith('data:'):
                continue
            
            img_url = urljoin(self.url, src)
            
            try:
                response = self.session.get(img_url, timeout=30)
                response.raise_for_status()
                
                # Determine filename and category
                original_name = Path(urlparse(img_url).path).name
                if not original_name:
                    original_name = f'image-{i+1}.jpg'
                
                # Categorize images
                if 'taxi' in original_name.lower():
                    category = 'services'
                elif 'slider' in original_name.lower():
                    category = 'banners'
                elif any(x in original_name.lower() for x in ['phone', 'zalo', 'contact']):
                    category = 'icons'
                elif 'logo' in original_name.lower():
                    category = 'branding'
                else:
                    category = 'gallery'
                
                # Create category directory
                category_dir = self.output_dir / 'assets' / 'images' / category
                category_dir.mkdir(exist_ok=True)
                
                # Save image
                img_path = category_dir / original_name
                with open(img_path, 'wb') as f:
                    f.write(response.content)
                
                relative_path = f'assets/images/{category}/{original_name}'
                image_files.append(relative_path)
                self.stats['image_files'] += 1
                self.stats['total_size'] += len(response.content)
                
                logger.info(f"✓ Downloaded image: {category}/{original_name}")
                
                # Update img in HTML
                img['src'] = relative_path
                
            except Exception as e:
                logger.warning(f"✗ Failed to download image {img_url}: {e}")
        
        return image_files
    
    def create_clean_html(self, soup: BeautifulSoup) -> str:
        """Tạo HTML clean và semantic"""
        logger.info("Creating clean and semantic HTML...")
        
        # Remove unnecessary attributes
        for tag in soup.find_all():
            # Remove WordPress specific attributes
            if tag.has_attr('data-elementor-type'):
                del tag['data-elementor-type']
            if tag.has_attr('data-elementor-id'):
                del tag['data-elementor-id']
            if tag.has_attr('data-elementor-settings'):
                del tag['data-elementor-settings']
        
        # Clean up inline styles (keep only essential ones)
        for tag in soup.find_all(style=True):
            style = tag['style']
            # Keep only essential styles
            essential_styles = []
            for style_rule in style.split(';'):
                if any(prop in style_rule.lower() for prop in ['width', 'height', 'display', 'position']):
                    essential_styles.append(style_rule.strip())
            
            if essential_styles:
                tag['style'] = '; '.join(essential_styles)
            else:
                del tag['style']
        
        # Add semantic structure
        html_content = str(soup)
        
        # Add proper DOCTYPE and meta tags
        html_content = re.sub(
            r'<!DOCTYPE[^>]*>',
            '<!DOCTYPE html>',
            html_content
        )
        
        return html_content
    
    def create_documentation(self, css_files: List[str], js_files: List[str], image_files: List[str]):
        """Tạo documentation cho project"""
        logger.info("Creating project documentation...")
        
        readme_content = f"""# VẠN DẶM TOUR - Structured Website

## 📁 Project Structure

```
vandamtour_structured/
├── index.html                 # Main HTML file
├── assets/                    # All assets organized
│   ├── css/                   # Stylesheets
│   │   ├── fontawesome.css    # FontAwesome icons
│   │   ├── theme-flatsome.css # Main theme styles
│   │   ├── contact-form.css   # Contact form styles
│   │   └── button-contact.css # Contact button styles
│   ├── js/                    # JavaScript files
│   │   ├── jquery.min.js      # jQuery library
│   │   ├── jquery-migrate.min.js # jQuery migrate
│   │   ├── theme-flatsome.js  # Theme functionality
│   │   └── contact-form.js    # Form handling
│   ├── images/                # Images organized by category
│   │   ├── services/          # Service related images
│   │   ├── banners/           # Banner/slider images
│   │   ├── icons/             # Icon images
│   │   ├── branding/          # Logo and branding
│   │   └── gallery/           # Other images
│   └── fonts/                 # Font files
├── components/                # Reusable components (future)
├── pages/                     # Additional pages (future)
└── docs/                      # Documentation
    ├── README.md              # This file
    └── structure.json         # Project structure data
```

## 🎯 Features

- ✅ **Clean HTML structure** - Semantic and maintainable
- ✅ **Organized CSS** - Modular and categorized
- ✅ **Structured JavaScript** - Clean and documented
- ✅ **Categorized images** - Organized by purpose
- ✅ **Professional structure** - Industry standard layout
- ✅ **Documentation** - Complete project docs

## 📊 Statistics

- **HTML files**: {self.stats['html_files']}
- **CSS files**: {self.stats['css_files']}
- **JavaScript files**: {self.stats['js_files']}
- **Image files**: {self.stats['image_files']}
- **Total size**: {self.stats['total_size']:,} bytes

## 🚀 Usage

1. Open `index.html` in a web browser
2. All assets are properly linked with relative paths
3. Website works offline with all functionality

## 🛠️ Development

### CSS Files
{chr(10).join(f'- {css}' for css in css_files)}

### JavaScript Files  
{chr(10).join(f'- {js}' for js in js_files)}

### Image Categories
- **Services**: Taxi service images
- **Banners**: Slider and banner images  
- **Icons**: Contact and UI icons
- **Branding**: Logo and brand assets
- **Gallery**: Other images

## 📝 Notes

- All external dependencies have been downloaded locally
- CSS is organized by functionality
- JavaScript maintains original structure
- Images are categorized for easy management
- HTML is clean and semantic

---

**Source**: {self.url}  
**Generated**: {time.strftime('%Y-%m-%d %H:%M:%S')}  
**Tool**: Structured Website Cloner v4.0.0
"""
        
        # Save README
        readme_path = self.output_dir / 'docs' / 'README.md'
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        # Save structure data
        structure_data = {
            'project_name': 'VẠN DẶM TOUR',
            'source_url': self.url,
            'generated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
            'statistics': self.stats,
            'assets': {
                'css_files': css_files,
                'js_files': js_files,
                'image_files': image_files
            }
        }
        
        structure_path = self.output_dir / 'docs' / 'structure.json'
        with open(structure_path, 'w', encoding='utf-8') as f:
            json.dump(structure_data, f, indent=2, ensure_ascii=False)
        
        logger.info("✓ Created project documentation")
    
    def clone_structured_website(self) -> bool:
        """Main method để clone website với cấu trúc chuyên nghiệp"""
        logger.info("Starting structured website cloning...")
        start_time = time.time()
        
        try:
            # Create directory structure
            self.create_directory_structure()
            
            # Download main HTML
            logger.info("Downloading main HTML page...")
            response = self.session.get(self.url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Download and organize assets
            css_files = self.download_and_organize_css(soup)
            js_files = self.download_and_organize_js(soup)
            image_files = self.download_and_organize_images(soup)
            
            # Create clean HTML
            clean_html = self.create_clean_html(soup)
            
            # Save main HTML file
            html_path = self.output_dir / 'index.html'
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(clean_html)
            
            self.stats['html_files'] = 1
            self.stats['total_size'] += len(clean_html)
            
            # Create documentation
            self.create_documentation(css_files, js_files, image_files)
            
            # Summary
            end_time = time.time()
            duration = end_time - start_time
            
            self.print_summary(duration)
            
            return True
            
        except Exception as e:
            logger.error(f"Structured cloning failed: {e}")
            return False
    
    def print_summary(self, duration: float):
        """In tóm tắt kết quả"""
        print("\n" + "="*60)
        print("🎉 STRUCTURED WEBSITE CLONING COMPLETED!")
        print("="*60)
        
        print(f"🎯 Target URL: {self.url}")
        print(f"📁 Output directory: {self.output_dir.absolute()}")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        
        print(f"\n📊 STATISTICS:")
        print(f"   HTML files: {self.stats['html_files']}")
        print(f"   CSS files: {self.stats['css_files']}")
        print(f"   JavaScript files: {self.stats['js_files']}")
        print(f"   Image files: {self.stats['image_files']}")
        print(f"   Total size: {self.stats['total_size']:,} bytes")
        
        print(f"\n📁 STRUCTURE:")
        print(f"   ├── index.html (main page)")
        print(f"   ├── assets/css/ ({self.stats['css_files']} files)")
        print(f"   ├── assets/js/ ({self.stats['js_files']} files)")
        print(f"   ├── assets/images/ ({self.stats['image_files']} files)")
        print(f"   └── docs/ (documentation)")
        
        print(f"\n🌐 USAGE:")
        print(f"   Open: {self.output_dir / 'index.html'}")
        print(f"   Docs: {self.output_dir / 'docs' / 'README.md'}")
        
        print("\n" + "="*60)

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python structured_website_cloner.py <URL>")
        print("Example: python structured_website_cloner.py https://vandamtour.vn")
        sys.exit(1)
    
    url = sys.argv[1]
    
    # Create cloner and run
    cloner = StructuredWebsiteCloner(url)
    success = cloner.clone_structured_website()
    
    if success:
        print("\n🎉 Structured cloning successful!")
    else:
        print("\n❌ Structured cloning failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
