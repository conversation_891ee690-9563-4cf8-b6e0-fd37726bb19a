/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }
    
    .hero-text h2 {
        font-size: 4rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .contact-content {
        grid-template-columns: 1fr 1fr;
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .hero-text h2 {
        font-size: 3.5rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
    .container {
        max-width: 720px;
        padding: 0 var(--spacing-lg);
    }
    
    /* Header */
    .top-bar-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .contact-info {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .header-content {
        flex-wrap: wrap;
        gap: var(--spacing-lg);
    }
    
    .main-nav {
        order: 3;
        width: 100%;
    }
    
    .nav-menu {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-lg);
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    /* Hero */
    .hero {
        height: 60vh;
        min-height: 400px;
    }
    
    .hero-text h2 {
        font-size: 3rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    /* Services */
    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    /* Contact */
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    /* Forms */
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    /* Floating contacts */
    .floating-contacts {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
    }
    
    .floating-btn {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
    .container {
        max-width: 540px;
        padding: 0 var(--spacing-md);
    }
    
    /* Typography */
    .hero-text h2 {
        font-size: 2.5rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    /* Header */
    .top-bar {
        display: none;
    }
    
    .main-header {
        padding: var(--spacing-md) 0;
    }
    
    .logo h1 {
        font-size: var(--font-size-xl);
    }
    
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        box-shadow: var(--shadow-medium);
        flex-direction: column;
        padding: var(--spacing-lg);
        z-index: 1000;
    }
    
    .nav-menu.active {
        display: flex;
    }
    
    .nav-menu a {
        padding: var(--spacing-md) 0;
        border-bottom: 1px solid var(--border-color);
    }
    
    .nav-menu a:last-child {
        border-bottom: none;
    }
    
    .header-actions {
        gap: var(--spacing-sm);
    }
    
    .header-actions .btn {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    /* Hero */
    .hero {
        height: 50vh;
        min-height: 350px;
    }
    
    .hero-text {
        padding: 0 var(--spacing-md);
    }
    
    .hero-text p {
        font-size: var(--font-size-lg);
    }
    
    /* Sections */
    .services,
    .booking,
    .contact {
        padding: var(--spacing-2xl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    /* Service cards */
    .service-card {
        padding: var(--spacing-lg);
    }
    
    .service-card-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }
    
    /* Booking form */
    .booking-form-container {
        padding: var(--spacing-lg);
    }
    
    /* Contact */
    .contact-info-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .contact-form {
        padding: var(--spacing-lg);
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-2xl) 0 var(--spacing-lg);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    /* Modal */
    .modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }
    
    .modal-header,
    .modal-body {
        padding: var(--spacing-md);
    }
    
    /* Notification */
    .notification {
        top: var(--spacing-md);
        right: var(--spacing-md);
        left: var(--spacing-md);
    }
    
    .notification-content {
        min-width: auto;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    /* Typography */
    :root {
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.5rem;
        --font-size-2xl: 1.25rem;
    }
    
    .hero-text h2 {
        font-size: 2rem;
    }
    
    .section-header h2 {
        font-size: 1.75rem;
    }
    
    /* Header */
    .logo {
        gap: var(--spacing-sm);
    }
    
    .logo img {
        height: 40px;
    }
    
    .logo h1 {
        font-size: var(--font-size-lg);
    }
    
    .header-actions .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .header-actions .btn span {
        display: none;
    }
    
    /* Hero */
    .hero {
        height: 40vh;
        min-height: 300px;
    }
    
    .hero-text h2 {
        margin-bottom: var(--spacing-sm);
    }
    
    .hero-text p {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-lg);
    }
    
    .hero-actions {
        gap: var(--spacing-md);
    }
    
    .hero-actions .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
    
    /* Sections */
    .services,
    .booking,
    .contact {
        padding: var(--spacing-xl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-xl);
    }
    
    .section-header p {
        font-size: var(--font-size-base);
    }
    
    /* Service cards */
    .service-card {
        padding: var(--spacing-md);
    }
    
    .service-card h3 {
        font-size: var(--font-size-lg);
    }
    
    .service-card-actions {
        flex-direction: column;
    }
    
    /* Forms */
    .form-group {
        margin-bottom: var(--spacing-md);
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: var(--spacing-sm);
    }
    
    .booking-form-container,
    .contact-form {
        padding: var(--spacing-md);
    }
    
    /* Buttons */
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .btn-lg {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }
    
    /* Contact info */
    .contact-info-item {
        padding: var(--spacing-md);
    }
    
    .contact-info-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    /* Footer */
    .footer-section h3 {
        font-size: var(--font-size-lg);
    }
    
    /* Floating contacts */
    .floating-contacts {
        bottom: var(--spacing-md);
        right: var(--spacing-md);
    }
    
    .floating-btn {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-base);
    }
    
    /* Stats */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .stat-item {
        padding: var(--spacing-md);
    }
    
    .stat-number {
        font-size: var(--font-size-2xl);
    }
    
    .stat-label {
        font-size: var(--font-size-base);
    }
    
    /* Testimonials */
    .testimonial-card {
        padding: var(--spacing-md);
    }
    
    .testimonial-content {
        padding-left: var(--spacing-lg);
    }
    
    /* Pricing */
    .pricing-card {
        padding: var(--spacing-lg);
    }
    
    .pricing-price {
        font-size: var(--font-size-2xl);
    }
}

/* Mobile Extra Small (up to 375px) */
@media (max-width: 375px) {
    .container {
        padding: 0 var(--spacing-xs);
    }
    
    /* Hero */
    .hero-text h2 {
        font-size: 1.75rem;
    }
    
    /* Sections */
    .section-header h2 {
        font-size: 1.5rem;
    }
    
    /* Forms */
    .hero-actions,
    .service-card-actions {
        gap: var(--spacing-sm);
    }
    
    /* Stats */
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    /* Modal */
    .modal-content {
        width: 98%;
        margin: var(--spacing-xs);
    }
    
    /* Notification */
    .notification {
        top: var(--spacing-xs);
        right: var(--spacing-xs);
        left: var(--spacing-xs);
    }
}

/* Print Styles */
@media print {
    .header,
    .floating-contacts,
    .modal,
    .notification,
    .mobile-menu-toggle {
        display: none !important;
    }
    
    .hero {
        height: auto;
        min-height: auto;
        padding: var(--spacing-xl) 0;
    }
    
    .hero-content {
        position: static;
    }
    
    .hero-slider {
        display: none;
    }
    
    .services,
    .booking,
    .contact {
        padding: var(--spacing-lg) 0;
    }
    
    .service-card,
    .contact-info-item {
        break-inside: avoid;
    }
    
    .btn {
        display: none;
    }
    
    .footer {
        margin-top: var(--spacing-xl);
        border-top: 2px solid var(--border-color);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
    
    .service-card-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .hero-slide {
        transition: none;
    }
    
    .floating-btn {
        animation: none;
    }
    
    .progress-fill::after {
        animation: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --border-color: #404040;
    }
    
    .service-card,
    .contact-info-item,
    .booking-form-container,
    .contact-form,
    .modal-content,
    .notification-content {
        background: var(--bg-secondary);
        color: var(--text-primary);
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        background: var(--bg-primary);
        color: var(--text-primary);
        border-color: var(--border-color);
    }
}
