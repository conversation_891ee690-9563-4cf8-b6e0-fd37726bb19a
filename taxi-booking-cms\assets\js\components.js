/**
 * Components - Các component UI cho Taxi Booking CMS
 */

class UIComponents {
    constructor() {
        this.data = window.dataManager;
    }

    /**
     * Render Header
     */
    renderHeader() {
        const settings = this.data.getSettings();
        const navigation = this.data.get('navigation');
        const socialLinks = settings.socialLinks;

        // Update site title
        document.getElementById('site-title').textContent = settings.siteName;
        document.getElementById('site-name').textContent = settings.siteName;

        // Update contact info in header
        document.querySelector('#header-phone .phone-number').textContent = settings.phone;
        document.querySelector('#header-email .email').textContent = settings.email;

        // Update logo
        const logoImg = document.getElementById('site-logo');
        if (settings.logo) {
            logoImg.src = settings.logo;
            logoImg.style.display = 'block';
        }

        // Render social links
        const socialLinksContainer = document.getElementById('social-links');
        socialLinksContainer.innerHTML = socialLinks.map(link => `
            <a href="${link.url}" target="_blank" title="${link.name}">
                <i class="${link.icon}"></i>
            </a>
        `).join('');

        // Render navigation
        const navMenu = document.getElementById('nav-menu');
        navMenu.innerHTML = navigation.filter(item => item.active).map(item => `
            <li><a href="${item.url}">${item.name}</a></li>
        `).join('');
    }

    /**
     * Render Hero Section
     */
    renderHero() {
        const settings = this.data.getSettings();
        
        // Update hero content
        document.getElementById('hero-title').textContent = settings.heroTitle;
        document.getElementById('hero-subtitle').textContent = settings.heroSubtitle;

        // Render hero slider
        const heroSlider = document.getElementById('hero-slider');
        heroSlider.innerHTML = settings.heroSlides.map(slide => `
            <div class="hero-slide ${slide.active ? 'active' : ''}" 
                 style="background-image: url('${slide.image}')">
            </div>
        `).join('');

        // Auto slide
        this.initHeroSlider();
    }

    /**
     * Initialize Hero Slider
     */
    initHeroSlider() {
        const slides = document.querySelectorAll('.hero-slide');
        if (slides.length <= 1) return;

        let currentSlide = 0;
        
        setInterval(() => {
            slides[currentSlide].classList.remove('active');
            currentSlide = (currentSlide + 1) % slides.length;
            slides[currentSlide].classList.add('active');
        }, 5000);
    }

    /**
     * Render Services
     */
    renderServices() {
        const services = this.data.getServices().filter(service => service.active);
        const servicesGrid = document.getElementById('services-grid');

        servicesGrid.innerHTML = services.map(service => `
            <div class="service-card ${service.featured ? 'featured' : ''}" data-aos="fade-up">
                <div class="service-card-icon">
                    <i class="${service.icon}"></i>
                </div>
                ${service.image ? `<img src="${service.image}" alt="${service.name}" class="service-card-image">` : ''}
                <h3>${service.name}</h3>
                <p>${service.description}</p>
                <div class="service-card-price">
                    ${service.price} <span style="font-size: 0.8em; font-weight: normal;">${service.priceUnit}</span>
                </div>
                <ul class="service-card-features">
                    ${service.features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
                <div class="service-card-actions">
                    <button class="btn btn-primary" onclick="selectService(${service.id})">
                        <i class="fas fa-car"></i> Chọn Dịch Vụ
                    </button>
                    <button class="btn btn-outline" onclick="openBookingModal()">
                        <i class="fas fa-phone"></i> Gọi Ngay
                    </button>
                </div>
            </div>
        `).join('');

        // Update service options in booking form
        this.updateServiceOptions();
    }

    /**
     * Update Service Options in Booking Form
     */
    updateServiceOptions() {
        const services = this.data.getServices().filter(service => service.active);
        const serviceSelect = document.getElementById('service-type');
        
        serviceSelect.innerHTML = '<option value="">Chọn loại dịch vụ</option>' + 
            services.map(service => `
                <option value="${service.id}">${service.name} - ${service.price}${service.priceUnit}</option>
            `).join('');
    }

    /**
     * Render Contact Info
     */
    renderContactInfo() {
        const contactInfo = this.data.get('contactInfo');
        const contactInfoContainer = document.getElementById('contact-info');

        contactInfoContainer.innerHTML = contactInfo.map(info => `
            <div class="contact-info-item">
                <div class="contact-info-icon">
                    <i class="${info.icon}"></i>
                </div>
                <div class="contact-info-content">
                    <h4>${info.title}</h4>
                    <p>
                        ${info.link && info.link !== '#' ? 
                            `<a href="${info.link}">${info.content}</a>` : 
                            info.content
                        }
                    </p>
                </div>
            </div>
        `).join('');
    }

    /**
     * Render Footer
     */
    renderFooter() {
        const settings = this.data.getSettings();
        const footerSections = this.data.get('footerSections');
        const footerContent = document.getElementById('footer-content');

        footerContent.innerHTML = footerSections.map(section => {
            let content = '';
            
            switch (section.type) {
                case 'text':
                    content = `
                        <div class="footer-section">
                            <h3>${section.title}</h3>
                            <p>${section.content}</p>
                        </div>
                    `;
                    break;
                    
                case 'links':
                    content = `
                        <div class="footer-section">
                            <h3>${section.title}</h3>
                            <ul>
                                ${section.links.map(link => `
                                    <li><a href="${link.url}"><i class="fas fa-chevron-right"></i> ${link.name}</a></li>
                                `).join('')}
                            </ul>
                        </div>
                    `;
                    break;
                    
                case 'contact':
                    content = `
                        <div class="footer-section">
                            <h3>${section.title}</h3>
                            <ul>
                                ${section.contacts.map(contact => `
                                    <li>
                                        <a href="${contact.link}">
                                            <i class="${contact.icon}"></i> ${contact.text}
                                        </a>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    `;
                    break;
            }
            
            return content;
        }).join('');

        // Update footer company name and year
        document.getElementById('footer-company-name').textContent = settings.companyName;
        document.getElementById('current-year').textContent = new Date().getFullYear();
    }

    /**
     * Render Floating Contacts
     */
    renderFloatingContacts() {
        const floatingContacts = this.data.get('floatingContacts').filter(contact => contact.active);
        const floatingContactsContainer = document.getElementById('floating-contacts');

        floatingContactsContainer.innerHTML = floatingContacts.map(contact => `
            <a href="${contact.link}" class="floating-btn" 
               style="background: ${contact.color}" 
               title="${contact.title}">
                <i class="${contact.icon}"></i>
            </a>
        `).join('');
    }

    /**
     * Show Notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.getElementById('notification');
        const icon = notification.querySelector('.notification-icon');
        const messageEl = notification.querySelector('.notification-message');

        // Set icon based on type
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        icon.className = `notification-icon ${icons[type]}`;
        messageEl.textContent = message;
        
        // Set notification type
        notification.className = `notification ${type}`;
        
        // Show notification
        notification.classList.add('show');

        // Auto hide
        setTimeout(() => {
            notification.classList.remove('show');
        }, duration);
    }

    /**
     * Render Stats (for admin)
     */
    renderStats() {
        const stats = this.data.getStats();
        
        return `
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">${stats.totalBookings}</span>
                    <span class="stat-label">Tổng Đặt Xe</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${stats.pendingBookings}</span>
                    <span class="stat-label">Chờ Xử Lý</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${stats.completedBookings}</span>
                    <span class="stat-label">Hoàn Thành</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${stats.unreadMessages}</span>
                    <span class="stat-label">Tin Nhắn Mới</span>
                </div>
            </div>
        `;
    }

    /**
     * Format Date
     */
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Format Currency
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    }

    /**
     * Validate Form
     */
    validateForm(formData, rules) {
        const errors = {};

        Object.keys(rules).forEach(field => {
            const rule = rules[field];
            const value = formData[field];

            if (rule.required && (!value || value.trim() === '')) {
                errors[field] = `${rule.label} là bắt buộc`;
                return;
            }

            if (value && rule.pattern && !rule.pattern.test(value)) {
                errors[field] = `${rule.label} không đúng định dạng`;
                return;
            }

            if (value && rule.minLength && value.length < rule.minLength) {
                errors[field] = `${rule.label} phải có ít nhất ${rule.minLength} ký tự`;
                return;
            }

            if (value && rule.maxLength && value.length > rule.maxLength) {
                errors[field] = `${rule.label} không được quá ${rule.maxLength} ký tự`;
                return;
            }
        });

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    /**
     * Show Form Errors
     */
    showFormErrors(errors) {
        // Clear previous errors
        document.querySelectorAll('.form-error').forEach(el => el.remove());

        Object.keys(errors).forEach(field => {
            const input = document.querySelector(`[name="${field}"]`);
            if (input) {
                const errorEl = document.createElement('div');
                errorEl.className = 'form-error';
                errorEl.style.color = 'var(--danger-color)';
                errorEl.style.fontSize = 'var(--font-size-sm)';
                errorEl.style.marginTop = 'var(--spacing-xs)';
                errorEl.textContent = errors[field];
                
                input.parentNode.appendChild(errorEl);
                input.style.borderColor = 'var(--danger-color)';
            }
        });
    }

    /**
     * Clear Form Errors
     */
    clearFormErrors() {
        document.querySelectorAll('.form-error').forEach(el => el.remove());
        document.querySelectorAll('input, textarea, select').forEach(el => {
            el.style.borderColor = '';
        });
    }

    /**
     * Render All Components
     */
    renderAll() {
        this.renderHeader();
        this.renderHero();
        this.renderServices();
        this.renderContactInfo();
        this.renderFooter();
        this.renderFloatingContacts();
    }
}

// Khởi tạo UIComponents global
window.uiComponents = new UIComponents();
