{"domain": "simply-static", "locale_data": {"simply-static": {"": {"domain": "simply-static", "plural_forms": "nplurals=2; plural=n != 1;", "lang": "de_DE"}, "This is known to work well with the Simply Static plugin.": ["<PERSON><PERSON> P<PERSON>in ist mit Simply Static kompatibel."], "Simply Static requires PHP 7.4 or higher.": ["Simply Static benötigt PHP 7.4 oder höher."], "You need to update Simply Static Pro to version 1.6.3.2 before continuing to use Simply Static, as we made significant changes requiring an upgrade.": ["Du musst Simply Static Pro auf Version 1.6.3.2 aktualisieren, bevor du Simply Static weiterhin benutzt."], "You need to update Simply Static Pro to version  1.6.3.2 before continuing to use Simply Static, as we made significant changes requiring an upgrade.": ["Du musst Simply Static Pro auf Version 1.6.3.2 aktualisieren, bevor du Simply Static weiterhin benutzt."], "Activity Log": ["Aktivitätsprotokoll"], "Changing ...": ["Wechseln.."], "Environment": ["Umgebung"], "A new environment will be created with the current configuration.": ["Eine neue Umgebung wird erstellt mit der aktuellen Konfiguration."], "Creating...": ["<PERSON><PERSON><PERSON>.."], "Create": ["<PERSON><PERSON><PERSON><PERSON>"], "Cancel": ["Abbrechen"], "Choose an environment or create a new one to configure settings.": ["<PERSON><PERSON>hle eine Umgebung oder erstelle eine neue mit den aktuellen Einstellungen."], "Delete selected environment": ["Lösche die ausgewählte Umgebung"], "Requires saving settings": ["Benötigt das Speichern der Einstellungen"], "Automated Redirects with Redirection": ["Automatische Weiterleitungen mit Redirection"], "Cookie Consent with Complianz": ["<PERSON><PERSON> mit Complianz"], "Requires Simply Static Pro": ["<PERSON><PERSON><PERSON><PERSON> Simply Static Pro"], "Get the Pro version": ["Hole dir die Pro-Version"], "Download Log": ["Log herunterladen"], "Clear Log": ["Log löschen"], "Log file cleared.": ["Log-<PERSON><PERSON> erfolgreich geleert."], "Use current settings": ["Nutze aktuelle Einstellungen"], "Generate Static Files": ["<PERSON><PERSON><PERSON>"], "Generating...": ["Generiere.."], "Cancel Export": ["Export abbrechen"], "Changelog": ["Changelog"], "Documentation": ["Dokumentation"], "Export": ["Export"], "Update": ["Aktualisieren"], "Update (Requires Simply Static Pro)": ["Aktualisieren (benötigt Simply Static Pro)"], "Import": ["Import"], "Choose a subsite to import settings from.": ["Wähle eine Subsite um Einstellungen zu importieren."], "Import Settings": ["Import-Einstellungen"], "Settings successfully imported.": ["Einstellungen erfolgreich importiert."], "Tools": ["Tools"], "Diagnostics": ["Diagnose"], "Settings": ["Einstellungen"], "General": ["Allgemein"], "Deploy": ["Deployment"], "Forms": ["Formulare"], "Search": ["<PERSON><PERSON>"], "Optimize": ["Optimierung"], "Advanced": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Integrations": ["Integrationen"], "Utilities": ["Werkzeuge"], "Debug": ["Debugging"], "Video Course": ["<PERSON> Kurs"], "Tutorials": ["Tutorials"], "There are errors in diagnostics that may negatively affect your static export.": ["<PERSON><PERSON> gib<PERSON> in der Diagnose welche möglicherweise deinen Export beeinträchtigen."], "Please review them and get them fixed to avoid problems.": ["Bitte überprüfe und behebe die Meldungen um Fehlern vorzubeugen."], "Visit Diagnostics": ["Diagnose ansehen"], "You are using the pro version without a valid license.": ["Du nutzt die Pro-Version ohne gültige Lizenz."], "We have temporarily disabled all the pro features now. Please contact our support to have the problem solved.": ["Wir haben alle Pro-Funktionen vorübergehend deaktiviert. Bitte kontaktiere den Support um das Problem zu beheben."], "Basic Auth": ["Basic Auth"], "How to set up basic auth": ["Wie man Basic Auth konfiguriert"], "If you've secured WordPress with HTTP Basic Auth you need to specify the username and password to use below.": ["Wenn du deine WordPress-Seite mit HTTP BASIC Auth geschützt hast, trage Nutzername und Password hier ein."], "Basic Auth Username": ["Basic Auth Nutzername"], "Basic Auth Password": ["Basic Auth Passwort"], "Enable Basic Auth": ["Basic Auth aktivieren"], "Automatically setting up Basic Auth requires Simply Static Pro.": ["Automatische Einrichtung von Basic Auth benötigt Simply Static Pro."], "Once enabled we will put your entire website behind password protection.": ["<PERSON><PERSON><PERSON><PERSON>, wird deine gesamte WordPress-Seite mit einem Passwort-<PERSON><PERSON><PERSON>hen."], "Requires Username & Password to work": ["Benötigt Nutzername und Passwort"], "Temporary Files": ["Temporä<PERSON>"], "Temporary Files Directory": ["Temporäres <PERSON>iverzeichnis"], "Optionally specify the directory to save your temporary files. This directory must exist and be writeable.": ["Lege das temporäre Dateiverzeichnis fest. Diese Verzeichnis muss existieren und beschreibbar sein."], "Whitelist Plugins": ["Plugins whitelisten"], "Whitelist plugins in diagnostics": ["Plugins whitelisten und aus Diagnose-Report ausschließen"], "If you want to exclude certain plugins from the diagnostics check add the plugin slugs here (one per line).": ["Wenn du bestimmte Plugins vom Diagnose-Report ausschließen möchtest, trage sie hier ein (ein Plugin pro Zeile)."], "Proxy Setup": ["Proxy-Konfiguration"], "Origin URL": ["Quell-URL"], "If the URL of your WordPress installation differs from the public-facing URL (Proxy Setup), add the public URL here.": ["Wenn die URL deiner WordPress-Installation von der öffentlichen URL abweicht (Proxy), für die öffentliche URL hier ein."], "Debug Log": ["Debug-Log"], "Activate Debug Log": ["Debug-Log aktivieren"], "Enable it to download the debug log from Simply Static -> Generate.": ["Aktiviere den Debug Log um die Datei in Simply Static -> Aktivitätsprotokoll herunterzuladen."], "Cron": ["<PERSON><PERSON>"], "Use server-side cron job": ["<PERSON><PERSON>er einen server-seitigen Cron-Job"], "Enable this if you use a server-side cron job instead of the default WP-Cron.": ["Aktiviere diese Option wenn du einen server-seitigen Cron-Job statt dem standard WP-Cron verwendest."], "Settings saved successfully.": ["Einstellungen erfolgreich gespeichert."], "Save Settings": ["Einstellungen speichern"], "ZIP Archive": ["ZIP Archiv"], "Local Directory": ["Lokales Verzeichnis"], "SFTP": ["SFTP"], "GitHub": ["GitHub"], "AWS S3": ["AWS S3"], "Bunny CDN": ["<PERSON>"], "Tiiny.host": ["Tiiny.host"], "No page selected": ["Keine Seite ausgewählt"], "Simply Static Studio": ["Simply Static Studio"], "Deployment Settings": ["Deployment Einstellungen"], "Choose from a variety of deployment methods. Depending on your selection we either provide a ZIP file, export to a local directory or send your files to a remote destination.": ["Wähle eine der unterschiedlichen Deployment-Optionen. Basierend auf deiner Einstellung erhälst du eine Liste von zusätzlichen Optionen."], "Deployment method": ["Deployment-Methode"], "What is Simply Static Studio?": ["Was ist Simply Static Studio?"], "The static site hosting platform for your Static Studio powered WordPress websites.": ["Ein statischer Hosting-Service für Websites die auf Static Studio betrieben werden."], "ZIP": ["ZIP"], "How to export a ZIP file": ["Wie man eine ZIP-<PERSON><PERSON> exportiert"], "Get a download link in the activity log once the static export has finished.": ["Du bekommst einen Download-Link im Aktivitätsprotokoll angezeigt sobald der Export abgeschlossen ist."], "How to deploy to a local directory": ["Wie man in ein lokales Verzeichnis exportiert"], "Path": ["Pfad"], "This is the directory where your static files will be saved. We will create it automatically on the first export if it doesn't exist.": ["Dies ist das Verzeichnis in dem deine statischen Dateien gespeichert werden. Dieses Verzeichnis muss existieren und beschreibbar sein."], "Copied home path": ["Homeverzeichnis kopiert"], "Copy home path": ["Homeverzeichnis kopieren"], "Clear Local Directory": ["Lokales Verzeichnis leeren"], "Clear local directory before running an export.": ["Leere lokales Verzeichnis vor nächstem Export."], "Don't clear local directory before running an export.": ["Leere lokales Verzeichnis nicht vor nächstem Export."], "How to deploy to a GitHub (2/2)": ["Wie man zu GitHub exportiert (2/2)"], "GitHub enables you to export your static website to one of the common static hosting providers like Netlify, Cloudflare Pages or GitHub Pages.": ["GitHub ermöglicht es dir deine statische Seite auf einem der populären statischen Hostinganbieter zu hosten. Beispiele sind Netlify, Cloudflare Pages oder GitHub Pages."], "Account Type": ["Account-<PERSON><PERSON>"], "Depending on the account type the settings fields will change.": ["Basierend auf deinem Account-Ty<PERSON> werden sich die Einstellungen ändern."], "Personal": ["Personal"], "Organization": ["Organization"], "Enter the name of your organization.": ["Trage den Namen deiner Organisation ein."], "Username": ["<PERSON><PERSON><PERSON><PERSON>"], "Enter your GitHub username.": ["Trage deinen GitHub Nutzernamen ein."], "E-Mail": ["E-Mail"], "Enter your GitHub email address. This will be used to commit files to your repository.": ["Trage deine GitHub E-Mail-Adresse ein."], "Personal Access Token": ["Personal Access Token"], "How to prepare your GitHub account": ["Wie du deinen GitHub Account vorbereitest"], "You need a personal access token from GitHub. Learn how to get one ": ["<PERSON> benötigst deinen Personal Access Token um eine Verbindung zu GitHub aufzubauen "], "here": ["hier"], "Repository": ["Repository"], "Enter a name for your repository (lowercase without spaces or special characters).": ["Trage den Namen deines Repositories ein. Dieser Name sollte aus Kleinbuchstaben bestehen und keine Leer-und Sonderzeichen enthalten."], "Ensure to create the repository and add a readme file to it before running an export as shown in the docs ": ["Stelle sicher das dein Repository ein README file enthält bevor du den Export ausführst "], "Folder": ["Verzeichnis"], "Enter a relative path to a folder if you want to push files under it. Example: for github.com/USER/REPOSITORY/folder1, enter folder1": ["Füge einen relativen Pfad ein. Beispiel: für github.com/USER/REPOSITORY/folder1 setze folder1 ein"], "You need to create the repository manually within your organization before connecting it.": ["Du musst das Repository manuell in deinem Konto einrichten bevor du die Verbindung aufbauen kannst."], "Visiblity": ["Sichtbarkeit"], "Decide if you want to make your repository public or private.": ["Entscheide ob dein Repository public oder private sein soll."], "Public": ["Public"], "Private": ["Private"], "Branch": ["Branch"], "Simply Static automatically uses \"main\" as branch. You may want to modify that for example to gh-pages. for GitHub Pages.": ["Simply Static nutzt automatisch den \"main\" branch. Du kannst den Branch mit dieser Einstellung anpassen."], "Webhook URL": ["Webhook URL"], "Enter your Webhook URL here and Simply Static will send a POST request after all files are commited to GitHub.": ["Trage deine Webhook URL hier ein. Simply Static wird einen POST request an diese URL senden sobald alle Dateien auf GitHub hochgeladen wurden."], "Throttle Requests": ["Anfragen pro Sekunde limitieren"], "Enable this option if you are experiencing issues with the GitHub API rate limit.": ["Aktiviere die Option wenn du Probleme mit deinem GitHub Rate Limit bekommst."], "Batch size": ["Batch-Größe"], "Enter the number of files you want to be processed in a single batch. If current export fails to deploy, lower the number.": ["Trage die Anzahl der Seiten/Dateien ein die pro Durchlauf exportiert werden sollen. Falls es Probleme bei der Übertragung gibt, reduziere die Anzahl."], "How to deploy to Tiiny.host": ["<PERSON>ie man zu Tiiny.host exportiert"], "Deploying to Tiiny.host is the easiest and fastest deployment option available in Simply Static Pro.": ["Die Übertragung zur Tiiny.host ist die schnellste Deployment-Option in Simply Static Pro."], "This field is auto-filled with the e-mail address used for activating Simply Static Pro.": ["Dieses Feld wird automatisch mit deiner Admin-E-Mail-Adresse ausgefüllt. Ein Konto wird automatisch bei der ersten Übertragung erstellt."], "An account will be created automatically on your first deployment.": ["Ein Konto wird automatisch bei der ersten Übertragung erstellt."], "Subdomain": ["Subdomain"], "That's the part before your TLD. Your full URL is the combination of the subdomain plus the domain suffix.": ["Das ist der Part vor deiner Domain. Deine URL ist die Kombination aus Subdomain und Domain-Suffix."], "Domain Suffix": ["Domain-Suffix"], "This defaults to tiiny.site. If you have a custom domain configured in Tiiny.host, you can also use  that one.": ["Wenn du noch keine eigene Domain hast, nutzen wir .tiiny.site."], "Password Protection": ["Passwort-Schutz"], "Adding a password will activate password protection on your static site. The website is only visible with the password.": ["Deine statische Website wird mit dem eingetragenen Passwort geschützt und ist erst nach Eingabe erreichbar."], "How to deploy to Bunny CDN": ["Wie man zu BunnyCDN exportiert"], "Bunny CDN is a fast and reliable CDN provider that you can run your static website on.": ["Bunny CDN ist eine schnelle und zuverlässige Option um deine statische Seite zu hosten."], "Bunny CDN API Key": ["Bunny CDN API Key"], "Enter your API Key from Bunny CDN. You can find your API-Key as described ": ["Trage deinen Bunny CDN API Key hier ein "], "Storage Host": ["Storage Host"], "Depending on your location, you have a different storage host. You find out which URL to use ": ["Basierend auf deinem Standort benötigst du womöglich einen anderen Host.  Du findest mehr dazu in der Dokumentation von BunnyCDN "], "Bunny CDN Access Key": ["Bunny CDN Access Key"], "Enter your Acess Key from Bunny CDN. You will find it within your storage zone setttings within FTP & API Access -> Password.": ["Trage deinen Access Key hier ein. Du findest ihn in deinem BunnyCDN Konto unter Storage Zone -> Settings -> FTP & API Access."], "Pull Zone": ["Pull Zone"], "A pull zone is the connection of your CDN to the internet. Simply Static will try to find an existing pull zone with the provided name, if there is none it creates a new pull zone.": ["Pullzones sind vergleichbar mit Domains. Wenn du noch keine Pullzone in BunnyCDN erstellst hast, übernimmt Simply Static das für dich."], "Storage Zone": ["Storage Zone"], "A storage zone contains your static files. Simply Static will try to find an existing storage zone with the provided name, if there is none it creates a new storage zone.": ["Storage Zones sind der physische Speicher für deine statischen Dateien. Simply Static erstellt dies automatisch falls du es noch nicht gemacht hast."], "Subdirectory": ["Unterverzeichnis"], "If you want to transfer the files to a specific subdirectory on your storage zone add the name of that directory here.": ["Wenn du deine statischen Dateien in einem Unterverzeichnis ablegen möchtest, gib den Pfad hier ein."], "Amazon AWS S3": ["Amazon AWS S3"], "How to deploy to Amazon AWS S3": ["Wie man zu Amazon AWS S3 exportiert"], "Access Key ID": ["Access Key ID"], "Enter your Access Key from AWS. Learn how to get one ": ["Trage deinen AWS Access Key hier ein "], "Secret Access Key": ["Secret Access Key"], "Enter your Secret Key from AWS. Learn how to get one ": ["Tragen deinen AWS Secret Key hier ein "], "Region": ["Region"], "US East (Ohio)": ["US East (Ohio)"], "US East (N. Virginia)": ["US East (N. Virginia)"], "US West (N. California)": ["US West (N. California)"], "US West (Oregon)": ["US West (Oregon)"], "Africa (Cape Town)": ["Africa (Cape Town)"], "Asia Pacific (Hong Kong)": ["Asia Pacific (Hong Kong)"], "Asia Pacific (Hyderabad)": ["Asia Pacific (Hyderabad)"], "Asia Pacific (Jakarta)": ["Asia Pacific (Jakarta)"], "Asia Pacific (Melbourne)": ["Asia Pacific (Melbourne)"], "Asia Pacific (Mumbai)": ["Asia Pacific (Mumbai)"], "Asia Pacific (Osaka)": ["Asia Pacific (Osaka)"], "Asia Pacific (Seoul)": ["Asia Pacific (Seoul)"], "Asia Pacific (Singapore)": ["Asia Pacific (Singapore)"], "Asia Pacific (Sydney)": ["Asia Pacific (Sydney)"], "Asia Pacific (Tokyo)": ["Asia Pacific (Tokyo)"], "Canada (Central)": ["Canada (Central)"], "Europe (Frankfurt)": ["Europe (Frankfurt)"], "Europe (Ireland)": ["Europe (Ireland)"], "Europe (London)": ["Europe (London)"], "Europe (Milan)": ["Europe (Milan)"], "Europe (Paris)": ["Europe (Paris)"], "Europe (Spain)": ["Europe (Spain)"], "Europe (Stockholm)": ["Europe (Stockholm)"], "Europe (Zurich)": ["Europe (Zurich)"], "Middle East (Bahrain)": ["Middle East (Bahrain)"], "Middle East (UAE)": ["Middle East (UAE)"], "South America (São Paulo)": ["South America (São Paulo)"], "AWS GovCloud (US-East)": ["AWS GovCloud (US-East)"], "AWS GovCloud (US-West)": ["AWS GovCloud (US-West)"], "Bucket": ["Bucket"], "Add the name of your bucket here.": ["Trage den Namen deines Buckets hier ein."], "Add an optional subdirectory for your bucket": ["<PERSON>üge ein optionales Unterverzeichnis ein"], "Cloudfront Distribution ID": ["Cloudfront Distribution ID"], "We automatically invalidate the cache after each export.": ["Wir leeren den Cache automatisch nach jedem statischen Export."], "Empty bucket before new export?": ["<PERSON>len wir den Inhalt des Buckets löschen bevor wir die neuen Dateien hochladen?"], "Clear bucket before new export.": ["Bucket vor neuem Export löschen."], "Don't clear bucket before new export.": ["<PERSON>et nicht vor neuem Export löschen."], "S3-compatible Storage": ["S3-kompatible Speicher"], "How to deploy to S3 compatible storages?": ["Wie man zu S3-kompatiblen Speichern exportiert?"], "Enter your Access Key from your S3 provider.": ["Trage deinen AWS Access Key hier ein."], "Enter your Secret Key from S3 provider.": ["Tragen deinen AWS Secret Key hier ein."], "Base URL": ["Basis URL"], "Add the base URL of the S3 service.": ["Füge die Basis-URL des S3 Dienstes ein."], "How to deploy via SFTP": ["Wie man zu einem SFTP Server exportiert"], "Host": ["Host"], "Enter your SFTP host.": ["Trage deinen SFTP Host ein."], "Port": ["Port"], "Enter your SFTP port.": ["Trage deinen SFTP Port ein."], "SFTP username": ["SFTP Nutzername"], "Enter your SFTP username.": ["Trage deinen SFTP Nutzernamen ein."], "SFTP password": ["SFTP Passwort"], "Enter your SFTP password.": ["Trage dein SFTP Passwort ein."], "SFTP private key": ["SFTP Private Key"], "OPTIONAL: This is only required if you need to authenticate via a private key to access your SFTP server.": ["Optional: Dies ist nur erforderlich wenn du dich mit einem privaten Schlüssel statt einem Passwort mit deinem SFTP Server verbinden möchtest."], "Enter your SFTP private key if you want password.less upload and the server is configured to allow it. You can set it as a constant in wp-config.php by using define('SSP_SFTP_KEY', 'YOUR_KEY')": ["Füge deinen SFTP Private Key ein falls du dich ohne Passwort bei deinem SFTP server anmelden möchtest. Du kannst den Schlüssel auch in deiner wp-config.php definieren mit der Konstante define('SSP_SFTP_KEY', 'YOUR_KEY')"], "SFTP folder": ["SFTP Verzeichnis"], "Leave empty to upload to the default SFTP folder. Enter a folder path where you want the static files to be uploaded to (example: \"uploads\" will upload to uploads folder. \"uploads/new-folder\" will upload files to \"new-folder\"). ": ["Trage den Pfad auf deinem SFTP-Server ein in dem du die Dateien ablegen möchtest. Lasse das Feld leer falls es das Stammverzeichnis sein soll. "], "Save settings to test": ["Einstellungen speichern zum testen"], "Test Deployment": ["Test-Export"], "How to use diagnostics": ["Wie man die Diagnose benutzt"], "Our diagnostics tool provides detailed insights into your WordPress installation and server configuration and tells you exactly what needs to be optimized to get the most out of Simply Static. Click the button below to get the latest results.": ["Unser Diagnose-<PERSON><PERSON> zeigt dir alle WordPress-und Serverkonfiguration die nötig sind um Simply Static bestmöglich auszuführen."], "Reset Diagnostics": ["Diagnose-<PERSON><PERSON>"], "Diagnostics resetted successfully.": ["Diagnose erfolgreich zurückgesetzt."], "Use forms?": ["<PERSON><PERSON> benutzen?"], "Use Forms on your static website.": ["Nutze Formulare auf deiner statischen Website."], "Don't use forms on your static website.": ["Nutze keine Formulare auf deiner statischen Website."], "Create a form connection": ["<PERSON><PERSON><PERSON> eine Formular-Verbindung"], "Comments": ["Kommentare"], "Use comments?": ["Komme<PERSON><PERSON> benutzen?"], "Use comments on your static website.": ["Nutze Kommentare auf deiner statischen Website."], "Don't use comments on your static website.": ["Nutze keine Kommentare auf deiner statischen Website."], "Select a redirect page": ["Wähle eine Weiterleitungs-Seite"], "The post will be regenerated after comment submission, but it might take a while so its good practice to redirect the visitor.": ["Der Beitrag wird automatisch neu generiert sobald ein Kommentar hinzugefügt wurde. Damit deine Besucher ein direktes Feedback bekommen, solltest du eine Weiterleitungsseite hinterlegen."], "CORS": ["CORS"], "How to deal with CORS": ["Wie man mit CORS umgeht"], "When using Forms and Comments in Simply Static Pro you may encounter CORS issues as you make requests from your static website to your original one.": ["<PERSON>n du Formulare oder Kommentare in Simply Static Pro benutzt, wirst du womöglich CORS Probleme haben, da <PERSON> <PERSON><PERSON><PERSON> von deiner statischen Seite zu deiner WordPress Website machst."], "Due to the variety of server setups out there, you may need to make changes on your server.": ["Durch die Menge an unterschiedlichen Server-Setups musst du womöglich Anpassungen an deinem Server vornehmen."], "Static URL": ["Statische URL"], "Add the URL of your static website to allow CORS from it.": ["Trage die URL zu deiner statischen Seite hier ein um CORS Anfragen zu erlauben."], "Select CORS method": ["Wähle eine CORS-Methode"], "Choose one of the methods to allow CORS for your website.": ["W<PERSON>hle einer der Methoden um CORS Anfragen zu erlauben."], "Embed Dynamic Content (iFrame)": ["Dynamischen Inhalt einbetten (iFrame)"], "We replace the HTML of the URLs with an iFrame that embeds the content directly from your WordPress website.": ["Wir ersetzen das HTML der URLs durch ein iFrame, das den Inhalt direkt von Ihrer WordPress-Website einbettet."], "This way you can use dynamic elements on your static website without the need of a specific integration.": ["Auf diese Weise können Sie dynamische Elemente auf Ihrer statischen Website verwenden, ohne dass eine spezielle Integration erforderlich ist."], "This requires your WordPress website to be online all the time.": ["<PERSON><PERSON>, dass Ihre WordPress-Website ständig online ist."], "URLs to embed as an iFrame": ["Als iFrame einzubettende URLs"], "If you want to embed specific pages from your WordPress website into your static website, add the URLs here (one per line).": ["Simply Static wird eine statische Kopie jeder Unterseite anlegen die es finden kann und beginnt bei %s. Wenn du zusätzliche Dateien, welche nirgendwo verlinkt sind, hinz<PERSON><PERSON><PERSON> möchtest, kannst du dies hier tun. Bitte trage eine URL je Zeile ein."], "Custom CSS": ["Benutzerdefiniertes CSS"], "These styles will only apply to the embedded pages, not your entire website.": ["Diese Stile gelten nur für die eingebetteten Seiten, nicht für Ihre gesamte Website."], "Replacing URLs": ["URLs ersetzen"], "How to replace URLs": ["Wie man URLs ersetzt"], "When exporting your static site, any links to your WordPress site will be replaced by one of the following: absolute URLs, relative URLs, or URLs contructed for offline use.": ["Wenn du eine statische Seite exportierst, werden alle Links deiner WordPress-Seite durch eine der folgenden Optionen ersetzt: absolute URLs, relative URLs oder konstruierte URLs für die Offline-Speicherung."], "Absolute URLs": ["Absolute URLs"], "Relative Path": ["Relativer <PERSON><PERSON>"], "Offline Usage": ["Offline"], "Scheme": ["<PERSON><PERSON><PERSON>"], "Convert all URLs for your WordPress site to absolute URLs at the domain specified above.": ["Konvertiere alle URLs deiner WordPress-Seite zu absoluten URLs."], "Convert all URLs for your WordPress site to relative URLs that will work at any domain.": ["Konvertiere alle URLs deiner WordPress-Seite zu relativen URLs."], "Optionally specify a path above if you intend to place the files in a subdirectory.": ["<PERSON>üge ein Unterverzeichnis ein (optional)"], "Example": ["Beispiel"], "enter /path above if you wanted to serve your files at www.example.com/path/": ["füge /pfad/ ein, falls deine Seite unter www.example.com/path/ erreichbar sein soll"], "Convert all URLs for your WordPress site so that you can browse the site locally on your own computer without hosting it on a web server.": ["Konvertiert alle deine WordPress-URLs so, dass sie auf deinem lokalen Computer geöffnet werden können. Dies ist nicht für die Verwendung auf einem Webserver geeignet."], "Force URL replacements": ["Erweitertes URL-Ersetzen"], "Replace all occurrences of the WordPress URL with the static URL (includes inline CSS and JS).": ["Ersetze alle Vorkommnisse deiner WordPress URL mit deiner statischen URL."], "Replace only occurrences of the WordPress URL that match our tag list.": ["Ersetze alle Vorkommnisse deiner WordPress URL die mit dem Tag matchen."], "Include": ["Einschließen"], "Include & Exclude files and pages": ["Einschließen und Ausschließen von Dateien und Seiten"], "Additional URLs": ["Zusätzliche URLs"], "If you want to create static copies of pages or files that aren't linked to, add the URLs here (one per line).": ["Simply Static wird eine statische Kopie jeder Unterseite anlegen die es finden kann und beginnt bei %s. Wenn du zusätzliche Dateien, welche nirgendwo verlinkt sind, hinz<PERSON><PERSON><PERSON> möchtest, kannst du dies hier tun. Bitte trage eine URL je Zeile ein."], "Additional Files and Directories": ["Zusätzliche Dateien und Verzeichnisse"], "Sometimes you may want to include additional files (such as files referenced via AJAX) or directories. Add the paths to those files or directories here (one per line).": ["Simply Static wird eine statische Kopie jeder Datei anlegen die es finden kann. Wenn du zusätzliche Dateien, welche nirgendwo verlinkt sind, hinz<PERSON><PERSON><PERSON> möcht<PERSON>, kannst du dies hier tun. Bitte trage eine URL je Zeile ein."], "Generate 404 Page?": ["Generiere eine 404-Fehlerseite?"], "How to manage 404 pages?": ["Wie verwalte ich 404-Seiten?"], "Generate a 404 page.": ["Generiere 404-Fehlerseite."], "Don't generate a 404 page.": ["Keine 404-Fehlerseite generieren."], "Exclude": ["Ausschließen"], "Urls to exclude": ["URLs zum Ausschließen"], "Specify URLs (or parts of URLs) you want to exclude from the processing (one per line).": ["Hier kannst du URLs hinterlegen, die du vom statischen Export ausschließen möchtest (eine pro Zeile)."], "Multisite": ["Multisite"], "Choose a site to export": ["<PERSON>ähle eine Seite für den Export"], "Debugging": ["Debug-Modus"], "Export Log": ["Export Log"], "Control Integrations that will be active during the export of the static site.": ["Kontrolliere die Integrationen, die während des Exports der statischen Site aktiv sein werden."], "Minify": ["Minifizieren"], "How to minify HTML, CSS and JavaScript?": ["Wie minimiert man HTML, CSS und JavaScript?"], "Minify Files?": ["<PERSON>ien minifizieren?"], "Enable minify files on your static website.": ["Aktiviere Datei-Minifizierung auf deiner statischen Website."], "Don't enable minify files on your static website.": ["Deaktiviere Datei-Minifizierung auf deiner statischen Website."], "Minify HTML": ["HTML minifizieren"], "Minify HTML files.": ["HTML minifizieren."], "Don't minify HTML files.": ["HTML nicht minifizieren."], "Leave quotes inside HTML attributes": ["Anführungszeichen in HTML-Attributen belassen"], "If there are issues with comments or JavaScript when minifying HTML, toggle this ON.": ["<PERSON><PERSON> beim Minimieren von HTML Probleme mit Kommentaren oder JavaScript auftreten, schalten Si<PERSON> diese Option ein."], "Minify CSS": ["CSS minifizieren"], "Minify CSS files.": ["CSS minifizieren."], "Don't minify CSS files.": ["CSS nicht minifizieren."], "Exclude Stylesheet URLs": ["Stylesheet-URLs ausschließen"], "Exclude URLs from minification (one per line).": ["URLs von der Minimierung ausschließen (eine pro Zeile)."], "Minify Inline CSS": ["Inline-CSS minifizieren"], "Minify Inline CSS.": ["Inline-CSS minifizieren."], "Don't minify Inline CSS.": ["Inline-CSS nicht minifizieren."], "Minify JavaScript": ["JavaScript minifizieren"], "Minify JavaScript files.": ["JavaScript minifizieren."], "Don't minify JavaScript files.": ["JavaScript nicht minifizieren."], "Exclude JavaScript URLs": ["JavaScript-URLs ausschließen"], "Minify Inline JavaScript": ["Inline-JavaScript minifizieren"], "Minify Inline JavaScript.": ["Inline-JavaScript minifizieren."], "Don't minify Inline JavaScript.": ["Inline-JavaScript nicht minifizieren."], "Image Optimization": ["Bild-Optimierung"], "How to optimize images with ShortPixel?": ["Wie optimiere ich Bilder mit ShortPixel?"], "Optimize Images with ShortPixel?": ["Bilder mit ShortPixel optimieren?"], "Optimize images.": ["Bilder optimieren."], "Don't optimize images.": ["Bilder nicht optimieren."], "ShortPixel API Key": ["ShortPixel API Key"], "Backup the original images?": ["Original-Bilder al<PERSON> speiche<PERSON>?"], "Restore Original Images": ["Original-Bilder w<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Restoring...": ["Wiederherstellen.."], "Replace": ["<PERSON><PERSON><PERSON><PERSON>"], "How to replace WP default paths": ["So ersetzen Sie WP-Standardpfade"], "wp-content directory": ["wp-content Verzeichnis"], "Replace the \"wp-content\" directory.": ["Ersetze das \"wp-content\" Verzeichnis."], "wp-includes directory": ["wp-includes Verzeichnis"], "Replace the \"wp-includes\" directory.": ["Ersetze das \"wp-includes\" Verzeichnis."], "uploads directory": ["uploads Verzeichnis"], "Replace the \"wp-content/uploads\" directory.": ["Ersetz<PERSON> das \"wp-content/uploads\" Verzeichnis."], "plugins directory": ["plugins Verzeichnis"], "Replace the \"wp-content/plugins\" directory.": ["Ersetze das \"wp-content/plugins\" Verzeichnis."], "themes directory": ["themes Verzeichnis"], "Replace the \"wp-content/themes\" directory.": ["Ersetze das \"wp-content/themes\" Verzeichnis."], "Theme style name": ["Theme Stylesheet Name"], "Replace the style.css filename.": ["Ersetz<PERSON> den style.css Dateinamen."], "Author URL": ["Autoren-URLs"], "Replace the author url.": ["Ersetze die Autoren-URLs."], "Hide": ["Verstecken"], "How to hide and disable WP core features": ["So verbergen und deaktivieren Sie WP-Kernfunktionen"], "Hide REST API URLs": ["Verstecke Rest API URLs"], "Hide Style/Script IDs": ["Verstecke Style/Script IDs"], "Hide HTML Comments": ["Verstecke HTML Kommentare"], "Hide WordPress Version": ["Verstecke WordPress Version"], "Hide WordPress Generator Meta": ["Verstecke WordPress Generator Meta"], "Hide DNS Prefetch WordPress link": ["Verstecke DNS Prefetch WordPress Link"], "Hide RSD Header": ["Verstecke RSD Header"], "Hide Emojis if you don't use them": ["Verstecke <PERSON>"], "Disable": ["Deaktivieren"], "Disable XML-RPC": ["Deaktiviere XML-RPC"], "Disable Embed Scripts": ["Deaktiviere Embed <PERSON>"], "Disable DB Debug in Frontend": ["Deaktiviere DB Debug"], "Disable WLW Manifest Scripts": ["Deaktiviere WLW Manifest Scripts"], "Use search?": ["<PERSON>e ben<PERSON>?"], "Use search on your static website.": ["Nutze Suche auf deiner statischen Website."], "Don't use search on your static website.": ["Nutze keine Suche auf deiner statischen Website."], "Search Type": ["Suche-<PERSON><PERSON>"], "Decide wich search type you want to use. Fuse runs locally based on file and Algolia is an external API service.": ["Entscheide dich für deinen Suche-Typ. Fuse.js nutzt eine lokale JSON Datei und Algolia ist ein externer API-Service."], "How to select data with meta tags": ["Wie man Daten aus Metatags selektiert"], "Targeting for excerpt in the meta description tag.": ["Ziel für das Excerpt im Meta-Description-Tag."], "Adding such meta in the excerpt field would be:": ["Ein Beispiel dafür würde wie folgt aussehen:"], "Targeting for title in the property meta tag.": ["Ziel für den Title im Meta-Description-Tag."], "If the second item (after | ) is not <code>content</code>, we'll use it as value of that attribute (<code>property=\"og:title\"</code> in this example) and use <code>content</code> for value.": ["Wenn das zweite Element (nach | ) nicht <code>content</code> ist, nutzen wir den Wert dieses Attributs (<code>property=\"og:title\"</code>in diesem Beispiel <code>content</code> als Wert."], "Caution: Use meta tags that exist everywhere for title.": ["Warnung: <PERSON><PERSON><PERSON>a-Tags die überall für den Titel existieren."], "Indexing": ["Indexierung"], "CSS-Selector for Title": ["CSS-Selector für den Titel"], "Add the CSS selector which contains the title of the page/post": ["Trage den CSS-Selector ein der den Titel der Seite enthält."], "Or meta tags. Click for more information.": ["Oder Meta-Tags. Klicke für mehr Informationen."], "CSS-Selector for Content": ["CSS-Selector für den Inhalt"], "Add the CSS selector which contains the content of the page/post.": ["Trage den CSS-Selector ein der den Inhalt der Seite enthält."], "CSS-Selector for Excerpt": ["CSS-Selector für Excerpt"], "Add the CSS selector which contains the excerpt of the page/post.": ["Trage den CSS-Selector ein der die Kurzbeschreibung der Seite enthält."], "Exclude URLs": ["URLs ausschließen"], "Exclude URLs from indexing (one per line). You can use full URLs, parts of an URL or plain words (like stop words).": ["Schließe Seiten von der Indexierung aus (ein Eintrag pro Zeile). Du kannst vollständige URLs oder nur Stichwörter verwenden."], "Fuse.js": ["Fuse.js"], "How to add search with FuseJS": ["So fügen Sie mit FuseJS eine Suche hinzu"], "CSS-Selector": ["CSS-Selector"], "Add the CSS selector of your search element here.": ["Trage den CSS-Selector deines Such-Felds hier ein."], "Algolia API": ["Algolia API"], "How to add search with the Algolia API": ["So fügen Sie eine Suche mit der Algolia-API hinzu"], "Application ID": ["Application ID"], "Add your Algolia App ID.": ["Trage deine Algolia App ID hier ein."], "Admin API Key": ["Admin API Key"], "Add your Algolia Admin API Key.": ["Trage deinen Algolia Admin API Key hier ein."], "Search-Only API Key": ["Search-Only API Key"], "Add your Algolia Search-Only API Key here. This is the only key that will be visible on your static site.": ["Trage deinen Algolia Search-only API Key hier ein."], "Name for your index": ["Der Name des Indexes"], "Add your Algolia index name here.": ["Trage den Namen des Suchindexes hier ein."], "If you have multiple search elements with different CSS selectors, separate them by a comma (,) such as: .search-field, .search-field2": ["<PERSON>n du mehrere Such-<PERSON><PERSON> hast, kannst du auch mehrere Selektoren eintragen. Separiere sie mit einem Komma: .search-field, .search-field2"], "Migrate Settings": ["Migrationseinstellungen"], "Migrate all of your settings to Simply Static 3.0": ["Migriere alle Einstellungen zu Simply Static 3.0"], "Migrate settings": ["Einstellungen migrieren"], "Settings migration successfully.": ["Migration erfolgreich abgeschlossen."], "Export & Import settings": ["Export & Import von Einstellungen"], "Export Settings": ["Export-Einstellungen"], "Copied!": ["Kopiert!"], "Copy export data": ["Ko<PERSON>re Export-Daten"], "Paste in the JSON string you got from your export to import all settings for the plugin.": ["Füge den JSON-String aus dem Export hier ein und importiere deine Einstellungen."], "Settings imported successfully.": ["Einstellungen erfolgreich importiert."], "Reset": ["Z<PERSON>ücksetzen"], "By clicking the \"Reset Plugin Settings\", you will reset all plugin settings. This can be useful if you want to import a new set of settings or you want a fresh start.": ["Mit einem Klick auf \"Plugin-Einstellungen zurücksetzen\" werden alle Plugin-Einstellungen zurückgesetzt. Dies kann nützlich sein, wenn Sie einen neuen Satz Einstellungen importieren oder einen Neustart durchführen möchten."], "If you click the \"Reset Database Table\" button instead, you will keep all your settings, and we will only recreate our DB table.": ["Wenn Sie stattdessen auf die Schaltfläche „Datenbanktabelle zurücksetzen“ klicken, bleiben alle Ihre Einstellungen erhalten und wir erstellen nur unsere DB-Tabelle neu."], "Reset Plugin Settings": ["Plugin-Einstellungen zurücksetzen"], "Reset Database Table": ["Datenbanktabelle zurücksetzen"], "Settings resetted successfully.": ["Einstellungen erfolgreich zurückgesetzt."], "Database table resetted successfully.": ["Datenbank-Tabelle erfolgreich zurückgesetzt."], "Simply Static": ["Simply Static"], "Export static page": ["Statische Seite exportieren"], "Export posts and pages directly with ": ["Exportieren Sie Beiträge und Seiten direkt mit "], "Generate": ["<PERSON><PERSON><PERSON>"], "Done! Finished in %s": ["Fertig! Fertigstellt in %s"], "An exception occurred: %s": ["Eine Ausnahme ist eingetreten: %s"], "An error occurred: %s": ["Ein Fehler ist aufgetreten: %s"], "Error: %s": ["Fehler: %s"], "PHP Version": ["PHP Version"], "php-xml": ["php-xml"], "cURL": ["cURL"], "Permalinks": ["Permalinks"], "Indexable": ["Indexierbar"], "Caching": ["Caching"], "WP-CRON": ["WP-CRON"], "Temp dir readable": ["Temporäres Verzeichnis lesbar"], "Temp dir writeable": ["Temporäres Verzeichnis beschreibbar"], "DELETE": ["DELETE"], "INSERT": ["INSERT"], "SELECT": ["SELECT"], "CREATE": ["CREATE"], "ALTER": ["ALTER"], "DROP": ["DROP"], "Destination URL": ["Ziel-URL"], "Local Dir": ["Lokales Verzeichnis"], "No incompatible plugins are active on your website!": ["Auf Ihrer Website sind keine inkompatiblen Plugins aktiv!"], "%d incompatible plugins are active": ["%d inkompatible Plugins sind aktiv"], "Destination URL %s is valid": ["Ziel-URL %s ist valide"], "Destination URL %s is not valid": ["Ziel-URL %s ist nicht valide"], "Is a valid URL.": ["Ist eine valide URL."], "Is not a valid URL.": ["Ist keine valide URL."], "Not a valid path": ["<PERSON><PERSON> valider Pfad"], "Not readable": ["<PERSON><PERSON>"], "Additional File/Dir %s is valid": ["Zusätzliche Datei/Verzeichnis %s ist valide"], "WordPress permalink structure is set": ["WordPress Permalinks sind gesetzt"], "WordPress permalink structure is not set": ["WordPress Permalinks sind nicht gesetzt"], "Discourage search engines from indexing this site is disabled": ["Such<PERSON><PERSON><PERSON> davon a<PERSON>, diese Site zu indizieren, ist deaktiviert"], "Discourage search engines from indexing this site is enabled": ["Suchmas<PERSON><PERSON> davon abhalten, diese Site zu indizieren ist aktiviert"], "WordPress cron is available and running": ["WP-Cron ist verfügbar und läuft"], "WordPress cron is not available and not running": ["<PERSON><PERSON> gibt ein Problem mit WP-Cron"], "Caching is disabled, great!": ["Caching ist deaktiviert, super!"], "Please disable caching before running a static export": ["Bitte deaktivieren Sie das Caching, bevor Sie einen statischen Export ausführen"], "Please disable caching (%s) before running a static export.": ["Bitte deaktivieren Sie das Caching (%s), bevor <PERSON> einen statischen Export ausführen."], "%s is not compatible with Simply Static.": ["%s ist nicht mit Simply Static kompatibel."], "Web server can read from Temp Files Directory: %s": ["Webserver Dateien aus dem temporären Verzeichnis %s können gelesen werden"], "Web server can't read from Temp Files Directory: %s": ["Webserver Dateien aus dem temporären Verzeichnis %s können nicht gelesen werden"], "Web server can write to Temp Files Directory: %s": ["Webserver kann temporäre <PERSON> in %s erstellen"], "Web server can't write to Temp Files Directory: %s": ["Webserver kann keine tempor<PERSON><PERSON> in %s erstellen"], "Web server can write to Local Directory: %s": ["Webserver kann ins lokale Verzeichnis %s schreiben"], "Web server can not write to Local Directory: %s": ["Webserver kann nicht ins lokale Verzeichnis %s schreiben"], "MySQL user has DELETE privilege": ["Prüfe ob der MySQL-Benutzer DELETE Rechte besitzt"], "MySQL user has no DELETE privilege": ["Prüfe ob der MySQL-Benutzer DELETE Rechte besitzt"], "MySQL user has INSERT privilege": ["Prüfe ob der MySQL-Benutzer NSERT Rechte besitzt"], "MySQL user has no INSERT privilege": ["Prüfe ob der MySQL-Benutzer INSERT Rechte besitzt"], "MySQL user has SELECT privilege": ["Prüfe ob der MySQL-Benutzer SELECT Rechte besitzt"], "MySQL user has no SELECT privilege": ["Prüfe ob der MySQL-Benutzer SELECT Rechte besitzt"], "MySQL user has CREATE privilege": ["Prüfe ob der MySQL-Benutzer CREATE Rechte besitzt"], "MySQL user has no CREATE privilege": ["Prüfe ob der MySQL-Benutzer CREATE Rechte besitzt"], "MySQL user has ALTER privilege": ["Prüfe ob der MySQL-Benutzer ALTER Rechte besitzt"], "MySQL user has no ALTER privilege": ["Prüfe ob der MySQL-Benutzer ALTER Rechte besitzt"], "MySQL user has DROP privilege": ["Prüfe ob der MySQL-Benutzer DROP Rechte besitzt"], "MySQL user has no DROP privilege": ["Prüfe ob der MySQL-Benutzer DROP Rechte besitzt"], "PHP version is >= %s": ["PHP Version >= %s"], "PHP version < %s": ["PHP Version < %s"], "php-xml is available": ["php-xml ist als Paket verfügbar"], "php-xml is not available": ["php-xml ist nicht als Paket verfügbar"], "cURL is available": ["cURL ist verfügbar"], "cURL version < %s": ["cURL Version < %s"], "Basic Auth is not enabled.": ["Basic Auth ist nicht aktiviert."], "Basic Auth is enabled, but no username or password is set in Simply Static -> Settings -> Debug -> Basic Auth": ["Basic Auth ist aktiviert, aber kein Benutzername oder Passwort ist in Simply Static -> Einstellungen -> Debug -> Basic Auth festgelegt"], "Basic Auth is enabled, and username and password are set in Simply Static -> Settings -> Debug -> Basic Auth": ["Basic Auth ist aktiviert und Benutzername und Passwort sind in Simply Static -> Einstellungen -> Debug -> Basic Auth festgelegt"], "Not a valid url.": ["Keine valide URL."], "Received a %s response. This might indicate a problem.": ["%s Antwort erhalten. Das weist auf ein Problem hin."], "Received a %s response.": ["Es wurde eine %s Antwort erhalten."], "Site link of %s": ["Seiten-Link von %s"], "Visit site": ["Seite an<PERSON>hen"], "(opens in a new tab)": ["(<PERSON><PERSON><PERSON> sich in einem neuen Tab)"], "Simply Static Compatible": ["Simply Static kompatibel"], "Missing Basic Auth credentials - you need to configure the Basic Auth credentials in Simply Static -> Settings -> Misc -> Basic Auth to continue the export.": ["Fehlende Basic-Auth-Anmeldeinformationen – Sie müssen die Basic-Auth-Anmeldeinformationen in Simply Static -> Einstellungen -> Sonstiges -> Basic Auth konfigurieren, um den Export fortzusetzen."], "Found on %s": ["Gefunden auf %s"], "Docs": ["Dokumentation"], "Attempted to fetch a remote URL": ["Versuche die URL abzurufen: %s"], "Can't find view template: %s": ["Konnte &#8222;Template Ansicht&#8220; nicht finden: %s"], "Can't find view layout: %s": ["Konnte &#8222;Layout Ansicht&#8220; nicht finden: %s"], "XML Sitemap %1$s %2$s": ["XML Sitemap %1$s %2$s"], "Locations Sitemap %1$s %2$s": ["Standort Sitemap %1$s %2$s"], "All in One SEO": ["All in One SEO"], "Adds sitemaps to generated static files.": ["<PERSON><PERSON><PERSON> den generierten statischen Dateien Sitemaps hinzu."], "Sitemap URL": ["Sitemap URL"], "Brizy": ["Brizy"], "Makes sure images optimized by Brizy are exported as well.": ["<PERSON><PERSON><PERSON> sicher, dass auch von Brizy optimierte Bilder exportiert werden."], "CookieYes | GDPR Cookie Consent": ["CookieYes | GDPR Cookie Consent"], "Fixes scripts given by CookieYes to work on exported pages.": ["<PERSON><PERSON><PERSON><PERSON> von CookieYes bereitgestellte Skripte, damit diese auf exportierten Seiten funktionieren."], "Elementor": ["<PERSON><PERSON><PERSON>"], "Exports assets required for Elementor widgets and prepares data used by them.": ["Exportiert für Elementor-Widgets erforderliche Assets und bereitet die von ihnen verwendeten Daten vor."], "Elementor Asset": ["<PERSON><PERSON><PERSON>"], "Elementor Pro": ["Elementor Pro"], "Exports assets required for Elementor Pro widgets and prepares data used by them.": ["Exportiert für Elementor Pro-Widgets erforderliche Assets und bereitet die von ihnen verwendeten Daten vor."], "Elementor Pro Asset": ["Elementor Pro Asset"], "Elementor Pro Lottie": ["Elementor Pro Lottie"], "Jetpack": ["Jetpack"], "Adds scripts for carousels and sliders to the static site.": ["Fügt der statischen Site Skripte für Karussells und Schieberegler hinzu."], "Jetpack Integration": ["Jetpack Integration"], "Rank Math": ["Rank Math"], "Automatically includes your XML sitemaps, handles URL replacements in schema.org markup, and creates redirects on your static site for you.": ["Schl<PERSON>ßt automatisch Ihre XML-Sitemaps ein, übernimmt URL-Ersetzungen im Schema.org-Markup und erstellt für Sie Weiterleitungen auf Ihrer statischen Site."], "RankMath Redirection URL": ["RankMath-Umleitungs-URL"], "SEOPress": ["SEOPress"], "Admin Bar (Core)": ["<PERSON>min <PERSON> (Core)"], "Adds an admin bar integration for Simply Static to see the current status of static exports.": ["<PERSON>ügt eine Adminleisten-Integration für Simply Static hinzu, um den aktuellen Status statischer Exporte anzuzeigen."], "Static Generation: Waiting..": ["Statische Generierung: Warten..."], "Static Generation:": ["Statische Generierung:"], "Running..": ["Läuft.."], "Idle": ["<PERSON><PERSON><PERSON><PERSON>"], "Error": ["<PERSON><PERSON>"], "Yoast": ["Yoast"], "Yoast Redirection URL": ["Yoast-Umleitungs-URL"], "Complianz | GDPR/CCPA Cookie Consent": ["Complianz | GDPR/CCPA Cookie Consent"], "Integrates Complianz Cookie banner to work on the static site.": ["Integriert das Complianz-Cookie-Banner, damit es auf der statischen Site funktioniert."], "Environments (Core)": ["Umgebungen (Core)"], "Define multiple environments of Simply Static so you can easily change between saved configurations.": ["Definieren Sie mehrere Umgebungen von Simply Static, damit <PERSON><PERSON>los z<PERSON>schen gespeicherten Konfigurationen wechseln können."], "Github": ["<PERSON><PERSON><PERSON>"], "Used when deploying the exported sites to Github": ["Wird beim Bereitstellen der exportierten Sites auf Github verwendet"], "WPML - Multilingual": ["WPML - Multilingual"], "Integrates WPML to work with exported sites.": ["Integriert WPML, um mit exportierten Sites zu arbeiten."], "Redirection": ["Redirection"], "Integrates redirections from the \"Redirection\" Plugin automatically on each export.": ["Integriert Weiterleitungen vom Plugin „Redirection“ automatisch bei jedem Export."], "Shortpixel": ["Shortpixel"], "Optimizes Images before exporting them for static sites.": ["Optimiert Bilder, bevor sie für statische Sites exportiert werden."], "Cancelling job": ["Ausführung wird abgebrochen"], "ZIP archive created: ": ["ZIP Archiv erstellt: "], "Click here to download": ["<PERSON>licke hier zum Download"], "Unable to create ZIP archive": ["ZIP Archiv konnte nicht erstellt werden"], "Do not save or follow": ["Nicht speichern oder folgen"], "Fetched %d of %d pages/files": ["%d von %d <PERSON><PERSON><PERSON>/<PERSON><PERSON> abger<PERSON>en"], "Do not follow": ["<PERSON>cht folgen"], "Do not save": ["<PERSON>cht speichern"], "Generating 404 Page.": ["Generiere 404-Fehlerseite."], "404 Page generated": ["404 Fehlerseite generiert"], "Setting up": ["Einstellen"], "Cannot create archive directory %s": ["Konnte die Datei bzw. das Verzeichnis nicht erstellen: %s"], "Additional URL": ["Zusätzliche URL"], "Additional File": ["Zusätzliche Datei"], "Additional Dir": ["Zusätzliches Verzeichnis"], "Destination URL:": ["Ziel-URL:"], "No new/updated pages to transfer": ["<PERSON><PERSON> neuen/aktualisierten Seiten zum Übertragen"], "Transferred %d of %d files": ["%d von %d <PERSON><PERSON> k<PERSON>t"], "Wrapping up": ["Fertigstellung"], "Uploaded %d of %d files": ["%d von %d <PERSON><PERSON><PERSON>/Dateien aktualisiert"], "Every Minute": ["<PERSON><PERSON>"], "Every %d Minutes": ["Alle %d <PERSON>uten"], "Redirecting...": ["Weiterleiten.."], "You are being redirected to %s": ["Du wirst weitergeleitet auf %s"]}}}