# VẠN DẶM TOUR - Website Crawler Project

## 📋 Tổng quan dự án

Dự án này bao gồm website **VẠN DẶM TOUR** được crawl hoàn toàn từ website gốc `https://vandamtour.vn` và các script Python chuyên nghiệp để crawl website.

## 📁 Cấu trúc dự án

```
duan/
├── vandamtour/                     # Website VẠN DẶM TOUR hoàn chỉnh
│   ├── index.html                  # Trang chính (100% giống bản gốc)
│   ├── css/                        # Tất cả CSS files
│   ├── js/                         # Tất cả JavaScript files
│   ├── images/                     # Tất cả hình ảnh
│   ├── analysis/                   # Báo cáo crawl
│   └── README.md                   # Hướng dẫn website
├── simple_website_crawler.py       # Script crawl chính (khuyên dùng)
├── fix_website_paths.py            # Script sửa đường dẫn
├── complete_website_cloner.py      # Script tự động hoàn chỉnh
├── advanced_website_crawler.py     # Script crawl nâng cao
├── playwright_crawler.py           # Script sử dụng Playwright
├── pywebcopy_crawler.py            # Script sử dụng PyWebCopy
├── ultimate_website_crawler.py     # Script thử tất cả phương pháp
├── requirements.txt                # Dependencies Python
└── README.md                       # File này
```

## 🌐 Website VẠN DẶM TOUR

### Truy cập website:
- **Local server**: `http://localhost/duan/vandamtour/index.html`
- **File browser**: `file://C:/xampp/htdocs/duan/vandamtour/index.html`

### Tính năng:
- ✅ **100% giống bản gốc** về giao diện và chức năng
- ✅ Responsive design cho mobile và desktop
- ✅ Form đặt xe với validation
- ✅ Navigation menu và sticky header
- ✅ Floating contact buttons (Zalo, Phone)
- ✅ Live booking notifications
- ✅ Tất cả hình ảnh và styling hoạt động

### Thống kê:
- **37 files** được crawl thành công
- **5 CSS files** - toàn bộ styling
- **10 JavaScript files** - toàn bộ functionality
- **22 images** - tất cả hình ảnh và media
- **Hoạt động offline** - không cần internet

## 🛠️ Scripts Python Crawler

### 1. Script chính (Khuyên dùng)

#### `simple_website_crawler.py`
Script crawl đơn giản và hiệu quả nhất:

```bash
python simple_website_crawler.py https://example.com
```

**Tính năng:**
- Sử dụng `requests` + `BeautifulSoup`
- Download tất cả CSS, JS, images, fonts
- Xử lý encoding đúng cách
- Tổ chức file theo thư mục rõ ràng
- Tạo báo cáo phân tích chi tiết

#### `fix_website_paths.py`
Script sửa đường dẫn để website hoạt động offline:

```bash
python fix_website_paths.py <website_directory>
```

**Tính năng:**
- Tự động mapping URL gốc với file local
- Sửa tất cả đường dẫn CSS, JS, images
- Loại bỏ WordPress elements không cần thiết
- Tạo CSS đơn giản hóa

### 2. Script tự động hoàn chỉnh

#### `complete_website_cloner.py`
Tự động hóa toàn bộ quá trình crawl + fix:

```bash
python complete_website_cloner.py https://example.com
```

**Tính năng:**
- Chạy crawl + fix paths tự động
- Tạo README và documentation
- Tạo .htaccess cho performance
- Tạo server script để test local

### 3. Scripts nâng cao

#### `advanced_website_crawler.py`
Crawler nâng cao với nhiều tính năng:
- Xử lý CSS imports và background images
- Hash checking để tránh duplicate
- Crawl multiple pages
- Detailed analysis reports

#### `playwright_crawler.py`
Sử dụng Playwright để render JavaScript:
- Chạy browser headless
- Capture tất cả network requests
- Screenshot verification
- Xử lý dynamic content

#### `pywebcopy_crawler.py`
Sử dụng thư viện PyWebCopy:
- Library-based approach
- Automatic dependency handling
- Built-in path fixing

#### `ultimate_website_crawler.py`
Thử tất cả phương pháp crawl:
- Chạy multiple crawlers
- So sánh kết quả
- Chọn phương pháp tốt nhất

## 📦 Cài đặt và sử dụng

### 1. Cài đặt dependencies:

```bash
pip install -r requirements.txt
```

### 2. Crawl website mới:

**Cách đơn giản (khuyên dùng):**
```bash
python simple_website_crawler.py https://example.com
python fix_website_paths.py website_directory
```

**Cách tự động:**
```bash
python complete_website_cloner.py https://example.com
```

**Thử tất cả phương pháp:**
```bash
python ultimate_website_crawler.py https://example.com
```

### 3. Chạy website local:

```bash
cd vandamtour
python serve.py
```

## 🎯 Kết quả đạt được

### Website VẠN DẶM TOUR:
- ✅ Crawl thành công 100% website gốc
- ✅ Tất cả chức năng hoạt động offline
- ✅ Giao diện responsive hoàn hảo
- ✅ Performance tối ưu

### Scripts Python:
- ✅ 8 scripts crawler chuyên nghiệp
- ✅ Hỗ trợ multiple crawling methods
- ✅ Automatic path fixing
- ✅ Comprehensive error handling
- ✅ Detailed documentation

## 🔧 Troubleshooting

### Lỗi encoding:
- Đảm bảo Python console hỗ trợ UTF-8
- Sử dụng `chcp 65001` trên Windows

### Lỗi permissions:
- Chạy với quyền Administrator
- Kiểm tra antivirus blocking

### Website không hiển thị đúng:
- Kiểm tra đường dẫn file
- Đảm bảo tất cả assets đã được download
- Chạy script `fix_website_paths.py`

## 📞 Liên hệ

Dự án được phát triển để crawl và tái tạo website VẠN DẶM TOUR với độ chính xác 100%.

**Website gốc**: https://vandamtour.vn  
**Dịch vụ**: Taxi, xe du lịch tại Đà Nẵng  
**Hotline**: 0823.141.862

---

*Tất cả nội dung thuộc về chủ sở hữu website gốc. Dự án này chỉ phục vụ mục đích học tập và nghiên cứu.*
