#!/usr/bin/env python3
"""
CSS Optimizer
<PERSON><PERSON><PERSON> tất cả CSS thành một file duy nhất và tối ưu hóa
"""

import os
import re
from pathlib import Path

class CSSOptimizer:
    def __init__(self, website_dir):
        self.website_dir = Path(website_dir)
        self.css_dir = self.website_dir / 'css'
        self.optimized_css = ""
        
    def read_css_file(self, css_file):
        """Read CSS file content"""
        try:
            with open(css_file, 'r', encoding='utf-8') as f:
                content = f.read()
            return content
        except Exception as e:
            print(f"Error reading {css_file}: {e}")
            return ""
    
    def minify_css(self, css_content):
        """Basic CSS minification"""
        # Remove comments
        css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
        
        # Remove extra whitespace
        css_content = re.sub(r'\s+', ' ', css_content)
        
        # Remove spaces around certain characters
        css_content = re.sub(r'\s*([{}:;,>+~])\s*', r'\1', css_content)
        
        # Remove trailing semicolons before }
        css_content = re.sub(r';\s*}', '}', css_content)
        
        return css_content.strip()
    
    def combine_css_files(self):
        """Combine all CSS files into one"""
        print("Combining CSS files...")
        
        # Priority order for CSS files
        priority_files = [
            'wp-content_uploads_font-awesome_v5.15.4_css_svg-with-js.css',
            'wp-content_themes_flatsome_assets_css_flatsome.css',
            'wp-content_themes_flatsome-child_style.css',
            'extracted-inline.css'
        ]
        
        combined_css = "/* VẠN DẶM TOUR - Combined CSS */\n\n"
        
        # Add priority files first
        for filename in priority_files:
            css_file = self.css_dir / filename
            if css_file.exists():
                print(f"Adding: {filename}")
                content = self.read_css_file(css_file)
                combined_css += f"/* === {filename} === */\n"
                combined_css += content + "\n\n"
        
        # Add remaining CSS files
        for css_file in self.css_dir.glob('*.css'):
            if css_file.name not in priority_files and css_file.name != 'combined.css':
                print(f"Adding: {css_file.name}")
                content = self.read_css_file(css_file)
                combined_css += f"/* === {css_file.name} === */\n"
                combined_css += content + "\n\n"
        
        return combined_css
    
    def add_custom_optimizations(self, css_content):
        """Add custom CSS optimizations"""
        
        custom_css = """
/* === CUSTOM OPTIMIZATIONS === */

/* Performance optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
    loading: lazy;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Improved button styles */
.button, .btn {
    transition: all 0.3s ease;
    cursor: pointer;
}

.button:hover, .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Loading animation */
.loading {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .hide-for-mobile {
        display: none !important;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
}

/* Component placeholders styling */
[id*="placeholder"] {
    min-height: 20px;
    background: #f9f9f9;
    border: 1px dashed #ddd;
    padding: 10px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Contact buttons improvements */
.button-contact {
    z-index: 9999;
    position: fixed;
}

/* Form improvements */
.wpcf7-form input,
.wpcf7-form textarea,
.wpcf7-form select {
    border-radius: 4px;
    border: 1px solid #ddd;
    padding: 10px;
    transition: border-color 0.3s ease;
}

.wpcf7-form input:focus,
.wpcf7-form textarea:focus,
.wpcf7-form select:focus {
    border-color: #007cba;
    outline: none;
    box-shadow: 0 0 5px rgba(0,124,186,0.3);
}

"""
        
        return css_content + custom_css
    
    def create_optimized_css(self):
        """Create optimized CSS file"""
        print("Creating optimized CSS...")
        
        # Combine all CSS
        combined_css = self.combine_css_files()
        
        # Add custom optimizations
        optimized_css = self.add_custom_optimizations(combined_css)
        
        # Save combined CSS
        combined_file = self.css_dir / 'combined.css'
        with open(combined_file, 'w', encoding='utf-8') as f:
            f.write(optimized_css)
        
        print(f"Combined CSS saved: {combined_file}")
        
        # Create minified version
        minified_css = self.minify_css(optimized_css)
        minified_file = self.css_dir / 'combined.min.css'
        with open(minified_file, 'w', encoding='utf-8') as f:
            f.write(minified_css)
        
        print(f"Minified CSS saved: {minified_file}")
        
        # Show file sizes
        original_size = len(optimized_css)
        minified_size = len(minified_css)
        savings = original_size - minified_size
        
        print(f"\nCSS Optimization Results:")
        print(f"Original size: {original_size:,} bytes")
        print(f"Minified size: {minified_size:,} bytes")
        print(f"Savings: {savings:,} bytes ({savings/original_size*100:.1f}%)")
        
        return combined_file, minified_file
    
    def update_html_file(self, use_minified=True):
        """Update HTML file to use combined CSS"""
        print("Updating HTML file...")
        
        html_file = self.website_dir / 'index-optimized.html'
        if not html_file.exists():
            html_file = self.website_dir / 'index.html'
        
        # Read HTML content
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove existing CSS links (except external ones)
        content = re.sub(r'<link[^>]*href="css/[^"]*"[^>]*>', '', content)
        
        # Add combined CSS link
        css_filename = 'combined.min.css' if use_minified else 'combined.css'
        css_link = f'<link rel="stylesheet" href="css/{css_filename}" type="text/css">'
        
        # Insert CSS link in head
        content = content.replace('</head>', f'  {css_link}\n</head>')
        
        # Save updated HTML
        updated_file = self.website_dir / 'index-final.html'
        with open(updated_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Updated HTML saved: {updated_file}")
        return updated_file

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python optimize_css.py <website_directory>")
        print("Example: python optimize_css.py vandamtour")
        sys.exit(1)
    
    website_dir = sys.argv[1]
    
    if not os.path.exists(website_dir):
        print(f"Directory not found: {website_dir}")
        sys.exit(1)
    
    # Create optimizer
    optimizer = CSSOptimizer(website_dir)
    
    try:
        # Create optimized CSS
        combined_file, minified_file = optimizer.create_optimized_css()
        
        # Update HTML file
        updated_html = optimizer.update_html_file(use_minified=True)
        
        print(f"\n✅ CSS optimization completed!")
        print(f"📁 Combined CSS: {combined_file}")
        print(f"📁 Minified CSS: {minified_file}")
        print(f"📁 Updated HTML: {updated_html}")
        print(f"🌐 Open: file://{updated_html.absolute()}")
        
    except Exception as e:
        print(f"❌ CSS optimization failed: {e}")

if __name__ == "__main__":
    main()
