(globalThis.webpackChunksimplystatic_settings=globalThis.webpackChunksimplystatic_settings||[]).push([[353],{734:(e,t,a)=>{var r,i=Object.create,l=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,o=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,c=(e,t,a,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of s(t))p.call(e,i)||i===a||l(e,i,{get:()=>t[i],enumerable:!(r=n(t,i))||r.enumerable});return e},h=(e,t,a)=>(((e,t,a)=>{t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a})(e,"symbol"!=typeof t?t+"":t,a),a),u={};((e,t)=>{for(var a in t)l(e,a,{get:t[a],enumerable:!0})})(u,{default:()=>b}),e.exports=(r=u,c(l({},"__esModule",{value:!0}),r));var d=((e,t,a)=>(a=null!=e?i(o(e)):{},c(e&&e.__esModule?a:l(a,"default",{value:e,enumerable:!0}),e)))(a(609));const m="64px",g={};class b extends d.Component{constructor(){super(...arguments),h(this,"mounted",!1),h(this,"state",{image:null}),h(this,"handleKeyPress",(e=>{"Enter"!==e.key&&" "!==e.key||this.props.onClick()}))}componentDidMount(){this.mounted=!0,this.fetchImage(this.props)}componentDidUpdate(e){const{url:t,light:a}=this.props;e.url===t&&e.light===a||this.fetchImage(this.props)}componentWillUnmount(){this.mounted=!1}fetchImage({url:e,light:t,oEmbedUrl:a}){if(!d.default.isValidElement(t))if("string"!=typeof t){if(!g[e])return this.setState({image:null}),window.fetch(a.replace("{url}",e)).then((e=>e.json())).then((t=>{if(t.thumbnail_url&&this.mounted){const a=t.thumbnail_url.replace("height=100","height=480").replace("-d_295x166","-d_640");this.setState({image:a}),g[e]=a}}));this.setState({image:g[e]})}else this.setState({image:t})}render(){const{light:e,onClick:t,playIcon:a,previewTabIndex:r,previewAriaLabel:i}=this.props,{image:l}=this.state,n=d.default.isValidElement(e),s={display:"flex",alignItems:"center",justifyContent:"center"},o={preview:{width:"100%",height:"100%",backgroundImage:l&&!n?`url(${l})`:void 0,backgroundSize:"cover",backgroundPosition:"center",cursor:"pointer",...s},shadow:{background:"radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)",borderRadius:m,width:m,height:m,position:n?"absolute":void 0,...s},playIcon:{borderStyle:"solid",borderWidth:"16px 0 16px 26px",borderColor:"transparent transparent transparent white",marginLeft:"7px"}},p=d.default.createElement("div",{style:o.shadow,className:"react-player__shadow"},d.default.createElement("div",{style:o.playIcon,className:"react-player__play-icon"}));return d.default.createElement("div",{style:o.preview,className:"react-player__preview",onClick:t,tabIndex:r,onKeyPress:this.handleKeyPress,...i?{"aria-label":i}:{}},n?e:null,a||p)}}}}]);