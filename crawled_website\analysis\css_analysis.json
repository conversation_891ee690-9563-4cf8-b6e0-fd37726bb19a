{"svg-with-js.css": {"url": "https://vandamtour.vn/wp-content/uploads/font-awesome/v5.15.4/css/svg-with-js.css", "selectors": [{"selector": "/*!\n * Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\n */\n.svg-inline--fa,svg:not(:root).svg-inline--fa", "properties": 0}, {"selector": ".svg-inline--fa", "properties": 0}, {"selector": ".svg-inline--fa.fa-lg", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-1", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-2", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-3", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-4", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-5", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-6", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-7", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-8", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-9", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-10", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-11", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-12", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-13", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-14", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-15", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-16", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-17", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-18", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-19", "properties": 0}, {"selector": ".svg-inline--fa.fa-w-20", "properties": 0}, {"selector": ".svg-inline--fa.fa-pull-left", "properties": 0}, {"selector": ".svg-inline--fa.fa-pull-right", "properties": 0}, {"selector": ".svg-inline--fa.fa-border", "properties": 0}, {"selector": ".svg-inline--fa.fa-li", "properties": 0}, {"selector": ".svg-inline--fa.fa-fw", "properties": 0}, {"selector": ".fa-layers svg.svg-inline--fa", "properties": 0}, {"selector": ".fa-layers", "properties": 0}, {"selector": ".fa-layers svg.svg-inline--fa", "properties": 0}, {"selector": ".fa-layers-counter,.fa-layers-text", "properties": 0}, {"selector": ".fa-layers-text", "properties": 0}, {"selector": ".fa-layers-counter", "properties": 0}, {"selector": ".fa-layers-bottom-right", "properties": 0}, {"selector": ".fa-layers-bottom-left", "properties": 0}, {"selector": ".fa-layers-top-right", "properties": 0}, {"selector": ".fa-layers-top-left", "properties": 0}, {"selector": ".fa-lg", "properties": 0}, {"selector": ".fa-xs", "properties": 0}, {"selector": ".fa-sm", "properties": 0}, {"selector": ".fa-1x", "properties": 0}, {"selector": ".fa-2x", "properties": 0}, {"selector": ".fa-3x", "properties": 0}, {"selector": ".fa-4x", "properties": 0}, {"selector": ".fa-5x", "properties": 0}, {"selector": ".fa-6x", "properties": 0}, {"selector": ".fa-7x", "properties": 0}, {"selector": ".fa-8x", "properties": 0}, {"selector": ".fa-9x", "properties": 0}, {"selector": ".fa-10x", "properties": 0}, {"selector": ".fa-fw", "properties": 0}, {"selector": ".fa-ul", "properties": 0}, {"selector": ".fa-ul>li", "properties": 0}, {"selector": ".fa-li", "properties": 0}, {"selector": ".fa-border", "properties": 0}, {"selector": ".fa-pull-left", "properties": 0}, {"selector": ".fa-pull-right", "properties": 0}, {"selector": ".fa.fa-pull-left,.fab.fa-pull-left,.fal.fa-pull-left,.far.fa-pull-left,.fas.fa-pull-left", "properties": 0}, {"selector": ".fa.fa-pull-right,.fab.fa-pull-right,.fal.fa-pull-right,.far.fa-pull-right,.fas.fa-pull-right", "properties": 0}, {"selector": ".fa-spin", "properties": 0}, {"selector": ".fa-pulse", "properties": 0}, {"selector": "@-webkit-keyframes fa-spin", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": "@keyframes fa-spin", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": ".fa-rotate-90", "properties": 0}, {"selector": ".fa-rotate-180", "properties": 0}, {"selector": ".fa-rotate-270", "properties": 0}, {"selector": ".fa-flip-horizontal", "properties": 0}, {"selector": ".fa-flip-vertical", "properties": 0}, {"selector": ".fa-flip-both,.fa-flip-horizontal.fa-flip-vertical,.fa-flip-vertical", "properties": 0}, {"selector": ".fa-flip-both,.fa-flip-horizontal.fa-flip-vertical", "properties": 0}, {"selector": ":root .fa-flip-both,:root .fa-flip-horizontal,:root .fa-flip-vertical,:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270", "properties": 0}, {"selector": ".fa-stack", "properties": 0}, {"selector": ".fa-stack-1x,.fa-stack-2x", "properties": 0}, {"selector": ".svg-inline--fa.fa-stack-1x", "properties": 0}, {"selector": ".svg-inline--fa.fa-stack-2x", "properties": 0}, {"selector": ".fa-inverse", "properties": 0}, {"selector": ".sr-only", "properties": 0}, {"selector": ".sr-only-focusable:active,.sr-only-focusable:focus", "properties": 0}, {"selector": ".svg-inline--fa .fa-primary", "properties": 0}, {"selector": ".svg-inline--fa .fa-secondary", "properties": 0}, {"selector": ".svg-inline--fa .fa-secondary,.svg-inline--fa.fa-swap-opacity .fa-primary", "properties": 0}, {"selector": ".svg-inline--fa.fa-swap-opacity .fa-secondary", "properties": 0}, {"selector": ".svg-inline--fa mask .fa-primary,.svg-inline--fa mask .fa-secondary", "properties": 0}, {"selector": ".fad.fa-inverse", "properties": 0}], "media_queries": [], "imports": [], "fonts": [], "colors": ["#eee", "#fff", "#ff253a", "#000"], "size": 6359}, "styles.css": {"url": "https://vandamtour.vn/wp-content/plugins/contact-form-7/includes/css/styles.css?ver=6.1", "selectors": [{"selector": ".wpcf7 .screen-reader-response", "properties": 0}, {"selector": ".wpcf7 .hidden-fields-container", "properties": 0}, {"selector": ".wpcf7 form .wpcf7-response-output", "properties": 0}, {"selector": ".wpcf7 form.init .wpcf7-response-output,\n.wpcf7 form.resetting .wpcf7-response-output,\n.wpcf7 form.submitting .wpcf7-response-output", "properties": 0}, {"selector": ".wpcf7 form.sent .wpcf7-response-output", "properties": 0}, {"selector": ".wpcf7 form.failed .wpcf7-response-output,\n.wpcf7 form.aborted .wpcf7-response-output", "properties": 0}, {"selector": ".wpcf7 form.spam .wpcf7-response-output", "properties": 0}, {"selector": ".wpcf7 form.invalid .wpcf7-response-output,\n.wpcf7 form.unaccepted .wpcf7-response-output,\n.wpcf7 form.payment-required .wpcf7-response-output", "properties": 0}, {"selector": ".wpcf7-form-control-wrap", "properties": 0}, {"selector": ".wpcf7-not-valid-tip", "properties": 0}, {"selector": ".use-floating-validation-tip .wpcf7-not-valid-tip", "properties": 0}, {"selector": ".wpcf7-list-item", "properties": 0}, {"selector": ".wpcf7-list-item-label::before,\n.wpcf7-list-item-label::after", "properties": 0}, {"selector": ".wpcf7-spinner", "properties": 0}, {"selector": "form.submitting .wpcf7-spinner", "properties": 0}, {"selector": ".wpcf7-spinner::before", "properties": 0}, {"selector": "@media (prefers-reduced-motion: reduce)", "properties": 0}, {"selector": ".wpcf7-spinner::before", "properties": 0}, {"selector": "@keyframes spin", "properties": 0}, {"selector": "from", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": "@keyframes blink", "properties": 0}, {"selector": "from", "properties": 0}, {"selector": "50%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": ".wpcf7 [inert]", "properties": 0}, {"selector": ".wpcf7 input[type=\"file\"]", "properties": 0}, {"selector": ".wpcf7 input[type=\"file\"]:disabled", "properties": 0}, {"selector": ".wpcf7 .wpcf7-submit:disabled", "properties": 0}, {"selector": ".wpcf7 input[type=\"url\"],\n.wpcf7 input[type=\"email\"],\n.wpcf7 input[type=\"tel\"]", "properties": 0}, {"selector": ".wpcf7-reflection > output", "properties": 0}, {"selector": ".wpcf7-reflection > output[hidden]", "properties": 0}], "media_queries": [{"media": "(prefers-reduced-motion: reduce)", "rules": 0}], "imports": [], "fonts": [], "colors": ["#ffb900", "#46b450", "#dc3232", "#fbfbfc", "#23282d", "#f56e28", "#fff", "#00a0d2"], "size": 2947}, "style.css": {"url": "https://vandamtour.vn/wp-content/themes/flatsome-child/style.css?ver=3.0", "selectors": [{"selector": "/*\nTheme Name: Flatsome Child\nDescription: This is a child theme for Flatsome Theme\nAuthor: UX Themes\nTemplate: flatsome\nVersion: 3.0\n*/\n\n/*************** ADD CUSTOM CSS HERE.   ***************/\ndiv#top-bar", "properties": 0}, {"selector": "div#top-bar li.html.custom.html_topbar_right", "properties": 0}, {"selector": "div#top-bar li.html.custom.html_topbar_right a", "properties": 0}, {"selector": "div#top-bar a.hotline-top i", "properties": 0}, {"selector": "div#masthead", "properties": 0}, {"selector": "div#masthead ul li.menu-item a.nav-top-link", "properties": 0}, {"selector": "main#main", "properties": 0}, {"selector": "/* css hiệu ứng khách hàng đặt xe */\n.box_marquee_slider", "properties": 0}, {"selector": "ul.box_datxe", "properties": 0}, {"selector": ".box_datxe>li", "properties": 0}, {"selector": ".box_datxe li span, .box_datxe li b", "properties": 0}, {"selector": "@keyframes marquee", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "100%", "properties": 0}, {"selector": "/* css section dịch v<PERSON> */\n.title-dichvu h2", "properties": 0}, {"selector": ".box-dichvu", "properties": 0}, {"selector": ".box-dichvu:hover", "properties": 0}, {"selector": ".box-dichvu .icon-box-img", "properties": 0}, {"selector": ".box-dichvu p", "properties": 0}, {"selector": ".dichvu-gia>.col-inner", "properties": 0}, {"selector": ".dichvu-gia>.col-inner h4", "properties": 0}, {"selector": ".dichvu-gia>.col-inner ul", "properties": 0}, {"selector": ".dichvu-gia>.col-inner ul li", "properties": 0}, {"selector": "/* css section tour */\n.title-tour", "properties": 0}, {"selector": ".title-tour h3", "properties": 0}, {"selector": ".title-tour p", "properties": 0}, {"selector": ".box-tour", "properties": 0}, {"selector": ".box-tour .icon-box-img", "properties": 0}, {"selector": ".box-tour .icon-box-img img", "properties": 0}, {"selector": ".box-tour h4", "properties": 0}, {"selector": ".box-tour p", "properties": 0}, {"selector": ".box-tour p strong", "properties": 0}, {"selector": ".box-tour .icon-box-text", "properties": 0}, {"selector": ".box-tour .icon-box-text::-webkit-scrollbar", "properties": 0}, {"selector": ".box-tour .icon-box-text::-webkit-scrollbar-thumb", "properties": 0}, {"selector": "/* css form */\n.col.col-padding", "properties": 0}, {"selector": ".formdatxe", "properties": 0}, {"selector": ".formdatxe .col.col-padding", "properties": 0}, {"selector": ".formdatxe h4", "properties": 0}, {"selector": ".formdatxe input.wpcf7-form-control.wpcf7-text, .formdatxe select", "properties": 0}, {"selector": ".formdatxe i", "properties": 0}, {"selector": "i.fas.fa-street-view, i.fas.fa-phone-square-alt", "properties": 0}, {"selector": "i.fas.fa-user, i.fas.fa-map-marker-alt", "properties": 0}, {"selector": "i.fas.fa-car, i.fas.fa-calendar-alt", "properties": 0}, {"selector": ".formdatxe input.wpcf7-form-control.has-spinner.wpcf7-submit", "properties": 0}, {"selector": ".formdatxe .btn-datxe", "properties": 0}, {"selector": ".btn-click", "properties": 0}, {"selector": ".wpcf7 form.invalid .wpcf7-response-output , .wpcf7-response-output, .wpcf7 form.failed .wpcf7-response-output", "properties": 0}, {"selector": "/* css đội xe */\n.col.col-doixe", "properties": 0}, {"selector": ".col.col-doixe h2", "properties": 0}, {"selector": "/* css section đ<PERSON>h giá */\n.title-khachhang h2", "properties": 0}, {"selector": "/* css footer */\n.footer-widgets.footer.footer-1", "properties": 0}, {"selector": ".ct-foo p", "properties": 0}, {"selector": ".ct-foo ul li", "properties": 0}, {"selector": ".ct-foo h4", "properties": 0}, {"selector": ".ct-foo h4:after", "properties": 0}, {"selector": ".absolute-footer.dark.medium-text-center.small-text-center", "properties": 0}, {"selector": ".copyright-footer", "properties": 0}, {"selector": "@media (max-width: 549px)", "properties": 0}, {"selector": "/* \tcss col tour */\n\t.col.col-tour", "properties": 0}, {"selector": ".box-tour p", "properties": 0}, {"selector": "/* \tcss form */\n\tspan.wpcf7-list-item.first", "properties": 0}, {"selector": "input[type=checkbox]", "properties": 0}, {"selector": ".col.col-<PERSON><PERSON><PERSON>", "properties": 0}, {"selector": ".btn-click", "properties": 0}, {"selector": "@media only screen and (max-width: 48em)", "properties": 0}], "media_queries": [{"media": "(max-width: 549px)", "rules": 0}, {"media": "only screen and (max-width: 48em)", "rules": 0}], "imports": [], "fonts": [], "colors": ["#ffff", "#f7f9fb", "rgb(60 64 67 / 15%)", "#f5f5f5", "#e4ff00", "#12a9e2", "#0a5ec7", "#61c73f", "rgb(0 0 0)", "#ccc", "#292929", "#56c441", "rgb(0 0 0 / 50%)", "#29b94c", "#000000", "#5bc641", "#ff8a00", "#000", "#4fc243", "#737373", "#12a8e1", "#fff", "rgb(60 64 67 / 30%)"], "size": 6641}, "all.css": {"url": "https://use.fontawesome.com/releases/v5.15.4/css/all.css", "selectors": [{"selector": "/*!\n * Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\n */\n.fa,.fab,.fad,.fal,.far,.fas", "properties": 0}, {"selector": ".fa-lg", "properties": 0}, {"selector": ".fa-xs", "properties": 0}, {"selector": ".fa-sm", "properties": 0}, {"selector": ".fa-1x", "properties": 0}, {"selector": ".fa-2x", "properties": 0}, {"selector": ".fa-3x", "properties": 0}, {"selector": ".fa-4x", "properties": 0}, {"selector": ".fa-5x", "properties": 0}, {"selector": ".fa-6x", "properties": 0}, {"selector": ".fa-7x", "properties": 0}, {"selector": ".fa-8x", "properties": 0}, {"selector": ".fa-9x", "properties": 0}, {"selector": ".fa-10x", "properties": 0}, {"selector": ".fa-fw", "properties": 0}, {"selector": ".fa-ul", "properties": 0}, {"selector": ".fa-ul>li", "properties": 0}, {"selector": ".fa-li", "properties": 0}, {"selector": ".fa-border", "properties": 0}, {"selector": ".fa-pull-left", "properties": 0}, {"selector": ".fa-pull-right", "properties": 0}, {"selector": ".fa.fa-pull-left,.fab.fa-pull-left,.fal.fa-pull-left,.far.fa-pull-left,.fas.fa-pull-left", "properties": 0}, {"selector": ".fa.fa-pull-right,.fab.fa-pull-right,.fal.fa-pull-right,.far.fa-pull-right,.fas.fa-pull-right", "properties": 0}, {"selector": ".fa-spin", "properties": 0}, {"selector": ".fa-pulse", "properties": 0}, {"selector": "@-webkit-keyframes fa-spin", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": "@keyframes fa-spin", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": ".fa-rotate-90", "properties": 0}, {"selector": ".fa-rotate-180", "properties": 0}, {"selector": ".fa-rotate-270", "properties": 0}, {"selector": ".fa-flip-horizontal", "properties": 0}, {"selector": ".fa-flip-vertical", "properties": 0}, {"selector": ".fa-flip-both,.fa-flip-horizontal.fa-flip-vertical,.fa-flip-vertical", "properties": 0}, {"selector": ".fa-flip-both,.fa-flip-horizontal.fa-flip-vertical", "properties": 0}, {"selector": ":root .fa-flip-both,:root .fa-flip-horizontal,:root .fa-flip-vertical,:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270", "properties": 0}, {"selector": ".fa-stack", "properties": 0}, {"selector": ".fa-stack-1x,.fa-stack-2x", "properties": 0}, {"selector": ".fa-stack-1x", "properties": 0}, {"selector": ".fa-stack-2x", "properties": 0}, {"selector": ".fa-inverse", "properties": 0}, {"selector": ".fa-500px:before", "properties": 0}, {"selector": ".fa-accessible-icon:before", "properties": 0}, {"selector": ".fa-accusoft:before", "properties": 0}, {"selector": ".fa-acquisitions-incorporated:before", "properties": 0}, {"selector": ".fa-ad:before", "properties": 0}, {"selector": ".fa-address-book:before", "properties": 0}, {"selector": ".fa-address-card:before", "properties": 0}, {"selector": ".fa-adjust:before", "properties": 0}, {"selector": ".fa-adn:before", "properties": 0}, {"selector": ".fa-adversal:before", "properties": 0}, {"selector": ".fa-affiliatetheme:before", "properties": 0}, {"selector": ".fa-air-freshener:before", "properties": 0}, {"selector": ".fa-airbnb:before", "properties": 0}, {"selector": ".fa-algolia:before", "properties": 0}, {"selector": ".fa-align-center:before", "properties": 0}, {"selector": ".fa-align-justify:before", "properties": 0}, {"selector": ".fa-align-left:before", "properties": 0}, {"selector": ".fa-align-right:before", "properties": 0}, {"selector": ".fa-alipay:before", "properties": 0}, {"selector": ".fa-allergies:before", "properties": 0}, {"selector": ".fa-amazon:before", "properties": 0}, {"selector": ".fa-amazon-pay:before", "properties": 0}, {"selector": ".fa-ambulance:before", "properties": 0}, {"selector": ".fa-american-sign-language-interpreting:before", "properties": 0}, {"selector": ".fa-amilia:before", "properties": 0}, {"selector": ".fa-anchor:before", "properties": 0}, {"selector": ".fa-android:before", "properties": 0}, {"selector": ".fa-angellist:before", "properties": 0}, {"selector": ".fa-angle-double-down:before", "properties": 0}, {"selector": ".fa-angle-double-left:before", "properties": 0}, {"selector": ".fa-angle-double-right:before", "properties": 0}, {"selector": ".fa-angle-double-up:before", "properties": 0}, {"selector": ".fa-angle-down:before", "properties": 0}, {"selector": ".fa-angle-left:before", "properties": 0}, {"selector": ".fa-angle-right:before", "properties": 0}, {"selector": ".fa-angle-up:before", "properties": 0}, {"selector": ".fa-angry:before", "properties": 0}, {"selector": ".fa-angrycreative:before", "properties": 0}, {"selector": ".fa-angular:before", "properties": 0}, {"selector": ".fa-ankh:before", "properties": 0}, {"selector": ".fa-app-store:before", "properties": 0}, {"selector": ".fa-app-store-ios:before", "properties": 0}, {"selector": ".fa-apper:before", "properties": 0}, {"selector": ".fa-apple:before", "properties": 0}, {"selector": ".fa-apple-alt:before", "properties": 0}, {"selector": ".fa-apple-pay:before", "properties": 0}, {"selector": ".fa-archive:before", "properties": 0}, {"selector": ".fa-archway:before", "properties": 0}, {"selector": ".fa-arrow-alt-circle-down:before", "properties": 0}, {"selector": ".fa-arrow-alt-circle-left:before", "properties": 0}, {"selector": ".fa-arrow-alt-circle-right:before", "properties": 0}, {"selector": ".fa-arrow-alt-circle-up:before", "properties": 0}, {"selector": ".fa-arrow-circle-down:before", "properties": 0}, {"selector": ".fa-arrow-circle-left:before", "properties": 0}, {"selector": ".fa-arrow-circle-right:before", "properties": 0}, {"selector": ".fa-arrow-circle-up:before", "properties": 0}, {"selector": ".fa-arrow-down:before", "properties": 0}, {"selector": ".fa-arrow-left:before", "properties": 0}, {"selector": ".fa-arrow-right:before", "properties": 0}, {"selector": ".fa-arrow-up:before", "properties": 0}, {"selector": ".fa-arrows-alt:before", "properties": 0}, {"selector": ".fa-arrows-alt-h:before", "properties": 0}, {"selector": ".fa-arrows-alt-v:before", "properties": 0}, {"selector": ".fa-artstation:before", "properties": 0}, {"selector": ".fa-assistive-listening-systems:before", "properties": 0}, {"selector": ".fa-asterisk:before", "properties": 0}, {"selector": ".fa-asymmetrik:before", "properties": 0}, {"selector": ".fa-at:before", "properties": 0}, {"selector": ".fa-atlas:before", "properties": 0}, {"selector": ".fa-atlassian:before", "properties": 0}, {"selector": ".fa-atom:before", "properties": 0}, {"selector": ".fa-audible:before", "properties": 0}, {"selector": ".fa-audio-description:before", "properties": 0}, {"selector": ".fa-autoprefixer:before", "properties": 0}, {"selector": ".fa-avianex:before", "properties": 0}, {"selector": ".fa-aviato:before", "properties": 0}, {"selector": ".fa-award:before", "properties": 0}, {"selector": ".fa-aws:before", "properties": 0}, {"selector": ".fa-baby:before", "properties": 0}, {"selector": ".fa-baby-carriage:before", "properties": 0}, {"selector": ".fa-backspace:before", "properties": 0}, {"selector": ".fa-backward:before", "properties": 0}, {"selector": ".fa-bacon:before", "properties": 0}, {"selector": ".fa-bacteria:before", "properties": 0}, {"selector": ".fa-bacterium:before", "properties": 0}, {"selector": ".fa-bahai:before", "properties": 0}, {"selector": ".fa-balance-scale:before", "properties": 0}, {"selector": ".fa-balance-scale-left:before", "properties": 0}, {"selector": ".fa-balance-scale-right:before", "properties": 0}, {"selector": ".fa-ban:before", "properties": 0}, {"selector": ".fa-band-aid:before", "properties": 0}, {"selector": ".fa-bandcamp:before", "properties": 0}, {"selector": ".fa-barcode:before", "properties": 0}, {"selector": ".fa-bars:before", "properties": 0}, {"selector": ".fa-baseball-ball:before", "properties": 0}, {"selector": ".fa-basketball-ball:before", "properties": 0}, {"selector": ".fa-bath:before", "properties": 0}, {"selector": ".fa-battery-empty:before", "properties": 0}, {"selector": ".fa-battery-full:before", "properties": 0}, {"selector": ".fa-battery-half:before", "properties": 0}, {"selector": ".fa-battery-quarter:before", "properties": 0}, {"selector": ".fa-battery-three-quarters:before", "properties": 0}, {"selector": ".fa-battle-net:before", "properties": 0}, {"selector": ".fa-bed:before", "properties": 0}, {"selector": ".fa-beer:before", "properties": 0}, {"selector": ".fa-behance:before", "properties": 0}, {"selector": ".fa-behance-square:before", "properties": 0}, {"selector": ".fa-bell:before", "properties": 0}, {"selector": ".fa-bell-slash:before", "properties": 0}, {"selector": ".fa-bezier-curve:before", "properties": 0}, {"selector": ".fa-bible:before", "properties": 0}, {"selector": ".fa-bicycle:before", "properties": 0}, {"selector": ".fa-biking:before", "properties": 0}, {"selector": ".fa-bimobject:before", "properties": 0}, {"selector": ".fa-binoculars:before", "properties": 0}, {"selector": ".fa-biohazard:before", "properties": 0}, {"selector": ".fa-birthday-cake:before", "properties": 0}, {"selector": ".fa-bitbucket:before", "properties": 0}, {"selector": ".fa-bitcoin:before", "properties": 0}, {"selector": ".fa-bity:before", "properties": 0}, {"selector": ".fa-black-tie:before", "properties": 0}, {"selector": ".fa-blackberry:before", "properties": 0}, {"selector": ".fa-blender:before", "properties": 0}, {"selector": ".fa-blender-phone:before", "properties": 0}, {"selector": ".fa-blind:before", "properties": 0}, {"selector": ".fa-blog:before", "properties": 0}, {"selector": ".fa-blogger:before", "properties": 0}, {"selector": ".fa-blogger-b:before", "properties": 0}, {"selector": ".fa-bluetooth:before", "properties": 0}, {"selector": ".fa-bluetooth-b:before", "properties": 0}, {"selector": ".fa-bold:before", "properties": 0}, {"selector": ".fa-bolt:before", "properties": 0}, {"selector": ".fa-bomb:before", "properties": 0}, {"selector": ".fa-bone:before", "properties": 0}, {"selector": ".fa-bong:before", "properties": 0}, {"selector": ".fa-book:before", "properties": 0}, {"selector": ".fa-book-dead:before", "properties": 0}, {"selector": ".fa-book-medical:before", "properties": 0}, {"selector": ".fa-book-open:before", "properties": 0}, {"selector": ".fa-book-reader:before", "properties": 0}, {"selector": ".fa-bookmark:before", "properties": 0}, {"selector": ".fa-bootstrap:before", "properties": 0}, {"selector": ".fa-border-all:before", "properties": 0}, {"selector": ".fa-border-none:before", "properties": 0}, {"selector": ".fa-border-style:before", "properties": 0}, {"selector": ".fa-bowling-ball:before", "properties": 0}, {"selector": ".fa-box:before", "properties": 0}, {"selector": ".fa-box-open:before", "properties": 0}, {"selector": ".fa-box-tissue:before", "properties": 0}, {"selector": ".fa-boxes:before", "properties": 0}, {"selector": ".fa-braille:before", "properties": 0}, {"selector": ".fa-brain:before", "properties": 0}, {"selector": ".fa-bread-slice:before", "properties": 0}, {"selector": ".fa-briefcase:before", "properties": 0}, {"selector": ".fa-briefcase-medical:before", "properties": 0}, {"selector": ".fa-broadcast-tower:before", "properties": 0}, {"selector": ".fa-broom:before", "properties": 0}, {"selector": ".fa-brush:before", "properties": 0}, {"selector": ".fa-btc:before", "properties": 0}, {"selector": ".fa-buffer:before", "properties": 0}, {"selector": ".fa-bug:before", "properties": 0}, {"selector": ".fa-building:before", "properties": 0}, {"selector": ".fa-bullhorn:before", "properties": 0}, {"selector": ".fa-bullseye:before", "properties": 0}, {"selector": ".fa-burn:before", "properties": 0}, {"selector": ".fa-buromobelexperte:before", "properties": 0}, {"selector": ".fa-bus:before", "properties": 0}, {"selector": ".fa-bus-alt:before", "properties": 0}, {"selector": ".fa-business-time:before", "properties": 0}, {"selector": ".fa-buy-n-large:before", "properties": 0}, {"selector": ".fa-buysellads:before", "properties": 0}, {"selector": ".fa-calculator:before", "properties": 0}, {"selector": ".fa-calendar:before", "properties": 0}, {"selector": ".fa-calendar-alt:before", "properties": 0}, {"selector": ".fa-calendar-check:before", "properties": 0}, {"selector": ".fa-calendar-day:before", "properties": 0}, {"selector": ".fa-calendar-minus:before", "properties": 0}, {"selector": ".fa-calendar-plus:before", "properties": 0}, {"selector": ".fa-calendar-times:before", "properties": 0}, {"selector": ".fa-calendar-week:before", "properties": 0}, {"selector": ".fa-camera:before", "properties": 0}, {"selector": ".fa-camera-retro:before", "properties": 0}, {"selector": ".fa-campground:before", "properties": 0}, {"selector": ".fa-canadian-maple-leaf:before", "properties": 0}, {"selector": ".fa-candy-cane:before", "properties": 0}, {"selector": ".fa-cannabis:before", "properties": 0}, {"selector": ".fa-capsules:before", "properties": 0}, {"selector": ".fa-car:before", "properties": 0}, {"selector": ".fa-car-alt:before", "properties": 0}, {"selector": ".fa-car-battery:before", "properties": 0}, {"selector": ".fa-car-crash:before", "properties": 0}, {"selector": ".fa-car-side:before", "properties": 0}, {"selector": ".fa-caravan:before", "properties": 0}, {"selector": ".fa-caret-down:before", "properties": 0}, {"selector": ".fa-caret-left:before", "properties": 0}, {"selector": ".fa-caret-right:before", "properties": 0}, {"selector": ".fa-caret-square-down:before", "properties": 0}, {"selector": ".fa-caret-square-left:before", "properties": 0}, {"selector": ".fa-caret-square-right:before", "properties": 0}, {"selector": ".fa-caret-square-up:before", "properties": 0}, {"selector": ".fa-caret-up:before", "properties": 0}, {"selector": ".fa-carrot:before", "properties": 0}, {"selector": ".fa-cart-arrow-down:before", "properties": 0}, {"selector": ".fa-cart-plus:before", "properties": 0}, {"selector": ".fa-cash-register:before", "properties": 0}, {"selector": ".fa-cat:before", "properties": 0}, {"selector": ".fa-cc-amazon-pay:before", "properties": 0}, {"selector": ".fa-cc-amex:before", "properties": 0}, {"selector": ".fa-cc-apple-pay:before", "properties": 0}, {"selector": ".fa-cc-diners-club:before", "properties": 0}, {"selector": ".fa-cc-discover:before", "properties": 0}, {"selector": ".fa-cc-jcb:before", "properties": 0}, {"selector": ".fa-cc-mastercard:before", "properties": 0}, {"selector": ".fa-cc-paypal:before", "properties": 0}, {"selector": ".fa-cc-stripe:before", "properties": 0}, {"selector": ".fa-cc-visa:before", "properties": 0}, {"selector": ".fa-centercode:before", "properties": 0}, {"selector": ".fa-centos:before", "properties": 0}, {"selector": ".fa-certificate:before", "properties": 0}, {"selector": ".fa-chair:before", "properties": 0}, {"selector": ".fa-chalkboard:before", "properties": 0}, {"selector": ".fa-chalkboard-teacher:before", "properties": 0}, {"selector": ".fa-charging-station:before", "properties": 0}, {"selector": ".fa-chart-area:before", "properties": 0}, {"selector": ".fa-chart-bar:before", "properties": 0}, {"selector": ".fa-chart-line:before", "properties": 0}, {"selector": ".fa-chart-pie:before", "properties": 0}, {"selector": ".fa-check:before", "properties": 0}, {"selector": ".fa-check-circle:before", "properties": 0}, {"selector": ".fa-check-double:before", "properties": 0}, {"selector": ".fa-check-square:before", "properties": 0}, {"selector": ".fa-cheese:before", "properties": 0}, {"selector": ".fa-chess:before", "properties": 0}, {"selector": ".fa-chess-bishop:before", "properties": 0}, {"selector": ".fa-chess-board:before", "properties": 0}, {"selector": ".fa-chess-king:before", "properties": 0}, {"selector": ".fa-chess-knight:before", "properties": 0}, {"selector": ".fa-chess-pawn:before", "properties": 0}, {"selector": ".fa-chess-queen:before", "properties": 0}, {"selector": ".fa-chess-rook:before", "properties": 0}, {"selector": ".fa-chevron-circle-down:before", "properties": 0}, {"selector": ".fa-chevron-circle-left:before", "properties": 0}, {"selector": ".fa-chevron-circle-right:before", "properties": 0}, {"selector": ".fa-chevron-circle-up:before", "properties": 0}, {"selector": ".fa-chevron-down:before", "properties": 0}, {"selector": ".fa-chevron-left:before", "properties": 0}, {"selector": ".fa-chevron-right:before", "properties": 0}, {"selector": ".fa-chevron-up:before", "properties": 0}, {"selector": ".fa-child:before", "properties": 0}, {"selector": ".fa-chrome:before", "properties": 0}, {"selector": ".fa-chromecast:before", "properties": 0}, {"selector": ".fa-church:before", "properties": 0}, {"selector": ".fa-circle:before", "properties": 0}, {"selector": ".fa-circle-notch:before", "properties": 0}, {"selector": ".fa-city:before", "properties": 0}, {"selector": ".fa-clinic-medical:before", "properties": 0}, {"selector": ".fa-clipboard:before", "properties": 0}, {"selector": ".fa-clipboard-check:before", "properties": 0}, {"selector": ".fa-clipboard-list:before", "properties": 0}, {"selector": ".fa-clock:before", "properties": 0}, {"selector": ".fa-clone:before", "properties": 0}, {"selector": ".fa-closed-captioning:before", "properties": 0}, {"selector": ".fa-cloud:before", "properties": 0}, {"selector": ".fa-cloud-download-alt:before", "properties": 0}, {"selector": ".fa-cloud-meatball:before", "properties": 0}, {"selector": ".fa-cloud-moon:before", "properties": 0}, {"selector": ".fa-cloud-moon-rain:before", "properties": 0}, {"selector": ".fa-cloud-rain:before", "properties": 0}, {"selector": ".fa-cloud-showers-heavy:before", "properties": 0}, {"selector": ".fa-cloud-sun:before", "properties": 0}, {"selector": ".fa-cloud-sun-rain:before", "properties": 0}, {"selector": ".fa-cloud-upload-alt:before", "properties": 0}, {"selector": ".fa-cloudflare:before", "properties": 0}, {"selector": ".fa-cloudscale:before", "properties": 0}, {"selector": ".fa-cloudsmith:before", "properties": 0}, {"selector": ".fa-cloudversify:before", "properties": 0}, {"selector": ".fa-cocktail:before", "properties": 0}, {"selector": ".fa-code:before", "properties": 0}, {"selector": ".fa-code-branch:before", "properties": 0}, {"selector": ".fa-codepen:before", "properties": 0}, {"selector": ".fa-codiepie:before", "properties": 0}, {"selector": ".fa-coffee:before", "properties": 0}, {"selector": ".fa-cog:before", "properties": 0}, {"selector": ".fa-cogs:before", "properties": 0}, {"selector": ".fa-coins:before", "properties": 0}, {"selector": ".fa-columns:before", "properties": 0}, {"selector": ".fa-comment:before", "properties": 0}, {"selector": ".fa-comment-alt:before", "properties": 0}, {"selector": ".fa-comment-dollar:before", "properties": 0}, {"selector": ".fa-comment-dots:before", "properties": 0}, {"selector": ".fa-comment-medical:before", "properties": 0}, {"selector": ".fa-comment-slash:before", "properties": 0}, {"selector": ".fa-comments:before", "properties": 0}, {"selector": ".fa-comments-dollar:before", "properties": 0}, {"selector": ".fa-compact-disc:before", "properties": 0}, {"selector": ".fa-compass:before", "properties": 0}, {"selector": ".fa-compress:before", "properties": 0}, {"selector": ".fa-compress-alt:before", "properties": 0}, {"selector": ".fa-compress-arrows-alt:before", "properties": 0}, {"selector": ".fa-concierge-bell:before", "properties": 0}, {"selector": ".fa-confluence:before", "properties": 0}, {"selector": ".fa-connectdevelop:before", "properties": 0}, {"selector": ".fa-contao:before", "properties": 0}, {"selector": ".fa-cookie:before", "properties": 0}, {"selector": ".fa-cookie-bite:before", "properties": 0}, {"selector": ".fa-copy:before", "properties": 0}, {"selector": ".fa-copyright:before", "properties": 0}, {"selector": ".fa-cotton-bureau:before", "properties": 0}, {"selector": ".fa-couch:before", "properties": 0}, {"selector": ".fa-cpanel:before", "properties": 0}, {"selector": ".fa-creative-commons:before", "properties": 0}, {"selector": ".fa-creative-commons-by:before", "properties": 0}, {"selector": ".fa-creative-commons-nc:before", "properties": 0}, {"selector": ".fa-creative-commons-nc-eu:before", "properties": 0}, {"selector": ".fa-creative-commons-nc-jp:before", "properties": 0}, {"selector": ".fa-creative-commons-nd:before", "properties": 0}, {"selector": ".fa-creative-commons-pd:before", "properties": 0}, {"selector": ".fa-creative-commons-pd-alt:before", "properties": 0}, {"selector": ".fa-creative-commons-remix:before", "properties": 0}, {"selector": ".fa-creative-commons-sa:before", "properties": 0}, {"selector": ".fa-creative-commons-sampling:before", "properties": 0}, {"selector": ".fa-creative-commons-sampling-plus:before", "properties": 0}, {"selector": ".fa-creative-commons-share:before", "properties": 0}, {"selector": ".fa-creative-commons-zero:before", "properties": 0}, {"selector": ".fa-credit-card:before", "properties": 0}, {"selector": ".fa-critical-role:before", "properties": 0}, {"selector": ".fa-crop:before", "properties": 0}, {"selector": ".fa-crop-alt:before", "properties": 0}, {"selector": ".fa-cross:before", "properties": 0}, {"selector": ".fa-crosshairs:before", "properties": 0}, {"selector": ".fa-crow:before", "properties": 0}, {"selector": ".fa-crown:before", "properties": 0}, {"selector": ".fa-crutch:before", "properties": 0}, {"selector": ".fa-css3:before", "properties": 0}, {"selector": ".fa-css3-alt:before", "properties": 0}, {"selector": ".fa-cube:before", "properties": 0}, {"selector": ".fa-cubes:before", "properties": 0}, {"selector": ".fa-cut:before", "properties": 0}, {"selector": ".fa-cuttlefish:before", "properties": 0}, {"selector": ".fa-d-and-d:before", "properties": 0}, {"selector": ".fa-d-and-d-beyond:before", "properties": 0}, {"selector": ".fa-dailymotion:before", "properties": 0}, {"selector": ".fa-dashcube:before", "properties": 0}, {"selector": ".fa-database:before", "properties": 0}, {"selector": ".fa-deaf:before", "properties": 0}, {"selector": ".fa-deezer:before", "properties": 0}, {"selector": ".fa-delicious:before", "properties": 0}, {"selector": ".fa-democrat:before", "properties": 0}, {"selector": ".fa-deploydog:before", "properties": 0}, {"selector": ".fa-deskpro:before", "properties": 0}, {"selector": ".fa-desktop:before", "properties": 0}, {"selector": ".fa-dev:before", "properties": 0}, {"selector": ".fa-deviantart:before", "properties": 0}, {"selector": ".fa-dharmachakra:before", "properties": 0}, {"selector": ".fa-dhl:before", "properties": 0}, {"selector": ".fa-diagnoses:before", "properties": 0}, {"selector": ".fa-diaspora:before", "properties": 0}, {"selector": ".fa-dice:before", "properties": 0}, {"selector": ".fa-dice-d20:before", "properties": 0}, {"selector": ".fa-dice-d6:before", "properties": 0}, {"selector": ".fa-dice-five:before", "properties": 0}, {"selector": ".fa-dice-four:before", "properties": 0}, {"selector": ".fa-dice-one:before", "properties": 0}, {"selector": ".fa-dice-six:before", "properties": 0}, {"selector": ".fa-dice-three:before", "properties": 0}, {"selector": ".fa-dice-two:before", "properties": 0}, {"selector": ".fa-digg:before", "properties": 0}, {"selector": ".fa-digital-ocean:before", "properties": 0}, {"selector": ".fa-digital-tachograph:before", "properties": 0}, {"selector": ".fa-directions:before", "properties": 0}, {"selector": ".fa-discord:before", "properties": 0}, {"selector": ".fa-discourse:before", "properties": 0}, {"selector": ".fa-disease:before", "properties": 0}, {"selector": ".fa-divide:before", "properties": 0}, {"selector": ".fa-dizzy:before", "properties": 0}, {"selector": ".fa-dna:before", "properties": 0}, {"selector": ".fa-dochub:before", "properties": 0}, {"selector": ".fa-docker:before", "properties": 0}, {"selector": ".fa-dog:before", "properties": 0}, {"selector": ".fa-dollar-sign:before", "properties": 0}, {"selector": ".fa-dolly:before", "properties": 0}, {"selector": ".fa-dolly-flatbed:before", "properties": 0}, {"selector": ".fa-donate:before", "properties": 0}, {"selector": ".fa-door-closed:before", "properties": 0}, {"selector": ".fa-door-open:before", "properties": 0}, {"selector": ".fa-dot-circle:before", "properties": 0}, {"selector": ".fa-dove:before", "properties": 0}, {"selector": ".fa-download:before", "properties": 0}, {"selector": ".fa-draft2digital:before", "properties": 0}, {"selector": ".fa-drafting-compass:before", "properties": 0}, {"selector": ".fa-dragon:before", "properties": 0}, {"selector": ".fa-draw-polygon:before", "properties": 0}, {"selector": ".fa-dribbble:before", "properties": 0}, {"selector": ".fa-dribbble-square:before", "properties": 0}, {"selector": ".fa-dropbox:before", "properties": 0}, {"selector": ".fa-drum:before", "properties": 0}, {"selector": ".fa-drum-steelpan:before", "properties": 0}, {"selector": ".fa-drumstick-bite:before", "properties": 0}, {"selector": ".fa-drupal:before", "properties": 0}, {"selector": ".fa-dumbbell:before", "properties": 0}, {"selector": ".fa-dumpster:before", "properties": 0}, {"selector": ".fa-dumpster-fire:before", "properties": 0}, {"selector": ".fa-dungeon:before", "properties": 0}, {"selector": ".fa-dyalog:before", "properties": 0}, {"selector": ".fa-earlybirds:before", "properties": 0}, {"selector": ".fa-ebay:before", "properties": 0}, {"selector": ".fa-edge:before", "properties": 0}, {"selector": ".fa-edge-legacy:before", "properties": 0}, {"selector": ".fa-edit:before", "properties": 0}, {"selector": ".fa-egg:before", "properties": 0}, {"selector": ".fa-eject:before", "properties": 0}, {"selector": ".fa-elementor:before", "properties": 0}, {"selector": ".fa-ellipsis-h:before", "properties": 0}, {"selector": ".fa-ellipsis-v:before", "properties": 0}, {"selector": ".fa-ello:before", "properties": 0}, {"selector": ".fa-ember:before", "properties": 0}, {"selector": ".fa-empire:before", "properties": 0}, {"selector": ".fa-envelope:before", "properties": 0}, {"selector": ".fa-envelope-open:before", "properties": 0}, {"selector": ".fa-envelope-open-text:before", "properties": 0}, {"selector": ".fa-envelope-square:before", "properties": 0}, {"selector": ".fa-envira:before", "properties": 0}, {"selector": ".fa-equals:before", "properties": 0}, {"selector": ".fa-eraser:before", "properties": 0}, {"selector": ".fa-erlang:before", "properties": 0}, {"selector": ".fa-ethereum:before", "properties": 0}, {"selector": ".fa-ethernet:before", "properties": 0}, {"selector": ".fa-etsy:before", "properties": 0}, {"selector": ".fa-euro-sign:before", "properties": 0}, {"selector": ".fa-evernote:before", "properties": 0}, {"selector": ".fa-exchange-alt:before", "properties": 0}, {"selector": ".fa-exclamation:before", "properties": 0}, {"selector": ".fa-exclamation-circle:before", "properties": 0}, {"selector": ".fa-exclamation-triangle:before", "properties": 0}, {"selector": ".fa-expand:before", "properties": 0}, {"selector": ".fa-expand-alt:before", "properties": 0}, {"selector": ".fa-expand-arrows-alt:before", "properties": 0}, {"selector": ".fa-expeditedssl:before", "properties": 0}, {"selector": ".fa-external-link-alt:before", "properties": 0}, {"selector": ".fa-external-link-square-alt:before", "properties": 0}, {"selector": ".fa-eye:before", "properties": 0}, {"selector": ".fa-eye-dropper:before", "properties": 0}, {"selector": ".fa-eye-slash:before", "properties": 0}, {"selector": ".fa-facebook:before", "properties": 0}, {"selector": ".fa-facebook-f:before", "properties": 0}, {"selector": ".fa-facebook-messenger:before", "properties": 0}, {"selector": ".fa-facebook-square:before", "properties": 0}, {"selector": ".fa-fan:before", "properties": 0}, {"selector": ".fa-fantasy-flight-games:before", "properties": 0}, {"selector": ".fa-fast-backward:before", "properties": 0}, {"selector": ".fa-fast-forward:before", "properties": 0}, {"selector": ".fa-faucet:before", "properties": 0}, {"selector": ".fa-fax:before", "properties": 0}, {"selector": ".fa-feather:before", "properties": 0}, {"selector": ".fa-feather-alt:before", "properties": 0}, {"selector": ".fa-fedex:before", "properties": 0}, {"selector": ".fa-fedora:before", "properties": 0}, {"selector": ".fa-female:before", "properties": 0}, {"selector": ".fa-fighter-jet:before", "properties": 0}, {"selector": ".fa-figma:before", "properties": 0}, {"selector": ".fa-file:before", "properties": 0}, {"selector": ".fa-file-alt:before", "properties": 0}, {"selector": ".fa-file-archive:before", "properties": 0}, {"selector": ".fa-file-audio:before", "properties": 0}, {"selector": ".fa-file-code:before", "properties": 0}, {"selector": ".fa-file-contract:before", "properties": 0}, {"selector": ".fa-file-csv:before", "properties": 0}, {"selector": ".fa-file-download:before", "properties": 0}, {"selector": ".fa-file-excel:before", "properties": 0}, {"selector": ".fa-file-export:before", "properties": 0}, {"selector": ".fa-file-image:before", "properties": 0}, {"selector": ".fa-file-import:before", "properties": 0}, {"selector": ".fa-file-invoice:before", "properties": 0}, {"selector": ".fa-file-invoice-dollar:before", "properties": 0}, {"selector": ".fa-file-medical:before", "properties": 0}, {"selector": ".fa-file-medical-alt:before", "properties": 0}, {"selector": ".fa-file-pdf:before", "properties": 0}, {"selector": ".fa-file-powerpoint:before", "properties": 0}, {"selector": ".fa-file-prescription:before", "properties": 0}, {"selector": ".fa-file-signature:before", "properties": 0}, {"selector": ".fa-file-upload:before", "properties": 0}, {"selector": ".fa-file-video:before", "properties": 0}, {"selector": ".fa-file-word:before", "properties": 0}, {"selector": ".fa-fill:before", "properties": 0}, {"selector": ".fa-fill-drip:before", "properties": 0}, {"selector": ".fa-film:before", "properties": 0}, {"selector": ".fa-filter:before", "properties": 0}, {"selector": ".fa-fingerprint:before", "properties": 0}, {"selector": ".fa-fire:before", "properties": 0}, {"selector": ".fa-fire-alt:before", "properties": 0}, {"selector": ".fa-fire-extinguisher:before", "properties": 0}, {"selector": ".fa-firefox:before", "properties": 0}, {"selector": ".fa-firefox-browser:before", "properties": 0}, {"selector": ".fa-first-aid:before", "properties": 0}, {"selector": ".fa-first-order:before", "properties": 0}, {"selector": ".fa-first-order-alt:before", "properties": 0}, {"selector": ".fa-firstdraft:before", "properties": 0}, {"selector": ".fa-fish:before", "properties": 0}, {"selector": ".fa-fist-raised:before", "properties": 0}, {"selector": ".fa-flag:before", "properties": 0}, {"selector": ".fa-flag-checkered:before", "properties": 0}, {"selector": ".fa-flag-usa:before", "properties": 0}, {"selector": ".fa-flask:before", "properties": 0}, {"selector": ".fa-flickr:before", "properties": 0}, {"selector": ".fa-flipboard:before", "properties": 0}, {"selector": ".fa-flushed:before", "properties": 0}, {"selector": ".fa-fly:before", "properties": 0}, {"selector": ".fa-folder:before", "properties": 0}, {"selector": ".fa-folder-minus:before", "properties": 0}, {"selector": ".fa-folder-open:before", "properties": 0}, {"selector": ".fa-folder-plus:before", "properties": 0}, {"selector": ".fa-font:before", "properties": 0}, {"selector": ".fa-font-awesome:before", "properties": 0}, {"selector": ".fa-font-awesome-alt:before", "properties": 0}, {"selector": ".fa-font-awesome-flag:before", "properties": 0}, {"selector": ".fa-font-awesome-logo-full:before", "properties": 0}, {"selector": ".fa-fonticons:before", "properties": 0}, {"selector": ".fa-fonticons-fi:before", "properties": 0}, {"selector": ".fa-football-ball:before", "properties": 0}, {"selector": ".fa-fort-awesome:before", "properties": 0}, {"selector": ".fa-fort-awesome-alt:before", "properties": 0}, {"selector": ".fa-forumbee:before", "properties": 0}, {"selector": ".fa-forward:before", "properties": 0}, {"selector": ".fa-foursquare:before", "properties": 0}, {"selector": ".fa-free-code-camp:before", "properties": 0}, {"selector": ".fa-freebsd:before", "properties": 0}, {"selector": ".fa-frog:before", "properties": 0}, {"selector": ".fa-frown:before", "properties": 0}, {"selector": ".fa-frown-open:before", "properties": 0}, {"selector": ".fa-fulcrum:before", "properties": 0}, {"selector": ".fa-funnel-dollar:before", "properties": 0}, {"selector": ".fa-futbol:before", "properties": 0}, {"selector": ".fa-galactic-republic:before", "properties": 0}, {"selector": ".fa-galactic-senate:before", "properties": 0}, {"selector": ".fa-gamepad:before", "properties": 0}, {"selector": ".fa-gas-pump:before", "properties": 0}, {"selector": ".fa-gavel:before", "properties": 0}, {"selector": ".fa-gem:before", "properties": 0}, {"selector": ".fa-genderless:before", "properties": 0}, {"selector": ".fa-get-pocket:before", "properties": 0}, {"selector": ".fa-gg:before", "properties": 0}, {"selector": ".fa-gg-circle:before", "properties": 0}, {"selector": ".fa-ghost:before", "properties": 0}, {"selector": ".fa-gift:before", "properties": 0}, {"selector": ".fa-gifts:before", "properties": 0}, {"selector": ".fa-git:before", "properties": 0}, {"selector": ".fa-git-alt:before", "properties": 0}, {"selector": ".fa-git-square:before", "properties": 0}, {"selector": ".fa-github:before", "properties": 0}, {"selector": ".fa-github-alt:before", "properties": 0}, {"selector": ".fa-github-square:before", "properties": 0}, {"selector": ".fa-gitkraken:before", "properties": 0}, {"selector": ".fa-gitlab:before", "properties": 0}, {"selector": ".fa-gitter:before", "properties": 0}, {"selector": ".fa-glass-cheers:before", "properties": 0}, {"selector": ".fa-glass-martini:before", "properties": 0}, {"selector": ".fa-glass-martini-alt:before", "properties": 0}, {"selector": ".fa-glass-whiskey:before", "properties": 0}, {"selector": ".fa-glasses:before", "properties": 0}, {"selector": ".fa-glide:before", "properties": 0}, {"selector": ".fa-glide-g:before", "properties": 0}, {"selector": ".fa-globe:before", "properties": 0}, {"selector": ".fa-globe-africa:before", "properties": 0}, {"selector": ".fa-globe-americas:before", "properties": 0}, {"selector": ".fa-globe-asia:before", "properties": 0}, {"selector": ".fa-globe-europe:before", "properties": 0}, {"selector": ".fa-gofore:before", "properties": 0}, {"selector": ".fa-golf-ball:before", "properties": 0}, {"selector": ".fa-goodreads:before", "properties": 0}, {"selector": ".fa-goodreads-g:before", "properties": 0}, {"selector": ".fa-google:before", "properties": 0}, {"selector": ".fa-google-drive:before", "properties": 0}, {"selector": ".fa-google-pay:before", "properties": 0}, {"selector": ".fa-google-play:before", "properties": 0}, {"selector": ".fa-google-plus:before", "properties": 0}, {"selector": ".fa-google-plus-g:before", "properties": 0}, {"selector": ".fa-google-plus-square:before", "properties": 0}, {"selector": ".fa-google-wallet:before", "properties": 0}, {"selector": ".fa-gopuram:before", "properties": 0}, {"selector": ".fa-graduation-cap:before", "properties": 0}, {"selector": ".fa-gratipay:before", "properties": 0}, {"selector": ".fa-grav:before", "properties": 0}, {"selector": ".fa-greater-than:before", "properties": 0}, {"selector": ".fa-greater-than-equal:before", "properties": 0}, {"selector": ".fa-grimace:before", "properties": 0}, {"selector": ".fa-grin:before", "properties": 0}, {"selector": ".fa-grin-alt:before", "properties": 0}, {"selector": ".fa-grin-beam:before", "properties": 0}, {"selector": ".fa-grin-beam-sweat:before", "properties": 0}, {"selector": ".fa-grin-hearts:before", "properties": 0}, {"selector": ".fa-grin-squint:before", "properties": 0}, {"selector": ".fa-grin-squint-tears:before", "properties": 0}, {"selector": ".fa-grin-stars:before", "properties": 0}, {"selector": ".fa-grin-tears:before", "properties": 0}, {"selector": ".fa-grin-tongue:before", "properties": 0}, {"selector": ".fa-grin-tongue-squint:before", "properties": 0}, {"selector": ".fa-grin-tongue-wink:before", "properties": 0}, {"selector": ".fa-grin-wink:before", "properties": 0}, {"selector": ".fa-grip-horizontal:before", "properties": 0}, {"selector": ".fa-grip-lines:before", "properties": 0}, {"selector": ".fa-grip-lines-vertical:before", "properties": 0}, {"selector": ".fa-grip-vertical:before", "properties": 0}, {"selector": ".fa-gripfire:before", "properties": 0}, {"selector": ".fa-grunt:before", "properties": 0}, {"selector": ".fa-guilded:before", "properties": 0}, {"selector": ".fa-guitar:before", "properties": 0}, {"selector": ".fa-gulp:before", "properties": 0}, {"selector": ".fa-h-square:before", "properties": 0}, {"selector": ".fa-hacker-news:before", "properties": 0}, {"selector": ".fa-hacker-news-square:before", "properties": 0}, {"selector": ".fa-hackerrank:before", "properties": 0}, {"selector": ".fa-hamburger:before", "properties": 0}, {"selector": ".fa-hammer:before", "properties": 0}, {"selector": ".fa-hamsa:before", "properties": 0}, {"selector": ".fa-hand-holding:before", "properties": 0}, {"selector": ".fa-hand-holding-heart:before", "properties": 0}, {"selector": ".fa-hand-holding-medical:before", "properties": 0}, {"selector": ".fa-hand-holding-usd:before", "properties": 0}, {"selector": ".fa-hand-holding-water:before", "properties": 0}, {"selector": ".fa-hand-lizard:before", "properties": 0}, {"selector": ".fa-hand-middle-finger:before", "properties": 0}, {"selector": ".fa-hand-paper:before", "properties": 0}, {"selector": ".fa-hand-peace:before", "properties": 0}, {"selector": ".fa-hand-point-down:before", "properties": 0}, {"selector": ".fa-hand-point-left:before", "properties": 0}, {"selector": ".fa-hand-point-right:before", "properties": 0}, {"selector": ".fa-hand-point-up:before", "properties": 0}, {"selector": ".fa-hand-pointer:before", "properties": 0}, {"selector": ".fa-hand-rock:before", "properties": 0}, {"selector": ".fa-hand-scissors:before", "properties": 0}, {"selector": ".fa-hand-sparkles:before", "properties": 0}, {"selector": ".fa-hand-spock:before", "properties": 0}, {"selector": ".fa-hands:before", "properties": 0}, {"selector": ".fa-hands-helping:before", "properties": 0}, {"selector": ".fa-hands-wash:before", "properties": 0}, {"selector": ".fa-handshake:before", "properties": 0}, {"selector": ".fa-handshake-alt-slash:before", "properties": 0}, {"selector": ".fa-handshake-slash:before", "properties": 0}, {"selector": ".fa-hanukiah:before", "properties": 0}, {"selector": ".fa-hard-hat:before", "properties": 0}, {"selector": ".fa-hashtag:before", "properties": 0}, {"selector": ".fa-hat-cowboy:before", "properties": 0}, {"selector": ".fa-hat-cowboy-side:before", "properties": 0}, {"selector": ".fa-hat-wizard:before", "properties": 0}, {"selector": ".fa-hdd:before", "properties": 0}, {"selector": ".fa-head-side-cough:before", "properties": 0}, {"selector": ".fa-head-side-cough-slash:before", "properties": 0}, {"selector": ".fa-head-side-mask:before", "properties": 0}, {"selector": ".fa-head-side-virus:before", "properties": 0}, {"selector": ".fa-heading:before", "properties": 0}, {"selector": ".fa-headphones:before", "properties": 0}, {"selector": ".fa-headphones-alt:before", "properties": 0}, {"selector": ".fa-headset:before", "properties": 0}, {"selector": ".fa-heart:before", "properties": 0}, {"selector": ".fa-heart-broken:before", "properties": 0}, {"selector": ".fa-heartbeat:before", "properties": 0}, {"selector": ".fa-helicopter:before", "properties": 0}, {"selector": ".fa-highlighter:before", "properties": 0}, {"selector": ".fa-hiking:before", "properties": 0}, {"selector": ".fa-hippo:before", "properties": 0}, {"selector": ".fa-hips:before", "properties": 0}, {"selector": ".fa-hire-a-helper:before", "properties": 0}, {"selector": ".fa-history:before", "properties": 0}, {"selector": ".fa-hive:before", "properties": 0}, {"selector": ".fa-hockey-puck:before", "properties": 0}, {"selector": ".fa-holly-berry:before", "properties": 0}, {"selector": ".fa-home:before", "properties": 0}, {"selector": ".fa-hooli:before", "properties": 0}, {"selector": ".fa-hornbill:before", "properties": 0}, {"selector": ".fa-horse:before", "properties": 0}, {"selector": ".fa-horse-head:before", "properties": 0}, {"selector": ".fa-hospital:before", "properties": 0}, {"selector": ".fa-hospital-alt:before", "properties": 0}, {"selector": ".fa-hospital-symbol:before", "properties": 0}, {"selector": ".fa-hospital-user:before", "properties": 0}, {"selector": ".fa-hot-tub:before", "properties": 0}, {"selector": ".fa-hotdog:before", "properties": 0}, {"selector": ".fa-hotel:before", "properties": 0}, {"selector": ".fa-hotjar:before", "properties": 0}, {"selector": ".fa-hourglass:before", "properties": 0}, {"selector": ".fa-hourglass-end:before", "properties": 0}, {"selector": ".fa-hourglass-half:before", "properties": 0}, {"selector": ".fa-hourglass-start:before", "properties": 0}, {"selector": ".fa-house-damage:before", "properties": 0}, {"selector": ".fa-house-user:before", "properties": 0}, {"selector": ".fa-houzz:before", "properties": 0}, {"selector": ".fa-hryvnia:before", "properties": 0}, {"selector": ".fa-html5:before", "properties": 0}, {"selector": ".fa-hubspot:before", "properties": 0}, {"selector": ".fa-i-cursor:before", "properties": 0}, {"selector": ".fa-ice-cream:before", "properties": 0}, {"selector": ".fa-icicles:before", "properties": 0}, {"selector": ".fa-icons:before", "properties": 0}, {"selector": ".fa-id-badge:before", "properties": 0}, {"selector": ".fa-id-card:before", "properties": 0}, {"selector": ".fa-id-card-alt:before", "properties": 0}, {"selector": ".fa-ideal:before", "properties": 0}, {"selector": ".fa-igloo:before", "properties": 0}, {"selector": ".fa-image:before", "properties": 0}, {"selector": ".fa-images:before", "properties": 0}, {"selector": ".fa-imdb:before", "properties": 0}, {"selector": ".fa-inbox:before", "properties": 0}, {"selector": ".fa-indent:before", "properties": 0}, {"selector": ".fa-industry:before", "properties": 0}, {"selector": ".fa-infinity:before", "properties": 0}, {"selector": ".fa-info:before", "properties": 0}, {"selector": ".fa-info-circle:before", "properties": 0}, {"selector": ".fa-innosoft:before", "properties": 0}, {"selector": ".fa-instagram:before", "properties": 0}, {"selector": ".fa-instagram-square:before", "properties": 0}, {"selector": ".fa-instalod:before", "properties": 0}, {"selector": ".fa-intercom:before", "properties": 0}, {"selector": ".fa-internet-explorer:before", "properties": 0}, {"selector": ".fa-invision:before", "properties": 0}, {"selector": ".fa-ioxhost:before", "properties": 0}, {"selector": ".fa-italic:before", "properties": 0}, {"selector": ".fa-itch-io:before", "properties": 0}, {"selector": ".fa-itunes:before", "properties": 0}, {"selector": ".fa-itunes-note:before", "properties": 0}, {"selector": ".fa-java:before", "properties": 0}, {"selector": ".fa-jedi:before", "properties": 0}, {"selector": ".fa-jedi-order:before", "properties": 0}, {"selector": ".fa-jenkins:before", "properties": 0}, {"selector": ".fa-jira:before", "properties": 0}, {"selector": ".fa-joget:before", "properties": 0}, {"selector": ".fa-joint:before", "properties": 0}, {"selector": ".fa-joomla:before", "properties": 0}, {"selector": ".fa-journal-whills:before", "properties": 0}, {"selector": ".fa-js:before", "properties": 0}, {"selector": ".fa-js-square:before", "properties": 0}, {"selector": ".fa-jsfiddle:before", "properties": 0}, {"selector": ".fa-kaaba:before", "properties": 0}, {"selector": ".fa-kaggle:before", "properties": 0}, {"selector": ".fa-key:before", "properties": 0}, {"selector": ".fa-keybase:before", "properties": 0}, {"selector": ".fa-keyboard:before", "properties": 0}, {"selector": ".fa-keycdn:before", "properties": 0}, {"selector": ".fa-khanda:before", "properties": 0}, {"selector": ".fa-kickstarter:before", "properties": 0}, {"selector": ".fa-kickstarter-k:before", "properties": 0}, {"selector": ".fa-kiss:before", "properties": 0}, {"selector": ".fa-kiss-beam:before", "properties": 0}, {"selector": ".fa-kiss-wink-heart:before", "properties": 0}, {"selector": ".fa-kiwi-bird:before", "properties": 0}, {"selector": ".fa-korvue:before", "properties": 0}, {"selector": ".fa-landmark:before", "properties": 0}, {"selector": ".fa-language:before", "properties": 0}, {"selector": ".fa-laptop:before", "properties": 0}, {"selector": ".fa-laptop-code:before", "properties": 0}, {"selector": ".fa-laptop-house:before", "properties": 0}, {"selector": ".fa-laptop-medical:before", "properties": 0}, {"selector": ".fa-laravel:before", "properties": 0}, {"selector": ".fa-lastfm:before", "properties": 0}, {"selector": ".fa-lastfm-square:before", "properties": 0}, {"selector": ".fa-laugh:before", "properties": 0}, {"selector": ".fa-laugh-beam:before", "properties": 0}, {"selector": ".fa-laugh-squint:before", "properties": 0}, {"selector": ".fa-laugh-wink:before", "properties": 0}, {"selector": ".fa-layer-group:before", "properties": 0}, {"selector": ".fa-leaf:before", "properties": 0}, {"selector": ".fa-leanpub:before", "properties": 0}, {"selector": ".fa-lemon:before", "properties": 0}, {"selector": ".fa-less:before", "properties": 0}, {"selector": ".fa-less-than:before", "properties": 0}, {"selector": ".fa-less-than-equal:before", "properties": 0}, {"selector": ".fa-level-down-alt:before", "properties": 0}, {"selector": ".fa-level-up-alt:before", "properties": 0}, {"selector": ".fa-life-ring:before", "properties": 0}, {"selector": ".fa-lightbulb:before", "properties": 0}, {"selector": ".fa-line:before", "properties": 0}, {"selector": ".fa-link:before", "properties": 0}, {"selector": ".fa-linkedin:before", "properties": 0}, {"selector": ".fa-linkedin-in:before", "properties": 0}, {"selector": ".fa-linode:before", "properties": 0}, {"selector": ".fa-linux:before", "properties": 0}, {"selector": ".fa-lira-sign:before", "properties": 0}, {"selector": ".fa-list:before", "properties": 0}, {"selector": ".fa-list-alt:before", "properties": 0}, {"selector": ".fa-list-ol:before", "properties": 0}, {"selector": ".fa-list-ul:before", "properties": 0}, {"selector": ".fa-location-arrow:before", "properties": 0}, {"selector": ".fa-lock:before", "properties": 0}, {"selector": ".fa-lock-open:before", "properties": 0}, {"selector": ".fa-long-arrow-alt-down:before", "properties": 0}, {"selector": ".fa-long-arrow-alt-left:before", "properties": 0}, {"selector": ".fa-long-arrow-alt-right:before", "properties": 0}, {"selector": ".fa-long-arrow-alt-up:before", "properties": 0}, {"selector": ".fa-low-vision:before", "properties": 0}, {"selector": ".fa-luggage-cart:before", "properties": 0}, {"selector": ".fa-lungs:before", "properties": 0}, {"selector": ".fa-lungs-virus:before", "properties": 0}, {"selector": ".fa-lyft:before", "properties": 0}, {"selector": ".fa-magento:before", "properties": 0}, {"selector": ".fa-magic:before", "properties": 0}, {"selector": ".fa-magnet:before", "properties": 0}, {"selector": ".fa-mail-bulk:before", "properties": 0}, {"selector": ".fa-mailchimp:before", "properties": 0}, {"selector": ".fa-male:before", "properties": 0}, {"selector": ".fa-mandalorian:before", "properties": 0}, {"selector": ".fa-map:before", "properties": 0}, {"selector": ".fa-map-marked:before", "properties": 0}, {"selector": ".fa-map-marked-alt:before", "properties": 0}, {"selector": ".fa-map-marker:before", "properties": 0}, {"selector": ".fa-map-marker-alt:before", "properties": 0}, {"selector": ".fa-map-pin:before", "properties": 0}, {"selector": ".fa-map-signs:before", "properties": 0}, {"selector": ".fa-markdown:before", "properties": 0}, {"selector": ".fa-marker:before", "properties": 0}, {"selector": ".fa-mars:before", "properties": 0}, {"selector": ".fa-mars-double:before", "properties": 0}, {"selector": ".fa-mars-stroke:before", "properties": 0}, {"selector": ".fa-mars-stroke-h:before", "properties": 0}, {"selector": ".fa-mars-stroke-v:before", "properties": 0}, {"selector": ".fa-mask:before", "properties": 0}, {"selector": ".fa-mastodon:before", "properties": 0}, {"selector": ".fa-maxcdn:before", "properties": 0}, {"selector": ".fa-mdb:before", "properties": 0}, {"selector": ".fa-medal:before", "properties": 0}, {"selector": ".fa-medapps:before", "properties": 0}, {"selector": ".fa-medium:before", "properties": 0}, {"selector": ".fa-medium-m:before", "properties": 0}, {"selector": ".fa-medkit:before", "properties": 0}, {"selector": ".fa-medrt:before", "properties": 0}, {"selector": ".fa-meetup:before", "properties": 0}, {"selector": ".fa-megaport:before", "properties": 0}, {"selector": ".fa-meh:before", "properties": 0}, {"selector": ".fa-meh-blank:before", "properties": 0}, {"selector": ".fa-meh-rolling-eyes:before", "properties": 0}, {"selector": ".fa-memory:before", "properties": 0}, {"selector": ".fa-mendeley:before", "properties": 0}, {"selector": ".fa-menorah:before", "properties": 0}, {"selector": ".fa-mercury:before", "properties": 0}, {"selector": ".fa-meteor:before", "properties": 0}, {"selector": ".fa-microblog:before", "properties": 0}, {"selector": ".fa-microchip:before", "properties": 0}, {"selector": ".fa-microphone:before", "properties": 0}, {"selector": ".fa-microphone-alt:before", "properties": 0}, {"selector": ".fa-microphone-alt-slash:before", "properties": 0}, {"selector": ".fa-microphone-slash:before", "properties": 0}, {"selector": ".fa-microscope:before", "properties": 0}, {"selector": ".fa-microsoft:before", "properties": 0}, {"selector": ".fa-minus:before", "properties": 0}, {"selector": ".fa-minus-circle:before", "properties": 0}, {"selector": ".fa-minus-square:before", "properties": 0}, {"selector": ".fa-mitten:before", "properties": 0}, {"selector": ".fa-mix:before", "properties": 0}, {"selector": ".fa-mixcloud:before", "properties": 0}, {"selector": ".fa-mixer:before", "properties": 0}, {"selector": ".fa-mizu<PERSON>:before", "properties": 0}, {"selector": ".fa-mobile:before", "properties": 0}, {"selector": ".fa-mobile-alt:before", "properties": 0}, {"selector": ".fa-modx:before", "properties": 0}, {"selector": ".fa-monero:before", "properties": 0}, {"selector": ".fa-money-bill:before", "properties": 0}, {"selector": ".fa-money-bill-alt:before", "properties": 0}, {"selector": ".fa-money-bill-wave:before", "properties": 0}, {"selector": ".fa-money-bill-wave-alt:before", "properties": 0}, {"selector": ".fa-money-check:before", "properties": 0}, {"selector": ".fa-money-check-alt:before", "properties": 0}, {"selector": ".fa-monument:before", "properties": 0}, {"selector": ".fa-moon:before", "properties": 0}, {"selector": ".fa-mortar-pestle:before", "properties": 0}, {"selector": ".fa-mosque:before", "properties": 0}, {"selector": ".fa-motorcycle:before", "properties": 0}, {"selector": ".fa-mountain:before", "properties": 0}, {"selector": ".fa-mouse:before", "properties": 0}, {"selector": ".fa-mouse-pointer:before", "properties": 0}, {"selector": ".fa-mug-hot:before", "properties": 0}, {"selector": ".fa-music:before", "properties": 0}, {"selector": ".fa-napster:before", "properties": 0}, {"selector": ".fa-neos:before", "properties": 0}, {"selector": ".fa-network-wired:before", "properties": 0}, {"selector": ".fa-neuter:before", "properties": 0}, {"selector": ".fa-newspaper:before", "properties": 0}, {"selector": ".fa-nimblr:before", "properties": 0}, {"selector": ".fa-node:before", "properties": 0}, {"selector": ".fa-node-js:before", "properties": 0}, {"selector": ".fa-not-equal:before", "properties": 0}, {"selector": ".fa-notes-medical:before", "properties": 0}, {"selector": ".fa-npm:before", "properties": 0}, {"selector": ".fa-ns8:before", "properties": 0}, {"selector": ".fa-nutritionix:before", "properties": 0}, {"selector": ".fa-object-group:before", "properties": 0}, {"selector": ".fa-object-ungroup:before", "properties": 0}, {"selector": ".fa-octopus-deploy:before", "properties": 0}, {"selector": ".fa-odnoklassniki:before", "properties": 0}, {"selector": ".fa-odnoklassniki-square:before", "properties": 0}, {"selector": ".fa-oil-can:before", "properties": 0}, {"selector": ".fa-old-republic:before", "properties": 0}, {"selector": ".fa-om:before", "properties": 0}, {"selector": ".fa-opencart:before", "properties": 0}, {"selector": ".fa-openid:before", "properties": 0}, {"selector": ".fa-opera:before", "properties": 0}, {"selector": ".fa-optin-monster:before", "properties": 0}, {"selector": ".fa-orcid:before", "properties": 0}, {"selector": ".fa-osi:before", "properties": 0}, {"selector": ".fa-otter:before", "properties": 0}, {"selector": ".fa-outdent:before", "properties": 0}, {"selector": ".fa-page4:before", "properties": 0}, {"selector": ".fa-pagelines:before", "properties": 0}, {"selector": ".fa-pager:before", "properties": 0}, {"selector": ".fa-paint-brush:before", "properties": 0}, {"selector": ".fa-paint-roller:before", "properties": 0}, {"selector": ".fa-palette:before", "properties": 0}, {"selector": ".fa-palfed:before", "properties": 0}, {"selector": ".fa-pallet:before", "properties": 0}, {"selector": ".fa-paper-plane:before", "properties": 0}, {"selector": ".fa-paperclip:before", "properties": 0}, {"selector": ".fa-parachute-box:before", "properties": 0}, {"selector": ".fa-paragraph:before", "properties": 0}, {"selector": ".fa-parking:before", "properties": 0}, {"selector": ".fa-passport:before", "properties": 0}, {"selector": ".fa-pastafarianism:before", "properties": 0}, {"selector": ".fa-paste:before", "properties": 0}, {"selector": ".fa-patreon:before", "properties": 0}, {"selector": ".fa-pause:before", "properties": 0}, {"selector": ".fa-pause-circle:before", "properties": 0}, {"selector": ".fa-paw:before", "properties": 0}, {"selector": ".fa-paypal:before", "properties": 0}, {"selector": ".fa-peace:before", "properties": 0}, {"selector": ".fa-pen:before", "properties": 0}, {"selector": ".fa-pen-alt:before", "properties": 0}, {"selector": ".fa-pen-fancy:before", "properties": 0}, {"selector": ".fa-pen-nib:before", "properties": 0}, {"selector": ".fa-pen-square:before", "properties": 0}, {"selector": ".fa-pencil-alt:before", "properties": 0}, {"selector": ".fa-pencil-ruler:before", "properties": 0}, {"selector": ".fa-penny-arcade:before", "properties": 0}, {"selector": ".fa-people-arrows:before", "properties": 0}, {"selector": ".fa-people-carry:before", "properties": 0}, {"selector": ".fa-pepper-hot:before", "properties": 0}, {"selector": ".fa-perbyte:before", "properties": 0}, {"selector": ".fa-percent:before", "properties": 0}, {"selector": ".fa-percentage:before", "properties": 0}, {"selector": ".fa-periscope:before", "properties": 0}, {"selector": ".fa-person-booth:before", "properties": 0}, {"selector": ".fa-phabricator:before", "properties": 0}, {"selector": ".fa-phoenix-framework:before", "properties": 0}, {"selector": ".fa-phoenix-squadron:before", "properties": 0}, {"selector": ".fa-phone:before", "properties": 0}, {"selector": ".fa-phone-alt:before", "properties": 0}, {"selector": ".fa-phone-slash:before", "properties": 0}, {"selector": ".fa-phone-square:before", "properties": 0}, {"selector": ".fa-phone-square-alt:before", "properties": 0}, {"selector": ".fa-phone-volume:before", "properties": 0}, {"selector": ".fa-photo-video:before", "properties": 0}, {"selector": ".fa-php:before", "properties": 0}, {"selector": ".fa-pied-piper:before", "properties": 0}, {"selector": ".fa-pied-piper-alt:before", "properties": 0}, {"selector": ".fa-pied-piper-hat:before", "properties": 0}, {"selector": ".fa-pied-piper-pp:before", "properties": 0}, {"selector": ".fa-pied-piper-square:before", "properties": 0}, {"selector": ".fa-piggy-bank:before", "properties": 0}, {"selector": ".fa-pills:before", "properties": 0}, {"selector": ".fa-pinterest:before", "properties": 0}, {"selector": ".fa-pinterest-p:before", "properties": 0}, {"selector": ".fa-pinterest-square:before", "properties": 0}, {"selector": ".fa-pizza-slice:before", "properties": 0}, {"selector": ".fa-place-of-worship:before", "properties": 0}, {"selector": ".fa-plane:before", "properties": 0}, {"selector": ".fa-plane-arrival:before", "properties": 0}, {"selector": ".fa-plane-departure:before", "properties": 0}, {"selector": ".fa-plane-slash:before", "properties": 0}, {"selector": ".fa-play:before", "properties": 0}, {"selector": ".fa-play-circle:before", "properties": 0}, {"selector": ".fa-playstation:before", "properties": 0}, {"selector": ".fa-plug:before", "properties": 0}, {"selector": ".fa-plus:before", "properties": 0}, {"selector": ".fa-plus-circle:before", "properties": 0}, {"selector": ".fa-plus-square:before", "properties": 0}, {"selector": ".fa-podcast:before", "properties": 0}, {"selector": ".fa-poll:before", "properties": 0}, {"selector": ".fa-poll-h:before", "properties": 0}, {"selector": ".fa-poo:before", "properties": 0}, {"selector": ".fa-poo-storm:before", "properties": 0}, {"selector": ".fa-poop:before", "properties": 0}, {"selector": ".fa-portrait:before", "properties": 0}, {"selector": ".fa-pound-sign:before", "properties": 0}, {"selector": ".fa-power-off:before", "properties": 0}, {"selector": ".fa-pray:before", "properties": 0}, {"selector": ".fa-praying-hands:before", "properties": 0}, {"selector": ".fa-prescription:before", "properties": 0}, {"selector": ".fa-prescription-bottle:before", "properties": 0}, {"selector": ".fa-prescription-bottle-alt:before", "properties": 0}, {"selector": ".fa-print:before", "properties": 0}, {"selector": ".fa-procedures:before", "properties": 0}, {"selector": ".fa-product-hunt:before", "properties": 0}, {"selector": ".fa-project-diagram:before", "properties": 0}, {"selector": ".fa-pump-medical:before", "properties": 0}, {"selector": ".fa-pump-soap:before", "properties": 0}, {"selector": ".fa-pushed:before", "properties": 0}, {"selector": ".fa-puzzle-piece:before", "properties": 0}, {"selector": ".fa-python:before", "properties": 0}, {"selector": ".fa-qq:before", "properties": 0}, {"selector": ".fa-qrcode:before", "properties": 0}, {"selector": ".fa-question:before", "properties": 0}, {"selector": ".fa-question-circle:before", "properties": 0}, {"selector": ".fa-quidditch:before", "properties": 0}, {"selector": ".fa-quinscape:before", "properties": 0}, {"selector": ".fa-quora:before", "properties": 0}, {"selector": ".fa-quote-left:before", "properties": 0}, {"selector": ".fa-quote-right:before", "properties": 0}, {"selector": ".fa-quran:before", "properties": 0}, {"selector": ".fa-r-project:before", "properties": 0}, {"selector": ".fa-radiation:before", "properties": 0}, {"selector": ".fa-radiation-alt:before", "properties": 0}, {"selector": ".fa-rainbow:before", "properties": 0}, {"selector": ".fa-random:before", "properties": 0}, {"selector": ".fa-raspberry-pi:before", "properties": 0}, {"selector": ".fa-ravelry:before", "properties": 0}, {"selector": ".fa-react:before", "properties": 0}, {"selector": ".fa-reacteurope:before", "properties": 0}, {"selector": ".fa-readme:before", "properties": 0}, {"selector": ".fa-rebel:before", "properties": 0}, {"selector": ".fa-receipt:before", "properties": 0}, {"selector": ".fa-record-vinyl:before", "properties": 0}, {"selector": ".fa-recycle:before", "properties": 0}, {"selector": ".fa-red-river:before", "properties": 0}, {"selector": ".fa-reddit:before", "properties": 0}, {"selector": ".fa-reddit-alien:before", "properties": 0}, {"selector": ".fa-reddit-square:before", "properties": 0}, {"selector": ".fa-redhat:before", "properties": 0}, {"selector": ".fa-redo:before", "properties": 0}, {"selector": ".fa-redo-alt:before", "properties": 0}, {"selector": ".fa-registered:before", "properties": 0}, {"selector": ".fa-remove-format:before", "properties": 0}, {"selector": ".fa-renren:before", "properties": 0}, {"selector": ".fa-reply:before", "properties": 0}, {"selector": ".fa-reply-all:before", "properties": 0}, {"selector": ".fa-replyd:before", "properties": 0}, {"selector": ".fa-republican:before", "properties": 0}, {"selector": ".fa-researchgate:before", "properties": 0}, {"selector": ".fa-resolving:before", "properties": 0}, {"selector": ".fa-restroom:before", "properties": 0}, {"selector": ".fa-retweet:before", "properties": 0}, {"selector": ".fa-rev:before", "properties": 0}, {"selector": ".fa-ribbon:before", "properties": 0}, {"selector": ".fa-ring:before", "properties": 0}, {"selector": ".fa-road:before", "properties": 0}, {"selector": ".fa-robot:before", "properties": 0}, {"selector": ".fa-rocket:before", "properties": 0}, {"selector": ".fa-rocketchat:before", "properties": 0}, {"selector": ".fa-rockrms:before", "properties": 0}, {"selector": ".fa-route:before", "properties": 0}, {"selector": ".fa-rss:before", "properties": 0}, {"selector": ".fa-rss-square:before", "properties": 0}, {"selector": ".fa-ruble-sign:before", "properties": 0}, {"selector": ".fa-ruler:before", "properties": 0}, {"selector": ".fa-ruler-combined:before", "properties": 0}, {"selector": ".fa-ruler-horizontal:before", "properties": 0}, {"selector": ".fa-ruler-vertical:before", "properties": 0}, {"selector": ".fa-running:before", "properties": 0}, {"selector": ".fa-rupee-sign:before", "properties": 0}, {"selector": ".fa-rust:before", "properties": 0}, {"selector": ".fa-sad-cry:before", "properties": 0}, {"selector": ".fa-sad-tear:before", "properties": 0}, {"selector": ".fa-safari:before", "properties": 0}, {"selector": ".fa-salesforce:before", "properties": 0}, {"selector": ".fa-sass:before", "properties": 0}, {"selector": ".fa-satellite:before", "properties": 0}, {"selector": ".fa-satellite-dish:before", "properties": 0}, {"selector": ".fa-save:before", "properties": 0}, {"selector": ".fa-schlix:before", "properties": 0}, {"selector": ".fa-school:before", "properties": 0}, {"selector": ".fa-screwdriver:before", "properties": 0}, {"selector": ".fa-scribd:before", "properties": 0}, {"selector": ".fa-scroll:before", "properties": 0}, {"selector": ".fa-sd-card:before", "properties": 0}, {"selector": ".fa-search:before", "properties": 0}, {"selector": ".fa-search-dollar:before", "properties": 0}, {"selector": ".fa-search-location:before", "properties": 0}, {"selector": ".fa-search-minus:before", "properties": 0}, {"selector": ".fa-search-plus:before", "properties": 0}, {"selector": ".fa-searchengin:before", "properties": 0}, {"selector": ".fa-seedling:before", "properties": 0}, {"selector": ".fa-sellcast:before", "properties": 0}, {"selector": ".fa-sellsy:before", "properties": 0}, {"selector": ".fa-server:before", "properties": 0}, {"selector": ".fa-servicestack:before", "properties": 0}, {"selector": ".fa-shapes:before", "properties": 0}, {"selector": ".fa-share:before", "properties": 0}, {"selector": ".fa-share-alt:before", "properties": 0}, {"selector": ".fa-share-alt-square:before", "properties": 0}, {"selector": ".fa-share-square:before", "properties": 0}, {"selector": ".fa-shekel-sign:before", "properties": 0}, {"selector": ".fa-shield-alt:before", "properties": 0}, {"selector": ".fa-shield-virus:before", "properties": 0}, {"selector": ".fa-ship:before", "properties": 0}, {"selector": ".fa-shipping-fast:before", "properties": 0}, {"selector": ".fa-shirtsinbulk:before", "properties": 0}, {"selector": ".fa-shoe-prints:before", "properties": 0}, {"selector": ".fa-shopify:before", "properties": 0}, {"selector": ".fa-shopping-bag:before", "properties": 0}, {"selector": ".fa-shopping-basket:before", "properties": 0}, {"selector": ".fa-shopping-cart:before", "properties": 0}, {"selector": ".fa-shopware:before", "properties": 0}, {"selector": ".fa-shower:before", "properties": 0}, {"selector": ".fa-shuttle-van:before", "properties": 0}, {"selector": ".fa-sign:before", "properties": 0}, {"selector": ".fa-sign-in-alt:before", "properties": 0}, {"selector": ".fa-sign-language:before", "properties": 0}, {"selector": ".fa-sign-out-alt:before", "properties": 0}, {"selector": ".fa-signal:before", "properties": 0}, {"selector": ".fa-signature:before", "properties": 0}, {"selector": ".fa-sim-card:before", "properties": 0}, {"selector": ".fa-simplybuilt:before", "properties": 0}, {"selector": ".fa-sink:before", "properties": 0}, {"selector": ".fa-sistrix:before", "properties": 0}, {"selector": ".fa-sitemap:before", "properties": 0}, {"selector": ".fa-sith:before", "properties": 0}, {"selector": ".fa-skating:before", "properties": 0}, {"selector": ".fa-sketch:before", "properties": 0}, {"selector": ".fa-skiing:before", "properties": 0}, {"selector": ".fa-skiing-nordic:before", "properties": 0}, {"selector": ".fa-skull:before", "properties": 0}, {"selector": ".fa-skull-crossbones:before", "properties": 0}, {"selector": ".fa-skyatlas:before", "properties": 0}, {"selector": ".fa-skype:before", "properties": 0}, {"selector": ".fa-slack:before", "properties": 0}, {"selector": ".fa-slack-hash:before", "properties": 0}, {"selector": ".fa-slash:before", "properties": 0}, {"selector": ".fa-sleigh:before", "properties": 0}, {"selector": ".fa-sliders-h:before", "properties": 0}, {"selector": ".fa-slideshare:before", "properties": 0}, {"selector": ".fa-smile:before", "properties": 0}, {"selector": ".fa-smile-beam:before", "properties": 0}, {"selector": ".fa-smile-wink:before", "properties": 0}, {"selector": ".fa-smog:before", "properties": 0}, {"selector": ".fa-smoking:before", "properties": 0}, {"selector": ".fa-smoking-ban:before", "properties": 0}, {"selector": ".fa-sms:before", "properties": 0}, {"selector": ".fa-snapchat:before", "properties": 0}, {"selector": ".fa-snapchat-ghost:before", "properties": 0}, {"selector": ".fa-snapchat-square:before", "properties": 0}, {"selector": ".fa-snowboarding:before", "properties": 0}, {"selector": ".fa-snowflake:before", "properties": 0}, {"selector": ".fa-snowman:before", "properties": 0}, {"selector": ".fa-snowplow:before", "properties": 0}, {"selector": ".fa-soap:before", "properties": 0}, {"selector": ".fa-socks:before", "properties": 0}, {"selector": ".fa-solar-panel:before", "properties": 0}, {"selector": ".fa-sort:before", "properties": 0}, {"selector": ".fa-sort-alpha-down:before", "properties": 0}, {"selector": ".fa-sort-alpha-down-alt:before", "properties": 0}, {"selector": ".fa-sort-alpha-up:before", "properties": 0}, {"selector": ".fa-sort-alpha-up-alt:before", "properties": 0}, {"selector": ".fa-sort-amount-down:before", "properties": 0}, {"selector": ".fa-sort-amount-down-alt:before", "properties": 0}, {"selector": ".fa-sort-amount-up:before", "properties": 0}, {"selector": ".fa-sort-amount-up-alt:before", "properties": 0}, {"selector": ".fa-sort-down:before", "properties": 0}, {"selector": ".fa-sort-numeric-down:before", "properties": 0}, {"selector": ".fa-sort-numeric-down-alt:before", "properties": 0}, {"selector": ".fa-sort-numeric-up:before", "properties": 0}, {"selector": ".fa-sort-numeric-up-alt:before", "properties": 0}, {"selector": ".fa-sort-up:before", "properties": 0}, {"selector": ".fa-soundcloud:before", "properties": 0}, {"selector": ".fa-sourcetree:before", "properties": 0}, {"selector": ".fa-spa:before", "properties": 0}, {"selector": ".fa-space-shuttle:before", "properties": 0}, {"selector": ".fa-speakap:before", "properties": 0}, {"selector": ".fa-speaker-deck:before", "properties": 0}, {"selector": ".fa-spell-check:before", "properties": 0}, {"selector": ".fa-spider:before", "properties": 0}, {"selector": ".fa-spinner:before", "properties": 0}, {"selector": ".fa-splotch:before", "properties": 0}, {"selector": ".fa-spotify:before", "properties": 0}, {"selector": ".fa-spray-can:before", "properties": 0}, {"selector": ".fa-square:before", "properties": 0}, {"selector": ".fa-square-full:before", "properties": 0}, {"selector": ".fa-square-root-alt:before", "properties": 0}, {"selector": ".fa-squarespace:before", "properties": 0}, {"selector": ".fa-stack-exchange:before", "properties": 0}, {"selector": ".fa-stack-overflow:before", "properties": 0}, {"selector": ".fa-stackpath:before", "properties": 0}, {"selector": ".fa-stamp:before", "properties": 0}, {"selector": ".fa-star:before", "properties": 0}, {"selector": ".fa-star-and-crescent:before", "properties": 0}, {"selector": ".fa-star-half:before", "properties": 0}, {"selector": ".fa-star-half-alt:before", "properties": 0}, {"selector": ".fa-star-of-david:before", "properties": 0}, {"selector": ".fa-star-of-life:before", "properties": 0}, {"selector": ".fa-staylinked:before", "properties": 0}, {"selector": ".fa-steam:before", "properties": 0}, {"selector": ".fa-steam-square:before", "properties": 0}, {"selector": ".fa-steam-symbol:before", "properties": 0}, {"selector": ".fa-step-backward:before", "properties": 0}, {"selector": ".fa-step-forward:before", "properties": 0}, {"selector": ".fa-stethoscope:before", "properties": 0}, {"selector": ".fa-sticker-mule:before", "properties": 0}, {"selector": ".fa-sticky-note:before", "properties": 0}, {"selector": ".fa-stop:before", "properties": 0}, {"selector": ".fa-stop-circle:before", "properties": 0}, {"selector": ".fa-stopwatch:before", "properties": 0}, {"selector": ".fa-stopwatch-20:before", "properties": 0}, {"selector": ".fa-store:before", "properties": 0}, {"selector": ".fa-store-alt:before", "properties": 0}, {"selector": ".fa-store-alt-slash:before", "properties": 0}, {"selector": ".fa-store-slash:before", "properties": 0}, {"selector": ".fa-strava:before", "properties": 0}, {"selector": ".fa-stream:before", "properties": 0}, {"selector": ".fa-street-view:before", "properties": 0}, {"selector": ".fa-strikethrough:before", "properties": 0}, {"selector": ".fa-stripe:before", "properties": 0}, {"selector": ".fa-stripe-s:before", "properties": 0}, {"selector": ".fa-stroopwafel:before", "properties": 0}, {"selector": ".fa-studiovinari:before", "properties": 0}, {"selector": ".fa-stumbleupon:before", "properties": 0}, {"selector": ".fa-stumbleupon-circle:before", "properties": 0}, {"selector": ".fa-subscript:before", "properties": 0}, {"selector": ".fa-subway:before", "properties": 0}, {"selector": ".fa-suitcase:before", "properties": 0}, {"selector": ".fa-suitcase-rolling:before", "properties": 0}, {"selector": ".fa-sun:before", "properties": 0}, {"selector": ".fa-superpowers:before", "properties": 0}, {"selector": ".fa-superscript:before", "properties": 0}, {"selector": ".fa-supple:before", "properties": 0}, {"selector": ".fa-surprise:before", "properties": 0}, {"selector": ".fa-suse:before", "properties": 0}, {"selector": ".fa-swatchbook:before", "properties": 0}, {"selector": ".fa-swift:before", "properties": 0}, {"selector": ".fa-swimmer:before", "properties": 0}, {"selector": ".fa-swimming-pool:before", "properties": 0}, {"selector": ".fa-symfony:before", "properties": 0}, {"selector": ".fa-synagogue:before", "properties": 0}, {"selector": ".fa-sync:before", "properties": 0}, {"selector": ".fa-sync-alt:before", "properties": 0}, {"selector": ".fa-syringe:before", "properties": 0}, {"selector": ".fa-table:before", "properties": 0}, {"selector": ".fa-table-tennis:before", "properties": 0}, {"selector": ".fa-tablet:before", "properties": 0}, {"selector": ".fa-tablet-alt:before", "properties": 0}, {"selector": ".fa-tablets:before", "properties": 0}, {"selector": ".fa-tachometer-alt:before", "properties": 0}, {"selector": ".fa-tag:before", "properties": 0}, {"selector": ".fa-tags:before", "properties": 0}, {"selector": ".fa-tape:before", "properties": 0}, {"selector": ".fa-tasks:before", "properties": 0}, {"selector": ".fa-taxi:before", "properties": 0}, {"selector": ".fa-teamspeak:before", "properties": 0}, {"selector": ".fa-teeth:before", "properties": 0}, {"selector": ".fa-teeth-open:before", "properties": 0}, {"selector": ".fa-telegram:before", "properties": 0}, {"selector": ".fa-telegram-plane:before", "properties": 0}, {"selector": ".fa-temperature-high:before", "properties": 0}, {"selector": ".fa-temperature-low:before", "properties": 0}, {"selector": ".fa-tencent-weibo:before", "properties": 0}, {"selector": ".fa-tenge:before", "properties": 0}, {"selector": ".fa-terminal:before", "properties": 0}, {"selector": ".fa-text-height:before", "properties": 0}, {"selector": ".fa-text-width:before", "properties": 0}, {"selector": ".fa-th:before", "properties": 0}, {"selector": ".fa-th-large:before", "properties": 0}, {"selector": ".fa-th-list:before", "properties": 0}, {"selector": ".fa-the-red-yeti:before", "properties": 0}, {"selector": ".fa-theater-masks:before", "properties": 0}, {"selector": ".fa-themeco:before", "properties": 0}, {"selector": ".fa-themeisle:before", "properties": 0}, {"selector": ".fa-thermometer:before", "properties": 0}, {"selector": ".fa-thermometer-empty:before", "properties": 0}, {"selector": ".fa-thermometer-full:before", "properties": 0}, {"selector": ".fa-thermometer-half:before", "properties": 0}, {"selector": ".fa-thermometer-quarter:before", "properties": 0}, {"selector": ".fa-thermometer-three-quarters:before", "properties": 0}, {"selector": ".fa-think-peaks:before", "properties": 0}, {"selector": ".fa-thumbs-down:before", "properties": 0}, {"selector": ".fa-thumbs-up:before", "properties": 0}, {"selector": ".fa-thumbtack:before", "properties": 0}, {"selector": ".fa-ticket-alt:before", "properties": 0}, {"selector": ".fa-tiktok:before", "properties": 0}, {"selector": ".fa-times:before", "properties": 0}, {"selector": ".fa-times-circle:before", "properties": 0}, {"selector": ".fa-tint:before", "properties": 0}, {"selector": ".fa-tint-slash:before", "properties": 0}, {"selector": ".fa-tired:before", "properties": 0}, {"selector": ".fa-toggle-off:before", "properties": 0}, {"selector": ".fa-toggle-on:before", "properties": 0}, {"selector": ".fa-toilet:before", "properties": 0}, {"selector": ".fa-toilet-paper:before", "properties": 0}, {"selector": ".fa-toilet-paper-slash:before", "properties": 0}, {"selector": ".fa-toolbox:before", "properties": 0}, {"selector": ".fa-tools:before", "properties": 0}, {"selector": ".fa-tooth:before", "properties": 0}, {"selector": ".fa-torah:before", "properties": 0}, {"selector": ".fa-torii-gate:before", "properties": 0}, {"selector": ".fa-tractor:before", "properties": 0}, {"selector": ".fa-trade-federation:before", "properties": 0}, {"selector": ".fa-trademark:before", "properties": 0}, {"selector": ".fa-traffic-light:before", "properties": 0}, {"selector": ".fa-trailer:before", "properties": 0}, {"selector": ".fa-train:before", "properties": 0}, {"selector": ".fa-tram:before", "properties": 0}, {"selector": ".fa-transgender:before", "properties": 0}, {"selector": ".fa-transgender-alt:before", "properties": 0}, {"selector": ".fa-trash:before", "properties": 0}, {"selector": ".fa-trash-alt:before", "properties": 0}, {"selector": ".fa-trash-restore:before", "properties": 0}, {"selector": ".fa-trash-restore-alt:before", "properties": 0}, {"selector": ".fa-tree:before", "properties": 0}, {"selector": ".fa-trello:before", "properties": 0}, {"selector": ".fa-trophy:before", "properties": 0}, {"selector": ".fa-truck:before", "properties": 0}, {"selector": ".fa-truck-loading:before", "properties": 0}, {"selector": ".fa-truck-monster:before", "properties": 0}, {"selector": ".fa-truck-moving:before", "properties": 0}, {"selector": ".fa-truck-pickup:before", "properties": 0}, {"selector": ".fa-tshirt:before", "properties": 0}, {"selector": ".fa-tty:before", "properties": 0}, {"selector": ".fa-tumblr:before", "properties": 0}, {"selector": ".fa-tumblr-square:before", "properties": 0}, {"selector": ".fa-tv:before", "properties": 0}, {"selector": ".fa-twitch:before", "properties": 0}, {"selector": ".fa-twitter:before", "properties": 0}, {"selector": ".fa-twitter-square:before", "properties": 0}, {"selector": ".fa-typo3:before", "properties": 0}, {"selector": ".fa-uber:before", "properties": 0}, {"selector": ".fa-ubuntu:before", "properties": 0}, {"selector": ".fa-uikit:before", "properties": 0}, {"selector": ".fa-umbraco:before", "properties": 0}, {"selector": ".fa-umbrella:before", "properties": 0}, {"selector": ".fa-umbrella-beach:before", "properties": 0}, {"selector": ".fa-uncharted:before", "properties": 0}, {"selector": ".fa-underline:before", "properties": 0}, {"selector": ".fa-undo:before", "properties": 0}, {"selector": ".fa-undo-alt:before", "properties": 0}, {"selector": ".fa-uniregistry:before", "properties": 0}, {"selector": ".fa-unity:before", "properties": 0}, {"selector": ".fa-universal-access:before", "properties": 0}, {"selector": ".fa-university:before", "properties": 0}, {"selector": ".fa-unlink:before", "properties": 0}, {"selector": ".fa-unlock:before", "properties": 0}, {"selector": ".fa-unlock-alt:before", "properties": 0}, {"selector": ".fa-unsplash:before", "properties": 0}, {"selector": ".fa-untappd:before", "properties": 0}, {"selector": ".fa-upload:before", "properties": 0}, {"selector": ".fa-ups:before", "properties": 0}, {"selector": ".fa-usb:before", "properties": 0}, {"selector": ".fa-user:before", "properties": 0}, {"selector": ".fa-user-alt:before", "properties": 0}, {"selector": ".fa-user-alt-slash:before", "properties": 0}, {"selector": ".fa-user-astronaut:before", "properties": 0}, {"selector": ".fa-user-check:before", "properties": 0}, {"selector": ".fa-user-circle:before", "properties": 0}, {"selector": ".fa-user-clock:before", "properties": 0}, {"selector": ".fa-user-cog:before", "properties": 0}, {"selector": ".fa-user-edit:before", "properties": 0}, {"selector": ".fa-user-friends:before", "properties": 0}, {"selector": ".fa-user-graduate:before", "properties": 0}, {"selector": ".fa-user-injured:before", "properties": 0}, {"selector": ".fa-user-lock:before", "properties": 0}, {"selector": ".fa-user-md:before", "properties": 0}, {"selector": ".fa-user-minus:before", "properties": 0}, {"selector": ".fa-user-ninja:before", "properties": 0}, {"selector": ".fa-user-nurse:before", "properties": 0}, {"selector": ".fa-user-plus:before", "properties": 0}, {"selector": ".fa-user-secret:before", "properties": 0}, {"selector": ".fa-user-shield:before", "properties": 0}, {"selector": ".fa-user-slash:before", "properties": 0}, {"selector": ".fa-user-tag:before", "properties": 0}, {"selector": ".fa-user-tie:before", "properties": 0}, {"selector": ".fa-user-times:before", "properties": 0}, {"selector": ".fa-users:before", "properties": 0}, {"selector": ".fa-users-cog:before", "properties": 0}, {"selector": ".fa-users-slash:before", "properties": 0}, {"selector": ".fa-usps:before", "properties": 0}, {"selector": ".fa-ussunnah:before", "properties": 0}, {"selector": ".fa-utensil-spoon:before", "properties": 0}, {"selector": ".fa-utensils:before", "properties": 0}, {"selector": ".fa-vaadin:before", "properties": 0}, {"selector": ".fa-vector-square:before", "properties": 0}, {"selector": ".fa-venus:before", "properties": 0}, {"selector": ".fa-venus-double:before", "properties": 0}, {"selector": ".fa-venus-mars:before", "properties": 0}, {"selector": ".fa-vest:before", "properties": 0}, {"selector": ".fa-vest-patches:before", "properties": 0}, {"selector": ".fa-viacoin:before", "properties": 0}, {"selector": ".fa-viadeo:before", "properties": 0}, {"selector": ".fa-viadeo-square:before", "properties": 0}, {"selector": ".fa-vial:before", "properties": 0}, {"selector": ".fa-vials:before", "properties": 0}, {"selector": ".fa-viber:before", "properties": 0}, {"selector": ".fa-video:before", "properties": 0}, {"selector": ".fa-video-slash:before", "properties": 0}, {"selector": ".fa-vihara:before", "properties": 0}, {"selector": ".fa-vimeo:before", "properties": 0}, {"selector": ".fa-vimeo-square:before", "properties": 0}, {"selector": ".fa-vimeo-v:before", "properties": 0}, {"selector": ".fa-vine:before", "properties": 0}, {"selector": ".fa-virus:before", "properties": 0}, {"selector": ".fa-virus-slash:before", "properties": 0}, {"selector": ".fa-viruses:before", "properties": 0}, {"selector": ".fa-vk:before", "properties": 0}, {"selector": ".fa-vnv:before", "properties": 0}, {"selector": ".fa-voicemail:before", "properties": 0}, {"selector": ".fa-volleyball-ball:before", "properties": 0}, {"selector": ".fa-volume-down:before", "properties": 0}, {"selector": ".fa-volume-mute:before", "properties": 0}, {"selector": ".fa-volume-off:before", "properties": 0}, {"selector": ".fa-volume-up:before", "properties": 0}, {"selector": ".fa-vote-yea:before", "properties": 0}, {"selector": ".fa-vr-cardboard:before", "properties": 0}, {"selector": ".fa-vuejs:before", "properties": 0}, {"selector": ".fa-walking:before", "properties": 0}, {"selector": ".fa-wallet:before", "properties": 0}, {"selector": ".fa-warehouse:before", "properties": 0}, {"selector": ".fa-watchman-monitoring:before", "properties": 0}, {"selector": ".fa-water:before", "properties": 0}, {"selector": ".fa-wave-square:before", "properties": 0}, {"selector": ".fa-waze:before", "properties": 0}, {"selector": ".fa-weebly:before", "properties": 0}, {"selector": ".fa-weibo:before", "properties": 0}, {"selector": ".fa-weight:before", "properties": 0}, {"selector": ".fa-weight-hanging:before", "properties": 0}, {"selector": ".fa-weixin:before", "properties": 0}, {"selector": ".fa-whatsapp:before", "properties": 0}, {"selector": ".fa-whatsapp-square:before", "properties": 0}, {"selector": ".fa-wheelchair:before", "properties": 0}, {"selector": ".fa-whmcs:before", "properties": 0}, {"selector": ".fa-wifi:before", "properties": 0}, {"selector": ".fa-wikipedia-w:before", "properties": 0}, {"selector": ".fa-wind:before", "properties": 0}, {"selector": ".fa-window-close:before", "properties": 0}, {"selector": ".fa-window-maximize:before", "properties": 0}, {"selector": ".fa-window-minimize:before", "properties": 0}, {"selector": ".fa-window-restore:before", "properties": 0}, {"selector": ".fa-windows:before", "properties": 0}, {"selector": ".fa-wine-bottle:before", "properties": 0}, {"selector": ".fa-wine-glass:before", "properties": 0}, {"selector": ".fa-wine-glass-alt:before", "properties": 0}, {"selector": ".fa-wix:before", "properties": 0}, {"selector": ".fa-wizards-of-the-coast:before", "properties": 0}, {"selector": ".fa-wodu:before", "properties": 0}, {"selector": ".fa-wolf-pack-battalion:before", "properties": 0}, {"selector": ".fa-won-sign:before", "properties": 0}, {"selector": ".fa-wordpress:before", "properties": 0}, {"selector": ".fa-wordpress-simple:before", "properties": 0}, {"selector": ".fa-wpbeginner:before", "properties": 0}, {"selector": ".fa-wpexplorer:before", "properties": 0}, {"selector": ".fa-wpforms:before", "properties": 0}, {"selector": ".fa-wpressr:before", "properties": 0}, {"selector": ".fa-wrench:before", "properties": 0}, {"selector": ".fa-x-ray:before", "properties": 0}, {"selector": ".fa-xbox:before", "properties": 0}, {"selector": ".fa-xing:before", "properties": 0}, {"selector": ".fa-xing-square:before", "properties": 0}, {"selector": ".fa-y-combinator:before", "properties": 0}, {"selector": ".fa-yahoo:before", "properties": 0}, {"selector": ".fa-yammer:before", "properties": 0}, {"selector": ".fa-yandex:before", "properties": 0}, {"selector": ".fa-yandex-international:before", "properties": 0}, {"selector": ".fa-yarn:before", "properties": 0}, {"selector": ".fa-yelp:before", "properties": 0}, {"selector": ".fa-yen-sign:before", "properties": 0}, {"selector": ".fa-yin-yang:before", "properties": 0}, {"selector": ".fa-yoast:before", "properties": 0}, {"selector": ".fa-youtube:before", "properties": 0}, {"selector": ".fa-youtube-square:before", "properties": 0}, {"selector": ".fa-zhihu:before", "properties": 0}, {"selector": ".sr-only", "properties": 0}, {"selector": ".sr-only-focusable:active,.sr-only-focusable:focus", "properties": 0}, {"selector": "@font-face", "properties": 0}, {"selector": ".fab", "properties": 0}, {"selector": "@font-face", "properties": 0}, {"selector": ".fab,.far", "properties": 0}, {"selector": "@font-face", "properties": 0}, {"selector": ".fa,.far,.fas", "properties": 0}, {"selector": ".fa,.fas", "properties": 0}], "media_queries": [], "imports": [], "fonts": ["\"font awesome 5 brands\"", "\"font awesome 5 brands\"}@font-face{font-family:\"font awesome 5 free\"", "\"font awesome 5 free\"", "\"font awesome 5 free\"}.fa,.fas{font-weight:900}"], "colors": ["#eee", "#fff"], "size": 59305}, "flatsome.css": {"url": "https://vandamtour.vn/wp-content/themes/flatsome/assets/css/flatsome.css?ver=3.19.6", "selectors": [{"selector": "@charset \"utf-8\";html", "properties": 0}, {"selector": "body", "properties": 0}, {"selector": "article,aside,details,figcaption,figure,footer,header,main,menu,nav,section,summary", "properties": 0}, {"selector": "audio,canvas,progress,video", "properties": 0}, {"selector": "audio:not([controls])", "properties": 0}, {"selector": "progress", "properties": 0}, {"selector": "[hidden],template", "properties": 0}, {"selector": "a", "properties": 0}, {"selector": "a:active,a:hover", "properties": 0}, {"selector": "abbr[title]", "properties": 0}, {"selector": "b,strong", "properties": 0}, {"selector": "dfn", "properties": 0}, {"selector": "mark", "properties": 0}, {"selector": "small", "properties": 0}, {"selector": "img", "properties": 0}, {"selector": "svg:not(:root)", "properties": 0}, {"selector": "button,input,select,textarea", "properties": 0}, {"selector": "optgroup", "properties": 0}, {"selector": "button,input,select", "properties": 0}, {"selector": "button,select", "properties": 0}, {"selector": "[type=button],[type=reset],[type=submit],button", "properties": 0}, {"selector": "[disabled]", "properties": 0}, {"selector": "[type=reset],[type=submit],button,html [type=button]", "properties": 0}, {"selector": "button::-moz-focus-inner,input::-moz-focus-inner", "properties": 0}, {"selector": "button:-moz-focusring,input:-moz-focusring", "properties": 0}, {"selector": "fieldset", "properties": 0}, {"selector": "legend", "properties": 0}, {"selector": "textarea", "properties": 0}, {"selector": "[type=checkbox],[type=radio]", "properties": 0}, {"selector": "[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button", "properties": 0}, {"selector": "[type=search]", "properties": 0}, {"selector": "[type=search]::-webkit-search-cancel-button,[type=search]::-webkit-search-decoration", "properties": 0}, {"selector": "*,:after,:before,html", "properties": 0}, {"selector": "html", "properties": 0}, {"selector": "body", "properties": 0}, {"selector": ".xdebug-var-dump", "properties": 0}, {"selector": "hr", "properties": 0}, {"selector": "img", "properties": 0}, {"selector": "figure", "properties": 0}, {"selector": "pre", "properties": 0}, {"selector": "p:empty", "properties": 0}, {"selector": "a,button,input", "properties": 0}, {"selector": "ins", "properties": 0}, {"selector": "embed,iframe,object", "properties": 0}, {"selector": ".col,.columns,.gallery-item", "properties": 0}, {"selector": ".col-fit", "properties": 0}, {"selector": ".col-first", "properties": 0}, {"selector": ".col-last", "properties": 0}, {"selector": ".col-inner", "properties": 0}, {"selector": ".col-has-radius", "properties": 0}, {"selector": "@media screen and (min-width:850px)", "properties": 0}, {"selector": ".col:first-child .col-inner", "properties": 0}, {"selector": ".col+.col .col-inner", "properties": 0}, {"selector": "@media screen and (max-width:549px)", "properties": 0}, {"selector": ".small-col-first", "properties": 0}, {"selector": "@media screen and (min-width:850px)", "properties": 0}, {"selector": ".large-col-first", "properties": 0}, {"selector": "@media screen and (max-width:849px)", "properties": 0}, {"selector": ".medium-col-first", "properties": 0}, {"selector": ".col", "properties": 0}, {"selector": "@media screen and (min-width:850px)", "properties": 0}, {"selector": ".row-divided>.col+.col:not(.large-12)", "properties": 0}, {"selector": ".row-divided.row-reverse>.col+.col:not(.large-12)", "properties": 0}, {"selector": ".col-divided", "properties": 0}, {"selector": ".col.col-divided:not(.col-first):last-child", "properties": 0}, {"selector": ".col-border", "properties": 0}, {"selector": ".col-border+.col,.col-divided+.col", "properties": 0}, {"selector": ".dark .col-divided", "properties": 0}, {"selector": ".align-equal>.col", "properties": 0}, {"selector": ".align-middle", "properties": 0}, {"selector": ".align-bottom", "properties": 0}, {"selector": ".align-top", "properties": 0}, {"selector": ".align-center", "properties": 0}, {"selector": ".align-right", "properties": 0}, {"selector": ".small-1", "properties": 0}, {"selector": ".small-2", "properties": 0}, {"selector": ".small-3", "properties": 0}, {"selector": ".small-4", "properties": 0}, {"selector": ".small-5", "properties": 0}, {"selector": ".small-6", "properties": 0}, {"selector": ".small-7", "properties": 0}, {"selector": ".small-8", "properties": 0}, {"selector": ".small-9", "properties": 0}, {"selector": ".small-10", "properties": 0}, {"selector": ".small-11", "properties": 0}, {"selector": ".small-12,.small-columns-1 .flickity-slider>.col,.small-columns-1>.col", "properties": 0}, {"selector": ".small-columns-2 .flickity-slider>.col,.small-columns-2>.col", "properties": 0}, {"selector": ".small-columns-3 .flickity-slider>.col,.small-columns-3>.col", "properties": 0}, {"selector": ".small-columns-4 .flickity-slider>.col,.small-columns-4>.col", "properties": 0}, {"selector": ".small-columns-5 .flickity-slider>.col,.small-columns-5>.col", "properties": 0}, {"selector": ".small-columns-6 .flickity-slider>.col,.small-columns-6>.col", "properties": 0}, {"selector": ".small-columns-7 .flickity-slider>.col,.small-columns-7>.col", "properties": 0}, {"selector": ".small-columns-8 .flickity-slider>.col,.small-columns-8>.col", "properties": 0}, {"selector": "@media screen and (min-width:550px)", "properties": 0}, {"selector": ".medium-1", "properties": 0}, {"selector": ".medium-2", "properties": 0}, {"selector": ".medium-3", "properties": 0}, {"selector": ".medium-4", "properties": 0}, {"selector": ".medium-5", "properties": 0}, {"selector": ".medium-6", "properties": 0}, {"selector": ".medium-7", "properties": 0}, {"selector": ".medium-8", "properties": 0}, {"selector": ".medium-9", "properties": 0}, {"selector": ".medium-10", "properties": 0}, {"selector": ".medium-11", "properties": 0}, {"selector": ".medium-12,.medium-columns-1 .flickity-slider>.col,.medium-columns-1>.col", "properties": 0}, {"selector": ".medium-columns-2 .flickity-slider>.col,.medium-columns-2>.col", "properties": 0}, {"selector": ".medium-columns-3 .flickity-slider>.col,.medium-columns-3>.col", "properties": 0}, {"selector": ".medium-columns-4 .flickity-slider>.col,.medium-columns-4>.col", "properties": 0}, {"selector": ".medium-columns-5 .flickity-slider>.col,.medium-columns-5>.col", "properties": 0}, {"selector": ".medium-columns-6 .flickity-slider>.col,.medium-columns-6>.col", "properties": 0}, {"selector": ".medium-columns-7 .flickity-slider>.col,.medium-columns-7>.col", "properties": 0}, {"selector": ".medium-columns-8 .flickity-slider>.col,.medium-columns-8>.col", "properties": 0}, {"selector": "@media screen and (min-width:850px)", "properties": 0}, {"selector": ".large-1", "properties": 0}, {"selector": ".large-2", "properties": 0}, {"selector": ".large-3", "properties": 0}, {"selector": ".large-4", "properties": 0}, {"selector": ".large-5", "properties": 0}, {"selector": ".large-6", "properties": 0}, {"selector": ".large-7", "properties": 0}, {"selector": ".large-8", "properties": 0}, {"selector": ".large-9", "properties": 0}, {"selector": ".large-10", "properties": 0}, {"selector": ".large-11", "properties": 0}, {"selector": ".gallery-columns-1 .gallery-item,.large-12,.large-columns-1 .flickity-slider>.col,.large-columns-1>.col", "properties": 0}, {"selector": ".gallery-columns-2 .gallery-item,.large-columns-2 .flickity-slider>.col,.large-columns-2>.col", "properties": 0}, {"selector": ".gallery-columns-3 .gallery-item,.large-columns-3 .flickity-slider>.col,.large-columns-3>.col", "properties": 0}, {"selector": ".gallery-columns-4 .gallery-item,.large-columns-4 .flickity-slider>.col,.large-columns-4>.col", "properties": 0}, {"selector": ".gallery-columns-5 .gallery-item,.large-columns-5 .flickity-slider>.col,.large-columns-5>.col", "properties": 0}, {"selector": ".gallery-columns-6 .gallery-item,.large-columns-6 .flickity-slider>.col,.large-columns-6>.col", "properties": 0}, {"selector": ".gallery-columns-7 .gallery-item,.large-columns-7 .flickity-slider>.col,.large-columns-7>.col", "properties": 0}, {"selector": ".gallery-columns-8 .gallery-item,.large-columns-8 .flickity-slider>.col,.large-columns-8>.col", "properties": 0}, {"selector": ".has-shadow>.col>.col-inner", "properties": 0}, {"selector": ".col-hover-blur .col-inner,.col-hover-fade .col-inner,.col-hover-focus .col-inner", "properties": 0}, {"selector": ".col-hover-fade .col-inner", "properties": 0}, {"selector": ".col-hover-fade:hover .col-inner", "properties": 0}, {"selector": ".col-hover-blur .col-inner", "properties": 0}, {"selector": ".col-hover-blur:hover .col-inner", "properties": 0}, {"selector": ".col-hover-focus:hover .col-inner", "properties": 0}, {"selector": ".row:hover .col-hover-focus .col:not(:hover)", "properties": 0}, {"selector": ".container,.container-width,.full-width .ubermenu-nav,.row,body.boxed,body.framed", "properties": 0}, {"selector": ".container", "properties": 0}, {"selector": ".container,.container-width,.full-width .ubermenu-nav,.row", "properties": 0}, {"selector": ".row.row-collapse", "properties": 0}, {"selector": ".row.row-small", "properties": 0}, {"selector": ".row.row-large", "properties": 0}, {"selector": "body.boxed,body.boxed .header-wrapper,body.boxed .is-sticky-section,body.boxed header,body.framed,body.framed .header-wrapper,body.framed header", "properties": 0}, {"selector": "@media screen and (min-width:850px)", "properties": 0}, {"selector": "body.framed", "properties": 0}, {"selector": ".flex-row", "properties": 0}, {"selector": ".flex-row-start", "properties": 0}, {"selector": ".flex-row-center", "properties": 0}, {"selector": ".flex-row-col", "properties": 0}, {"selector": ".text-center .flex-row", "properties": 0}, {"selector": ".header .flex-row", "properties": 0}, {"selector": ".flex-col", "properties": 0}, {"selector": ".flex-grow", "properties": 0}, {"selector": ".flex-center", "properties": 0}, {"selector": ".flex-left", "properties": 0}, {"selector": ".flex-right", "properties": 0}, {"selector": ".flex-wrap", "properties": 0}, {"selector": ".flex-has-center>.flex-col:not(.flex-center),.flex-has-center>.flex-row>.flex-col:not(.flex-center),.flex-has-center>.nav-row>.flex-col:not(.flex-center)", "properties": 0}, {"selector": "@media(max-width:849px)", "properties": 0}, {"selector": ".medium-flex-wrap", "properties": 0}, {"selector": ".medium-flex-wrap .flex-col", "properties": 0}, {"selector": ".medium-text-center .flex-row", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".small-flex-wrap", "properties": 0}, {"selector": ".small-flex-wrap .flex-col", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".col2-set,.u-columns", "properties": 0}, {"selector": ".col2-set>div+div,.u-columns>div+div", "properties": 0}, {"selector": ".row-grid .box .image-cover", "properties": 0}, {"selector": ".grid-col>.col-inner", "properties": 0}, {"selector": ".grid-col .banner,.grid-col .box,.grid-col .box-image,.grid-col .box-image img,.grid-col .col-inner>.img,.grid-col .flickity-slider>.img,.grid-col .image-cover,.grid-col .image-cover img,.grid-col .slider,.grid-col .slider-wrapper,.grid-col .slider:not(.flickity-enabled),.grid-col .slider>.img,.grid-col>.col-inner>.img,.grid-col>.col-inner>.img .img-inner>img,.grid-col>.col-inner>.img div", "properties": 0}, {"selector": ".grid-col .box-image img", "properties": 0}, {"selector": ".grid-col .flickity-viewport", "properties": 0}, {"selector": ".grid-col .box:not(.box-shade):not(.box-overlay):not(.box-badge) .box-text", "properties": 0}, {"selector": "@media(prefers-reduced-motion)", "properties": 0}, {"selector": ".grid-col", "properties": 0}, {"selector": "@media (-ms-high-contrast:none)", "properties": 0}, {"selector": ".payment-icon svg", "properties": 0}, {"selector": ".slider-nav-circle .flickity-prev-next-button svg", "properties": 0}, {"selector": ".nav>li>a>i", "properties": 0}, {"selector": ".gallery,.row", "properties": 0}, {"selector": ".row>div:not(.col):not([class^=col-]):not([class*=\" col-\"])", "properties": 0}, {"selector": ".row.row-grid,.row.row-masonry", "properties": 0}, {"selector": ".row.row-grid>.col,.row.row-masonry>.col", "properties": 0}, {"selector": ".container .row:not(.row-collapse),.lightbox-content .row:not(.row-collapse),.row .gallery,.row .row:not(.row-collapse)", "properties": 0}, {"selector": ".row .container", "properties": 0}, {"selector": ".banner+.row:not(.row-collapse),.banner-grid-wrapper+.row:not(.row-collapse),.section+.row:not(.row-collapse),.slider-wrapper+.row:not(.row-collapse)", "properties": 0}, {"selector": ".row-full-width", "properties": 0}, {"selector": ".row-isotope", "properties": 0}, {"selector": ".row-reverse", "properties": 0}, {"selector": "@media screen and (max-width:549px)", "properties": 0}, {"selector": ".small-row-reverse", "properties": 0}, {"selector": "@media screen and (max-width:849px)", "properties": 0}, {"selector": ".medium-row-reverse", "properties": 0}, {"selector": ".row-collapse", "properties": 0}, {"selector": ".row-collapse>.col,.row-collapse>.flickity-viewport>.flickity-slider>.col", "properties": 0}, {"selector": ".row-collapse>.col .container", "properties": 0}, {"selector": ".container .row-small:not(.row-collapse),.row .row-small:not(.row-collapse)", "properties": 0}, {"selector": ".row-small>.col,.row-small>.flickity-viewport>.flickity-slider>.col", "properties": 0}, {"selector": ".container .row-xsmall:not(.row-collapse),.row .row-xsmall:not(.row-collapse)", "properties": 0}, {"selector": ".row-xsmall>.col,.row-xsmall>.flickity-viewport>.flickity-slider>.col", "properties": 0}, {"selector": "@media screen and (min-width:850px)", "properties": 0}, {"selector": ".row-large", "properties": 0}, {"selector": ".container .row-large:not(.row-collapse),.row .row-large:not(.row-collapse)", "properties": 0}, {"selector": ".row-large>.col,.row-large>.flickity-viewport>.flickity-slider>.col", "properties": 0}, {"selector": ".row-dashed,.row-solid", "properties": 0}, {"selector": ".row-dashed .col,.row-solid .col", "properties": 0}, {"selector": ".row-dashed .col-inner,.row-solid .col-inner", "properties": 0}, {"selector": ".row-dashed .col:before,.row-solid .col:before", "properties": 0}, {"selector": ".row-dashed .col:after,.row-solid .col:after", "properties": 0}, {"selector": ".row-solid .col:before", "properties": 0}, {"selector": ".row-solid .col:after", "properties": 0}, {"selector": ".dark .row-dashed .col:after,.dark .row-dashed .col:before,.dark .row-solid .col:after,.dark .row-solid .col:before", "properties": 0}, {"selector": ".block-html-after-header .row .col", "properties": 0}, {"selector": ".wpb-js-composer .row:before", "properties": 0}, {"selector": ".section", "properties": 0}, {"selector": ".section.dark", "properties": 0}, {"selector": ".section-bg", "properties": 0}, {"selector": ".section-bg,.section-content", "properties": 0}, {"selector": ".section-content", "properties": 0}, {"selector": ".has-parallax", "properties": 0}, {"selector": ".container .section,.row .section", "properties": 0}, {"selector": ".section-bg :is(img,picture,video)", "properties": 0}, {"selector": ".is-sticky-section+.sticky-section-helper", "properties": 0}, {"selector": ".sticky-section-helper+div", "properties": 0}, {"selector": ".sticky-section", "properties": 0}, {"selector": ".is-sticky-section", "properties": 0}, {"selector": ".nav-dropdown", "properties": 0}, {"selector": ".dark.nav-dropdown", "properties": 0}, {"selector": ".nav-dropdown:after", "properties": 0}, {"selector": ".nav-dropdown li", "properties": 0}, {"selector": ".nav-dropdown li ul", "properties": 0}, {"selector": ".nav-dropdown>li.html", "properties": 0}, {"selector": ".next-prev-thumbs li.has-dropdown:hover>.nav-dropdown,.no-js li.has-dropdown:hover>.nav-dropdown,li.current-dropdown>.nav-dropdown", "properties": 0}, {"selector": ".nav-right li.current-dropdown:last-child>.nav-dropdown,.nav-right li.has-dropdown:hover:last-child>.nav-dropdown", "properties": 0}, {"selector": ".nav-column li>a,.nav-dropdown>li>a", "properties": 0}, {"selector": ".nav-column>li:last-child:not(.nav-dropdown-col)>a,.nav-dropdown>li:last-child:not(.nav-dropdown-col)>a", "properties": 0}, {"selector": ".dropdown-uppercase.nav-dropdown .nav-column>li>a,.nav-dropdown.dropdown-uppercase>li>a", "properties": 0}, {"selector": ".nav-dropdown>li.image-column,.nav-dropdown>li.nav-dropdown-col", "properties": 0}, {"selector": ".nav-dropdown>li.image-column:not(:last-child),.nav-dropdown>li.nav-dropdown-col:not(:last-child)", "properties": 0}, {"selector": ".nav-dropdown .menu-item-has-children>a,.nav-dropdown .nav-dropdown-col>a,.nav-dropdown .title>a", "properties": 0}, {"selector": ".nav-dropdown .nav-dropdown-col .menu-item-has-children", "properties": 0}, {"selector": ".nav-dropdown li.image-column", "properties": 0}, {"selector": ".nav-dropdown li.image-column>a", "properties": 0}, {"selector": ".nav-dropdown li.image-column>a img", "properties": 0}, {"selector": ".nav-dropdown li.image-column>a:hover", "properties": 0}, {"selector": ".nav-dropdown-default li.image-column:first-child>a", "properties": 0}, {"selector": ".nav-dropdown-default li.image-column:last-child>a", "properties": 0}, {"selector": ".nav-dropdown.nav-dropdown-bold>li.nav-dropdown-col,.nav-dropdown.nav-dropdown-simple>li.nav-dropdown-col", "properties": 0}, {"selector": ".nav-dropdown-default .nav-column li>a,.nav-dropdown.nav-dropdown-default>li>a", "properties": 0}, {"selector": ".nav-dropdown-default", "properties": 0}, {"selector": ".nav-dropdown-bold .nav-column li>a,.nav-dropdown.nav-dropdown-bold>li>a", "properties": 0}, {"selector": ".nav-dropdown-bold .nav-column li>a:hover,.nav-dropdown-bold.dark .nav-column li>a:hover,.nav-dropdown.nav-dropdown-bold.dark>li>a:hover,.nav-dropdown.nav-dropdown-bold>li>a:hover", "properties": 0}, {"selector": ".nav-dropdown-simple .nav-column li>a:hover,.nav-dropdown.nav-dropdown-simple>li>a:hover", "properties": 0}, {"selector": ".nav-dropdown.nav-dropdown-bold li.html,.nav-dropdown.nav-dropdown-simple li.html", "properties": 0}, {"selector": ".menu-item-has-block .nav-dropdown", "properties": 0}, {"selector": ".menu-item-has-block .nav-dropdown .col-inner ul:not(.nav.nav-vertical) li:not(.tab):not([class^=bullet-])", "properties": 0}, {"selector": "li.current-dropdown>.nav-dropdown-full,li.has-dropdown:hover>.nav-dropdown-full", "properties": 0}, {"selector": ".nav-dropdown-full>li.nav-dropdown-col", "properties": 0}, {"selector": ".nav-dropdown-has-arrow li.has-dropdown:after,.nav-dropdown-has-arrow li.has-dropdown:before", "properties": 0}, {"selector": ".nav-dropdown-has-arrow li.has-dropdown:after", "properties": 0}, {"selector": ".nav-dropdown-has-arrow li.has-dropdown:before", "properties": 0}, {"selector": ".nav-dropdown-has-arrow .ux-nav-vertical-menu .menu-item-design-custom-size:after,.nav-dropdown-has-arrow .ux-nav-vertical-menu .menu-item-design-custom-size:before,.nav-dropdown-has-arrow .ux-nav-vertical-menu .menu-item-design-default:after,.nav-dropdown-has-arrow .ux-nav-vertical-menu .menu-item-design-default:before,.nav-dropdown-has-arrow li.has-dropdown.menu-item-design-container-width:after,.nav-dropdown-has-arrow li.has-dropdown.menu-item-design-container-width:before,.nav-dropdown-has-arrow li.has-dropdown.menu-item-design-full-width:after,.nav-dropdown-has-arrow li.has-dropdown.menu-item-design-full-width:before", "properties": 0}, {"selector": ".nav-dropdown-has-shadow .nav-dropdown", "properties": 0}, {"selector": ".nav-dropdown-has-arrow.nav-dropdown-has-border li.has-dropdown:before", "properties": 0}, {"selector": ".nav-dropdown-has-border .nav-dropdown", "properties": 0}, {"selector": ".nav-dropdown-has-arrow li.current-dropdown.has-dropdown:after,.nav-dropdown-has-arrow li.current-dropdown.has-dropdown:before", "properties": 0}, {"selector": ".label-hot.menu-item>a:after,.label-new.menu-item>a:after,.label-popular.menu-item>a:after,.label-sale.menu-item>a:after", "properties": 0}, {"selector": ".label-new.menu-item>a:after", "properties": 0}, {"selector": ".label-sale.menu-item>a:after", "properties": 0}, {"selector": ".label-popular.menu-item>a:after", "properties": 0}, {"selector": ".label-hot.menu-item>a:after", "properties": 0}, {"selector": ".nav p", "properties": 0}, {"selector": ".nav,.nav ul:not(.nav-dropdown)", "properties": 0}, {"selector": ".nav>li>a.button,.nav>li>button", "properties": 0}, {"selector": ".nav", "properties": 0}, {"selector": ".nav,.nav>li", "properties": 0}, {"selector": ".nav>li", "properties": 0}, {"selector": ".nav>li>a", "properties": 0}, {"selector": ".html .nav>li>a", "properties": 0}, {"selector": ".nav-small .nav>li>a,.nav.nav-small>li>a", "properties": 0}, {"selector": ".nav-small.nav>li.html", "properties": 0}, {"selector": ".nav-center", "properties": 0}, {"selector": ".nav-fill", "properties": 0}, {"selector": ".nav-left", "properties": 0}, {"selector": ".nav-right", "properties": 0}, {"selector": "@media(max-width:849px)", "properties": 0}, {"selector": ".medium-nav-center", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".small-nav-center", "properties": 0}, {"selector": ".nav-column>li>a,.nav-dropdown>li>a,.nav-vertical-fly-out>li>a,.nav>li>a", "properties": 0}, {"selector": ".nav-column>li>a,.nav-dropdown>li>a", "properties": 0}, {"selector": ".nav-column li.active>a,.nav-column li>a:hover,.nav-dropdown li.active>a,.nav-dropdown>li>a:hover,.nav-vertical-fly-out>li>a:hover,.nav>li.active>a,.nav>li.current>a,.nav>li>a.active,.nav>li>a.current,.nav>li>a:hover", "properties": 0}, {"selector": ".nav li:first-child", "properties": 0}, {"selector": ".nav li:last-child", "properties": 0}, {"selector": ".nav-uppercase>li>a", "properties": 0}, {"selector": ".nav-thin>li>a", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".nav-divided>li", "properties": 0}, {"selector": ".nav-divided>li+li>a:after", "properties": 0}, {"selector": ".nav-divided>li+li.icon-top>a:after", "properties": 0}, {"selector": "li.html .row,li.html form,li.html input,li.html select", "properties": 0}, {"selector": "li.html>.button", "properties": 0}, {"selector": "li.hide-title>a", "properties": 0}, {"selector": ".nav-pagination>li>a,.nav-pagination>li>span", "properties": 0}, {"selector": ".nav-pagination>li i", "properties": 0}, {"selector": ".nav-pagination>li>.current,.nav-pagination>li>a:hover,.nav-pagination>li>span:hover", "properties": 0}, {"selector": ".off-canvas .mobile-sidebar-slide", "properties": 0}, {"selector": ".off-canvas .mobile-sidebar-slide .sidebar-menu", "properties": 0}, {"selector": ".mobile-sidebar-levels-1 .nav-slide>li>.sub-menu,.mobile-sidebar-levels-1 .nav-slide>li>ul.children", "properties": 0}, {"selector": ".mobile-sidebar-levels-1 .nav-slide>li>.sub-menu>li,.mobile-sidebar-levels-1 .nav-slide>li>ul.children>li", "properties": 0}, {"selector": ".mobile-sidebar-levels-1 .nav-slide>li>.sub-menu.is-current-slide,.mobile-sidebar-levels-1 .nav-slide>li>ul.children.is-current-slide", "properties": 0}, {"selector": ".mobile-sidebar-levels-1 .nav-slide>li>.sub-menu.is-current-parent,.mobile-sidebar-levels-1 .nav-slide>li>ul.children.is-current-parent", "properties": 0}, {"selector": ".mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header),.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)", "properties": 0}, {"selector": ".mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header)>a,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)>a", "properties": 0}, {"selector": ".mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header).active,.mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header):hover,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header).active,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header):hover", "properties": 0}, {"selector": ".mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header)>.sub-menu,.mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header)>ul.children,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)>.sub-menu,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)>ul.children", "properties": 0}, {"selector": ".mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header)>.sub-menu>li,.mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header)>ul.children>li,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)>.sub-menu>li,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)>ul.children>li", "properties": 0}, {"selector": ".mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header)>.sub-menu.is-current-slide,.mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header)>ul.children.is-current-slide,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)>.sub-menu.is-current-slide,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)>ul.children.is-current-slide", "properties": 0}, {"selector": ".mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header)>.sub-menu.is-current-parent,.mobile-sidebar-levels-2 .nav-slide>li>.sub-menu>li:not(.nav-slide-header)>ul.children.is-current-parent,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)>.sub-menu.is-current-parent,.mobile-sidebar-levels-2 .nav-slide>li>ul.children>li:not(.nav-slide-header)>ul.children.is-current-parent", "properties": 0}, {"selector": ".nav-slide", "properties": 0}, {"selector": ".nav-slide .active>.toggle", "properties": 0}, {"selector": ".nav-slide>li", "properties": 0}, {"selector": ".nav-slide-header .toggle", "properties": 0}, {"selector": ".nav-slide-header .toggle i", "properties": 0}, {"selector": ".text-center .nav-slide-header .toggle", "properties": 0}, {"selector": ".nav-line-bottom>li>a:before,.nav-line-grow>li>a:before,.nav-line>li>a:before", "properties": 0}, {"selector": ".nav-line-bottom>li.active>a:before,.nav-line-bottom>li:hover>a:before,.nav-line-grow>li.active>a:before,.nav-line-grow>li:hover>a:before,.nav-line>li.active>a:before,.nav-line>li:hover>a:before", "properties": 0}, {"selector": ".nav:hover>li:not(:hover)>a:before", "properties": 0}, {"selector": ".tabbed-content .nav-line-bottom:before", "properties": 0}, {"selector": ".nav-line-grow>li>a:before", "properties": 0}, {"selector": ".nav-line-grow>li.active>a:before,.nav-line-grow>li:hover>a:before", "properties": 0}, {"selector": ".nav-line-bottom>li:after,.nav-line-bottom>li:before", "properties": 0}, {"selector": ".nav-line-bottom>li>a:before", "properties": 0}, {"selector": ".nav-box>li.active>a,.nav-pills>li.active>a", "properties": 0}, {"selector": ".nav-tabs>li.active>a", "properties": 0}, {"selector": ".nav-dark .nav.nav-tabs>li.active>a", "properties": 0}, {"selector": ".nav-outline>li.active>a", "properties": 0}, {"selector": ".tabbed-content", "properties": 0}, {"selector": ".tabbed-content .nav,.tabbed-content .tab-panels", "properties": 0}, {"selector": ".tab-panels", "properties": 0}, {"selector": ".tab-panels .entry-content", "properties": 0}, {"selector": ".tab-panels .panel:not(.active)", "properties": 0}, {"selector": ".tab-panels .panel.active", "properties": 0}, {"selector": ".nav-box>li,.nav-outline>li,.nav-pills>li,.nav-tabs>li", "properties": 0}, {"selector": ".nav-box>li>a,.nav-pills>li>a", "properties": 0}, {"selector": ".nav-pills>li>a", "properties": 0}, {"selector": ".nav-tabs>li>a", "properties": 0}, {"selector": ".nav-tabs>li.active>a", "properties": 0}, {"selector": ".nav-dark .nav-tabs>li:not(.active)>a", "properties": 0}, {"selector": ".tabbed-content .nav-tabs>li", "properties": 0}, {"selector": ".nav-tabs+.tab-panels", "properties": 0}, {"selector": ".nav-outline>li>a", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".tabbed-content .nav-vertical", "properties": 0}, {"selector": ".nav-vertical+.tab-panels", "properties": 0}, {"selector": ".tabbed-content .nav-vertical.nav-outline", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".small-nav-collapse>li", "properties": 0}, {"selector": ".small-nav-collapse>li a", "properties": 0}, {"selector": ".small-nav-touch", "properties": 0}, {"selector": ".small-nav-touch>li", "properties": 0}, {"selector": ".sidebar-menu-tabs__tab", "properties": 0}, {"selector": ".sidebar-menu-tabs__tab.active", "properties": 0}, {"selector": ".sidebar-menu-tabs__tab-text", "properties": 0}, {"selector": ".sidebar-menu-tabs.nav>li", "properties": 0}, {"selector": ".sidebar-menu-tabs.nav>li>a", "properties": 0}, {"selector": ".toggle", "properties": 0}, {"selector": ".toggle i", "properties": 0}, {"selector": ".toggle:focus", "properties": 0}, {"selector": ".toggle:hover", "properties": 0}, {"selector": ".active>.toggle", "properties": 0}, {"selector": ".active>ul.children,.active>ul.sub-menu:not(.nav-dropdown)", "properties": 0}, {"selector": ".widget .toggle", "properties": 0}, {"selector": ".widget .toggle i", "properties": 0}, {"selector": "@media(prefers-reduced-motion)", "properties": 0}, {"selector": ".toggle", "properties": 0}, {"selector": ".nav.nav-vertical", "properties": 0}, {"selector": ".nav.nav-vertical li", "properties": 0}, {"selector": ".nav-vertical li li", "properties": 0}, {"selector": ".nav-vertical .image-column", "properties": 0}, {"selector": ".nav-vertical>li", "properties": 0}, {"selector": ".nav-vertical>li ul", "properties": 0}, {"selector": ".nav-vertical li li.menu-item-has-children", "properties": 0}, {"selector": ".nav-vertical li li.menu-item-has-children:not(:first-child)", "properties": 0}, {"selector": ".nav-vertical>li>a,.nav-vertical>li>ul>li a", "properties": 0}, {"selector": ".nav-vertical li li.menu-item-has-children>a", "properties": 0}, {"selector": ".nav-vertical>li.html", "properties": 0}, {"selector": ".nav-vertical>li>ul li a", "properties": 0}, {"selector": ".nav-vertical>li>ul li a:hover", "properties": 0}, {"selector": ".nav-vertical>li>ul", "properties": 0}, {"selector": ".nav .children", "properties": 0}, {"selector": "@media(prefers-reduced-motion)", "properties": 0}, {"selector": ".nav .children", "properties": 0}, {"selector": ".nav:not(.nav-slide) .active>.children", "properties": 0}, {"selector": ".nav-sidebar.nav-vertical>li.menu-item.active,.nav-sidebar.nav-vertical>li.menu-item:hover", "properties": 0}, {"selector": ".nav-sidebar.nav-vertical>li+li", "properties": 0}, {"selector": ".dark .nav-sidebar.nav-vertical>li+li,.dark .nav-vertical-fly-out>li+li", "properties": 0}, {"selector": ".nav-vertical>li+li", "properties": 0}, {"selector": ".nav-vertical-fly-out>li+li", "properties": 0}, {"selector": ".nav-vertical.nav-line>li>a:before", "properties": 0}, {"selector": ".nav-vertical.nav-tabs>li>a", "properties": 0}, {"selector": ".nav-vertical.nav-tabs>li.active>a", "properties": 0}, {"selector": ".text-center .toggle", "properties": 0}, {"selector": ".text-center .nav-vertical li", "properties": 0}, {"selector": ".text-center .nav-vertical li li,.text-center.nav-vertical li li", "properties": 0}, {"selector": "@media screen and (max-width:549px)", "properties": 0}, {"selector": ".small-nav-vertical", "properties": 0}, {"selector": ".small-nav-vertical>li", "properties": 0}, {"selector": ".nav-vertical .mega-menu-item", "properties": 0}, {"selector": ".nav-vertical .header-button,.nav-vertical .social-icons", "properties": 0}, {"selector": "@media screen and (min-width:849px)", "properties": 0}, {"selector": ".col-divided>.col-inner>.nav-wrapper>.nav-vertical", "properties": 0}, {"selector": ".header-vertical-menu__opener", "properties": 0}, {"selector": ".header-vertical-menu__opener .icon-menu", "properties": 0}, {"selector": ".header-vertical-menu__tagline", "properties": 0}, {"selector": ".header-vertical-menu__title", "properties": 0}, {"selector": ".header-vertical-menu__fly-out", "properties": 0}, {"selector": ".header-vertical-menu__fly-out .nav-vertical-fly-out", "properties": 0}, {"selector": ".header-vertical-menu__fly-out .nav-vertical-fly-out>li.menu-item", "properties": 0}, {"selector": ".header-vertical-menu__fly-out .nav-vertical-fly-out>li.menu-item[class*=\" label-\"]>a:after,.header-vertical-menu__fly-out .nav-vertical-fly-out>li.menu-item[class^=label-]>a:after", "properties": 0}, {"selector": ".header-vertical-menu__fly-out .nav-vertical-fly-out>li.menu-item>a>i.icon-angle-down", "properties": 0}, {"selector": ".header-vertical-menu__fly-out .nav-vertical-fly-out>li.menu-item>a", "properties": 0}, {"selector": ".header-vertical-menu__fly-out .nav-vertical-fly-out li.has-icon-left>a>i,.header-vertical-menu__fly-out .nav-vertical-fly-out li.has-icon-left>a>img,.header-vertical-menu__fly-out .nav-vertical-fly-out li.has-icon-left>a>svg", "properties": 0}, {"selector": ".header-vertical-menu__fly-out .nav-top-link", "properties": 0}, {"selector": ".header-vertical-menu__fly-out .current-dropdown .nav-dropdown", "properties": 0}, {"selector": ".header-vertical-menu__fly-out .current-dropdown.menu-item .nav-dropdown", "properties": 0}, {"selector": ".header-vertical-menu__fly-out--open", "properties": 0}, {"selector": ".sticky-hide-on-scroll--active .header-vertical-menu__fly-out,.stuck .header-vertical-menu__fly-out", "properties": 0}, {"selector": ".header-wrapper:not(.stuck) .header-vertical-menu__fly-out", "properties": 0}, {"selector": ".header-vertical-menu__fly-out.has-shadow", "properties": 0}, {"selector": ".header-vertical-menu:hover .header-vertical-menu__fly-out", "properties": 0}, {"selector": ".badge-container", "properties": 0}, {"selector": ".badge", "properties": 0}, {"selector": ".badge.top", "properties": 0}, {"selector": ".badge-inner", "properties": 0}, {"selector": ".badge-small", "properties": 0}, {"selector": ".badge-small .badge-inner", "properties": 0}, {"selector": ".badge+.badge", "properties": 0}, {"selector": ".badge+.badge+.badge", "properties": 0}, {"selector": ".badge-frame .badge-inner,.badge-outline .badge-inner", "properties": 0}, {"selector": ".has-hover:hover .badge-outline .badge-inner", "properties": 0}, {"selector": ".badge-circle,.badge-outline", "properties": 0}, {"selector": ".badge-circle-inside", "properties": 0}, {"selector": ".badge-circle+.badge", "properties": 0}, {"selector": ".badge-circle .badge-inner,.badge-circle-inside .badge-inner", "properties": 0}, {"selector": ".badge-frame .badge-inner,.widget .badge-outline .badge-inner", "properties": 0}, {"selector": ".badge-frame", "properties": 0}, {"selector": ".widget .badge", "properties": 0}, {"selector": ".dark .widget .badge-outline .badge-inner", "properties": 0}, {"selector": ".row-collapse .badge-circle", "properties": 0}, {"selector": ".banner", "properties": 0}, {"selector": ".banner-bg", "properties": 0}, {"selector": ".banner-layers", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".banner-layers", "properties": 0}, {"selector": ".banner-layers>*", "properties": 0}, {"selector": ".dark .banner a", "properties": 0}, {"selector": ".banner-bg :is(img,picture,video)", "properties": 0}, {"selector": "@media(min-width:549px)", "properties": 0}, {"selector": ".has-video .bg", "properties": 0}, {"selector": ".box", "properties": 0}, {"selector": ".box>a", "properties": 0}, {"selector": ".box a", "properties": 0}, {"selector": ".box,.box-image,.box-text", "properties": 0}, {"selector": ".box-image>a,.box-image>div>a", "properties": 0}, {"selector": ".box-image>a:focus-visible,.box-image>div>a:focus-visible", "properties": 0}, {"selector": ".box-text", "properties": 0}, {"selector": ".has-shadow .box-text", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".box-text", "properties": 0}, {"selector": ".box-image", "properties": 0}, {"selector": ".box-image[style*=-radius]", "properties": 0}, {"selector": ".box-image-inner", "properties": 0}, {"selector": ".box-text.text-center", "properties": 0}, {"selector": ".box-image img", "properties": 0}, {"selector": ".box-label", "properties": 0}, {"selector": ".box-label .box-text.text-center", "properties": 0}, {"selector": ".box-label .box-text.text-right", "properties": 0}, {"selector": ".box-label .box-text", "properties": 0}, {"selector": ".box-label:hover .box-text", "properties": 0}, {"selector": ".box-badge", "properties": 0}, {"selector": ".box-badge .box-text", "properties": 0}, {"selector": ".dark .box-badge .box-text", "properties": 0}, {"selector": ".box-badge:hover .box-text", "properties": 0}, {"selector": ".box-bounce .box-text", "properties": 0}, {"selector": ".box-bounce:hover .box-text", "properties": 0}, {"selector": ".dark .box-bounce:hover .box-text", "properties": 0}, {"selector": ".box-bounce:hover .box-image", "properties": 0}, {"selector": ".box-push .box-text", "properties": 0}, {"selector": ".box-overlay .box-text,.box-shade .box-text", "properties": 0}, {"selector": ".box-overlay.dark .box-text,.box-shade.dark .box-text", "properties": 0}, {"selector": ".box-none .box-text", "properties": 0}, {"selector": ".has-post-icon:hover .box-overlay .box-text,.has-post-icon:hover .box-shade .box-text", "properties": 0}, {"selector": ".box-text-middle.box-overlay .box-text,.box-text-middle.box-shade .box-text", "properties": 0}, {"selector": ".box-text-top.box-overlay .box-text,.box-text-top.box-shade .box-text", "properties": 0}, {"selector": ".box-vertical .box-text-middle .box-text", "properties": 0}, {"selector": ".has-box-vertical .col", "properties": 0}, {"selector": "@media screen and (max-width:549px)", "properties": 0}, {"selector": ".box-vertical .box-image", "properties": 0}, {"selector": "@media screen and (min-width:550px)", "properties": 0}, {"selector": ".box-list-view .col", "properties": 0}, {"selector": ".box-list-view .box,.box-vertical", "properties": 0}, {"selector": ".box-list-view .box .image-right,.box-vertical .image-right", "properties": 0}, {"selector": ".box-list-view .box .box-image,.box-list-view .box .box-text,.box-vertical .box-image,.box-vertical .box-text", "properties": 0}, {"selector": ".box-list-view .box .box-text,.box-vertical .box-text", "properties": 0}, {"selector": ".box-list-view .box .box-image,.box-vertical .box-image", "properties": 0}, {"selector": ".box-list-view .box.align-top .box-text,.box-vertical.align-top .box-text", "properties": 0}, {"selector": ".button,button,input[type=button],input[type=reset],input[type=submit]", "properties": 0}, {"selector": ".button span", "properties": 0}, {"selector": ".button.is-outline", "properties": 0}, {"selector": ".nav>li>a.button,.nav>li>a.button:hover", "properties": 0}, {"selector": ".nav>li>a.button:hover", "properties": 0}, {"selector": ".button,input[type=button].button,input[type=reset].button,input[type=submit],input[type=submit].button", "properties": 0}, {"selector": ".button.is-outline,input[type=button].is-outline,input[type=reset].is-outline,input[type=submit].is-outline", "properties": 0}, {"selector": ".button:hover,.dark .button.is-form:hover,input[type=button]:hover,input[type=reset]:hover,input[type=submit]:hover", "properties": 0}, {"selector": ".button.is-link,.button.is-underline", "properties": 0}, {"selector": ".button.is-underline:before", "properties": 0}, {"selector": ".button.is-underline:hover:before", "properties": 0}, {"selector": ".is-link,.is-underline", "properties": 0}, {"selector": ".dark .button.is-link:hover,.dark .button.is-underline:hover", "properties": 0}, {"selector": ".button.is-link:hover,.button.is-underline:hover", "properties": 0}, {"selector": ".is-form,button.is-form,button[type=submit].is-form,input[type=button].is-form,input[type=reset].is-form,input[type=submit].is-form", "properties": 0}, {"selector": ".button.is-form:hover", "properties": 0}, {"selector": ".dark .button,.dark input[type=button],.dark input[type=reset],.dark input[type=submit]", "properties": 0}, {"selector": ".dark .button.is-form,.dark input[type=button].is-form,.dark input[type=reset].is-form,.dark input[type=submit].is-form", "properties": 0}, {"selector": ".is-round", "properties": 0}, {"selector": ".is-bevel,.is-gloss,.is-shade", "properties": 0}, {"selector": ".is-bevel:after,.is-gloss:after,.is-shade:after", "properties": 0}, {"selector": ".is-shade:after", "properties": 0}, {"selector": ".is-bevel:after", "properties": 0}, {"selector": ".is-outline", "properties": 0}, {"selector": ".is-outline:hover", "properties": 0}, {"selector": ".primary,input[type=submit],input[type=submit].button", "properties": 0}, {"selector": ".primary.is-link,.primary.is-outline,.primary.is-underline", "properties": 0}, {"selector": ".is-outline:hover,.primary.is-outline:hover", "properties": 0}, {"selector": ".success", "properties": 0}, {"selector": ".success.is-link,.success.is-outline,.success.is-underline", "properties": 0}, {"selector": ".success.is-outline:hover", "properties": 0}, {"selector": ".white", "properties": 0}, {"selector": ".white.button.is-outline", "properties": 0}, {"selector": ".white.is-link,.white.is-outline,.white.is-underline", "properties": 0}, {"selector": ".white.is-outline:hover", "properties": 0}, {"selector": ".button.alt,.button.checkout,.checkout-button,.secondary", "properties": 0}, {"selector": ".secondary.is-link,.secondary.is-outline,.secondary.is-underline", "properties": 0}, {"selector": ".secondary.is-outline:hover", "properties": 0}, {"selector": "a.primary:not(.button),a.secondary:not(.button)", "properties": 0}, {"selector": ".alert", "properties": 0}, {"selector": ".alert.is-link,.alert.is-outline,.alert.is-underline", "properties": 0}, {"selector": ".alert.is-outline:hover", "properties": 0}, {"selector": ".text-center .button:first-of-type", "properties": 0}, {"selector": ".text-center .button:last-of-type,.text-right .button:last-of-type", "properties": 0}, {"selector": ".button.disabled,.button[disabled],button.disabled,button[disabled]", "properties": 0}, {"selector": ".header-buttons .button", "properties": 0}, {"selector": ".header-button", "properties": 0}, {"selector": ".header-button .plain.is-outline:not(:hover)", "properties": 0}, {"selector": ".nav-dark .header-button .plain.is-outline:not(:hover)", "properties": 0}, {"selector": ".header-button .button", "properties": 0}, {"selector": ".scroll-for-more", "properties": 0}, {"selector": ".scroll-for-more:not(:hover)", "properties": 0}, {"selector": ".flex-col .button,.flex-col button,.flex-col input", "properties": 0}, {"selector": "code", "properties": 0}, {"selector": "pre>code", "properties": 0}, {"selector": ".dark code", "properties": 0}, {"selector": ".is-divider", "properties": 0}, {"selector": ".widget .is-divider", "properties": 0}, {"selector": ".dark .is-divider", "properties": 0}, {"selector": "form", "properties": 0}, {"selector": ".select-resize-ghost,.select2-container .select2-choice,.select2-container .select2-selection,input[type=date],input[type=email],input[type=number],input[type=password],input[type=search],input[type=tel],input[type=text],input[type=url],select,textarea", "properties": 0}, {"selector": "@media(max-width:849px)", "properties": 0}, {"selector": ".select-resize-ghost,.select2-container .select2-choice,.select2-container .select2-selection,input[type=date],input[type=email],input[type=number],input[type=password],input[type=search],input[type=tel],input[type=text],input[type=url],select,textarea", "properties": 0}, {"selector": "input:hover,select:hover,textarea:hover", "properties": 0}, {"selector": "input[type=email],input[type=number],input[type=search],input[type=tel],input[type=text],input[type=url],textarea", "properties": 0}, {"selector": "textarea", "properties": 0}, {"selector": "input[type=email]:focus,input[type=number]:focus,input[type=password]:focus,input[type=search]:focus,input[type=tel]:focus,input[type=text]:focus,select:focus,textarea:focus", "properties": 0}, {"selector": "label,legend", "properties": 0}, {"selector": "legend", "properties": 0}, {"selector": "fieldset", "properties": 0}, {"selector": "input[type=checkbox],input[type=radio]", "properties": 0}, {"selector": "label>.label-body", "properties": 0}, {"selector": "abbr[title=required]", "properties": 0}, {"selector": "input[type=checkbox]+label,input[type=radio]+label", "properties": 0}, {"selector": ".select-resize-ghost,.select2-container .select2-choice,.select2-container .select2-selection,select", "properties": 0}, {"selector": ".select-resize-ghost", "properties": 0}, {"selector": "select.resize-select", "properties": 0}, {"selector": ".select2-selection__arrow b", "properties": 0}, {"selector": ".select2-container .selection .select2-selection--multiple", "properties": 0}, {"selector": ".select2-container .selection .select2-selection--multiple .select2-selection__choice", "properties": 0}, {"selector": "@media (-ms-high-contrast:none),screen and (-ms-high-contrast:active)", "properties": 0}, {"selector": "select::-ms-expand", "properties": 0}, {"selector": "select:focus::-ms-value", "properties": 0}, {"selector": ".form-flat input:not([type=submit]),.form-flat select,.form-flat textarea", "properties": 0}, {"selector": ".form-flat button,.form-flat input", "properties": 0}, {"selector": ".form-flat textarea", "properties": 0}, {"selector": ".flex-row.form-flat .flex-col,.form-flat .flex-row .flex-col", "properties": 0}, {"selector": ".dark .form-flat input:not([type=submit]),.dark .form-flat select,.dark .form-flat textarea,.nav-dark .form-flat input:not([type=submit]),.nav-dark .form-flat select,.nav-dark .form-flat textarea", "properties": 0}, {"selector": ".dark .form-flat select option", "properties": 0}, {"selector": ".dark .form-flat select,.nav-dark .form-flat select", "properties": 0}, {"selector": ".dark .form-flat input::-webkit-input-placeholder,.nav-dark .form-flat input::-webkit-input-placeholder", "properties": 0}, {"selector": ".form-flat .button.icon", "properties": 0}, {"selector": ".form-minimal input:not([type=submit]),.form-minimal select,.form-minimal textarea", "properties": 0}, {"selector": ".form-minimal.quantity .qty", "properties": 0}, {"selector": ".dark .form-minimal.quantity .qty", "properties": 0}, {"selector": ".woocommerce-invalid input,input.wpcf7-not-valid", "properties": 0}, {"selector": ".woocommerce-invalid select", "properties": 0}, {"selector": ".woocommerce-invalid input.input-checkbox", "properties": 0}, {"selector": ".select2-container", "properties": 0}, {"selector": ".form-row .select2-container", "properties": 0}, {"selector": ".select2-search__field", "properties": 0}, {"selector": ".select2-container--default .select2-selection--single .select2-selection__arrow", "properties": 0}, {"selector": ".select2-container .select2-choice>.select2-chosen,.select2-container .select2-selection--single .select2-selection__rendered,.select2-selection", "properties": 0}, {"selector": ".select2-container--default .select2-selection--single .select2-selection__arrow", "properties": 0}, {"selector": ".fl-wrap", "properties": 0}, {"selector": ".loading-site .fl-labels label[for]:first-child", "properties": 0}, {"selector": ".fl-labels .fl-wrap label[for]:first-child", "properties": 0}, {"selector": ".dark .fl-wrap label", "properties": 0}, {"selector": ".fl-wrap.fl-is-active>label[for]:first-child", "properties": 0}, {"selector": ".fl-labels .form-row input:not([type=checkbox]),.fl-labels .form-row select,.fl-labels .form-row textarea", "properties": 0}, {"selector": ".form-row .fl-is-active input,.form-row .fl-is-active textarea", "properties": 0}, {"selector": ".form-row .fl-is-active select", "properties": 0}, {"selector": ".icon-box .icon-box-img", "properties": 0}, {"selector": ".flickity-slider>.icon-box,.slider>.icon-box", "properties": 0}, {"selector": ".icon-box-img img,.icon-box-img svg", "properties": 0}, {"selector": ".icon-box-img svg", "properties": 0}, {"selector": ".icon-box-img svg path", "properties": 0}, {"selector": ".icon-box:hover .has-icon-bg i,.icon-box:hover .has-icon-bg svg,.icon-box:hover .has-icon-bg svg path", "properties": 0}, {"selector": ".has-icon-bg .icon", "properties": 0}, {"selector": ".has-icon-bg .icon .icon-inner", "properties": 0}, {"selector": ".icon-box:hover .has-icon-bg .icon .icon-inner", "properties": 0}, {"selector": ".has-icon-bg .icon i,.has-icon-bg .icon img,.has-icon-bg .icon svg", "properties": 0}, {"selector": ".icon-box-left .has-icon-bg .icon", "properties": 0}, {"selector": ".icon-box-left,.icon-box-right", "properties": 0}, {"selector": ".icon-box-left .icon-box-img,.icon-box-right .icon-box-img", "properties": 0}, {"selector": ".icon-box-left .icon-box-text,.icon-box-right .icon-box-text", "properties": 0}, {"selector": ".icon-box-right .icon-box-text", "properties": 0}, {"selector": ".icon-box-left .icon-box-img+.icon-box-text", "properties": 0}, {"selector": ".icon-box-right .icon-box-img+.icon-box-text", "properties": 0}, {"selector": ".icon-box-center .icon-box-img", "properties": 0}, {"selector": ".links>li>a:before,i[class*=\" icon-\"],i[class^=icon-]", "properties": 0}, {"selector": ".widget-title i", "properties": 0}, {"selector": ".button i,button i", "properties": 0}, {"selector": ".button.open-video i", "properties": 0}, {"selector": "a.icon-remove,a.remove", "properties": 0}, {"selector": "a.icon:not(.button),button.icon:not(.button)", "properties": 0}, {"selector": ".header a.icon:not(.button)", "properties": 0}, {"selector": ".header .nav-small a.icon:not(.button)", "properties": 0}, {"selector": ".button.icon", "properties": 0}, {"selector": ".button.icon i", "properties": 0}, {"selector": ".button.icon i+span", "properties": 0}, {"selector": ".button.icon.is-small", "properties": 0}, {"selector": ".button.icon.is-small i", "properties": 0}, {"selector": ".button.icon.circle,.button.icon.round", "properties": 0}, {"selector": ".button.icon.circle>span,.button.icon.round>span", "properties": 0}, {"selector": ".button.icon.circle>span+i,.button.icon.round>span+i", "properties": 0}, {"selector": ".button.icon.circle>i,.button.icon.round>i", "properties": 0}, {"selector": ".button.icon.circle>i+span,.button.icon.round>i+span", "properties": 0}, {"selector": ".button.icon.circle>i:only-child,.button.icon.round>i:only-child", "properties": 0}, {"selector": ".nav>li>a>i", "properties": 0}, {"selector": ".nav>li>a>i+span", "properties": 0}, {"selector": ".nav li.has-icon-left>a>i,.nav li.has-icon-left>a>img,.nav li.has-icon-left>a>svg", "properties": 0}, {"selector": ".nav>li>a>span+i", "properties": 0}, {"selector": ".nav-small>li>a>i", "properties": 0}, {"selector": ".nav>li>a>i.icon-search", "properties": 0}, {"selector": ".nav>li>a>i.icon-menu", "properties": 0}, {"selector": ".nav>li.has-icon>a>i", "properties": 0}, {"selector": ".nav-vertical>li>a>i", "properties": 0}, {"selector": ".header-button a.icon:not(.button)", "properties": 0}, {"selector": ".header-button a.icon:not(.button) i", "properties": 0}, {"selector": ".header-button a.icon:not(.button) i.icon-search", "properties": 0}, {"selector": ".nav-small .header-button a.icon:not(.button)", "properties": 0}, {"selector": ".button:not(.icon)>i", "properties": 0}, {"selector": ".button:not(.icon)>span+i", "properties": 0}, {"selector": ".has-dropdown .icon-angle-down", "properties": 0}, {"selector": ".overlay-icon", "properties": 0}, {"selector": ".overlay-icon i", "properties": 0}, {"selector": ".has-hover:hover .overlay-icon", "properties": 0}, {"selector": ".box-overlay .overlay-icon,.box-shade .overlay-icon", "properties": 0}, {"selector": ".image-icon", "properties": 0}, {"selector": "span+.image-icon", "properties": 0}, {"selector": ".image-icon img", "properties": 0}, {"selector": ".nav-small .image-icon", "properties": 0}, {"selector": ".nav-small .image-icon img", "properties": 0}, {"selector": "[data-icon-label]", "properties": 0}, {"selector": "[data-icon-label=\"0\"]:after", "properties": 0}, {"selector": "[data-icon-label]:after", "properties": 0}, {"selector": ".nav-small [data-icon-label]:after", "properties": 0}, {"selector": ".button [data-icon-label]:after", "properties": 0}, {"selector": ".reveal-icon i", "properties": 0}, {"selector": ".reveal-icon i,.reveal-icon span", "properties": 0}, {"selector": ".reveal-icon span", "properties": 0}, {"selector": ".reveal-icon i+span", "properties": 0}, {"selector": ".reveal-icon:hover>span", "properties": 0}, {"selector": ".reveal-icon:not(:hover) i", "properties": 0}, {"selector": "img", "properties": 0}, {"selector": ".col-inner>.img:not(:last-child),.col>.img:not(:last-child)", "properties": 0}, {"selector": ".img,.img .img-inner", "properties": 0}, {"selector": ".img .img-inner", "properties": 0}, {"selector": ".img,.img img", "properties": 0}, {"selector": ".overlay", "properties": 0}, {"selector": ".img .caption,.overlay", "properties": 0}, {"selector": ".img .caption", "properties": 0}, {"selector": ".img .caption-show,.img:hover .caption", "properties": 0}, {"selector": ".lazy-load", "properties": 0}, {"selector": ".has-format,.image-cover,.video-fit", "properties": 0}, {"selector": ".video-fit", "properties": 0}, {"selector": ".has-equal-box-heights .box-image img,.has-format img,.image-cover img,.video-fit iframe,.video-fit video,img.back-image", "properties": 0}, {"selector": ".has-equal-box-heights .box-image", "properties": 0}, {"selector": ".video-fit iframe", "properties": 0}, {"selector": ".image-tools", "properties": 0}, {"selector": ".image-tools a:last-child", "properties": 0}, {"selector": ".image-tools a.button", "properties": 0}, {"selector": ".image-tools.bottom.left", "properties": 0}, {"selector": ".image-tools.top.right", "properties": 0}, {"selector": ".image-tools .cart-icon", "properties": 0}, {"selector": ".image-tools .wishlist-button:hover", "properties": 0}, {"selector": "@media only screen and (max-device-width:1024px)", "properties": 0}, {"selector": ".image-tools", "properties": 0}, {"selector": ".overlay-tools", "properties": 0}, {"selector": ".overlay-tools a", "properties": 0}, {"selector": ".overlay-tools a:hover", "properties": 0}, {"selector": ".tag-label", "properties": 0}, {"selector": ".has-hover:hover .tag-label", "properties": 0}, {"selector": ".lightbox-content", "properties": 0}, {"selector": ".lightbox-content .lightbox-inner", "properties": 0}, {"selector": ".pswp__bg", "properties": 0}, {"selector": "@media(min-width:549px)", "properties": 0}, {"selector": ".pswp__top-bar", "properties": 0}, {"selector": ".pswp__item", "properties": 0}, {"selector": ".pswp--visible .pswp__item", "properties": 0}, {"selector": ".pswp__caption__center", "properties": 0}, {"selector": ".mfp-bg", "properties": 0}, {"selector": ".mfp-bg.mfp-ready", "properties": 0}, {"selector": ".mfp-bg.mfp-removing", "properties": 0}, {"selector": ".mfp-wrap", "properties": 0}, {"selector": ".mfp-container,.mfp-wrap", "properties": 0}, {"selector": ".mfp-container", "properties": 0}, {"selector": ".mfp-container:before", "properties": 0}, {"selector": ".mfp-image-holder", "properties": 0}, {"selector": ".mfp-align-top .mfp-container:before", "properties": 0}, {"selector": ".mfp-content", "properties": 0}, {"selector": ".mfp-content-inner", "properties": 0}, {"selector": ".mfp-ready .mfp-content", "properties": 0}, {"selector": ".mfp-ready.mfp-removing .mfp-content", "properties": 0}, {"selector": ".mfp-ajax-holder .mfp-content,.mfp-inline-holder .mfp-content", "properties": 0}, {"selector": ".mfp-ajax-cur", "properties": 0}, {"selector": ".mfp-zoom-out-cur,.mfp-zoom-out-cur .mfp-image-holder .mfp-close", "properties": 0}, {"selector": ".mfp-zoom", "properties": 0}, {"selector": ".mfp-auto-cursor .mfp-content", "properties": 0}, {"selector": ".mfp-arrow,.mfp-close,.mfp-counter,.mfp-preloader", "properties": 0}, {"selector": ".mfp-loading.mfp-figure", "properties": 0}, {"selector": ".mfp-hide", "properties": 0}, {"selector": ".mfp-preloader", "properties": 0}, {"selector": ".mfp-preloader a", "properties": 0}, {"selector": ".mfp-preloader a:hover", "properties": 0}, {"selector": ".mfp-s-error .mfp-content,.mfp-s-ready .mfp-preloader", "properties": 0}, {"selector": ".mfp-close", "properties": 0}, {"selector": ".mfp-close svg", "properties": 0}, {"selector": ".mfp-content .mfp-close", "properties": 0}, {"selector": ".mfp-ready .mfp-close", "properties": 0}, {"selector": ".mfp-removing .mfp-close", "properties": 0}, {"selector": ".mfp-close:hover", "properties": 0}, {"selector": ".mfp-close-btn-in .mfp-close", "properties": 0}, {"selector": ".mfp-counter", "properties": 0}, {"selector": ".mfp-arrow", "properties": 0}, {"selector": ".mfp-arrow i", "properties": 0}, {"selector": ".mfp-arrow:hover", "properties": 0}, {"selector": ".mfp-arrow:active", "properties": 0}, {"selector": ".mfp-arrow-left", "properties": 0}, {"selector": ".mfp-arrow-right", "properties": 0}, {"selector": ".mfp-ready .mfp-arrow-left,.mfp-ready .mfp-arrow-right", "properties": 0}, {"selector": ".mfp-iframe-holder", "properties": 0}, {"selector": ".mfp-iframe-holder .mfp-content,.mfp-inline-holder .ux-mfp-inline-content--video", "properties": 0}, {"selector": ".mfp-iframe-holder .mfp-close", "properties": 0}, {"selector": ".mfp-iframe-scaler", "properties": 0}, {"selector": ".mfp-iframe-scaler iframe", "properties": 0}, {"selector": ".mfp-iframe-scaler iframe,.mfp-inline-holder .ux-mfp-inline-content--video", "properties": 0}, {"selector": ".mfp-inline-holder .ux-mfp-inline-content,img.mfp-img", "properties": 0}, {"selector": "img.mfp-img", "properties": 0}, {"selector": ".mfp-figure,img.mfp-img", "properties": 0}, {"selector": ".mfp-figure:after", "properties": 0}, {"selector": ".mfp-figure small", "properties": 0}, {"selector": ".mfp-figure figure", "properties": 0}, {"selector": ".mfp-bottom-bar", "properties": 0}, {"selector": ".mfp-title", "properties": 0}, {"selector": ".mfp-title a", "properties": 0}, {"selector": ".mfp-title a:hover", "properties": 0}, {"selector": ".mfp-image-holder .mfp-content", "properties": 0}, {"selector": ".mfp-gallery .mfp-image-holder .mfp-figure", "properties": 0}, {"selector": "@media screen and (max-height:300px),screen and (max-width:800px)and (orientation:landscape)", "properties": 0}, {"selector": ".mfp-img-mobile .mfp-image-holder", "properties": 0}, {"selector": ".mfp-img-mobile img.mfp-img", "properties": 0}, {"selector": ".mfp-img-mobile .mfp-figure:after", "properties": 0}, {"selector": ".mfp-img-mobile .mfp-figure small", "properties": 0}, {"selector": ".mfp-img-mobile .mfp-bottom-bar", "properties": 0}, {"selector": ".mfp-img-mobile .mfp-bottom-bar:empty", "properties": 0}, {"selector": ".mfp-img-mobile .mfp-counter", "properties": 0}, {"selector": "@media(max-width:849px)", "properties": 0}, {"selector": "body.body-scroll-lock--active .mfp-wrap", "properties": 0}, {"selector": "@media(prefers-reduced-motion)", "properties": 0}, {"selector": ".mfp-bg,.mfp-close,.mfp-content", "properties": 0}, {"selector": ".loading-spin,.processing", "properties": 0}, {"selector": ".loading-spin,.processing:before", "properties": 0}, {"selector": ".box-image.processing:before,.dark .loading-spin,.dark .processing:before,.dark.loading-spin,.dark.processing:before", "properties": 0}, {"selector": ".box-image.processing:after", "properties": 0}, {"selector": ".box-image.processing .image-tools", "properties": 0}, {"selector": ".woocommerce-checkout.processing:before", "properties": 0}, {"selector": ".loading-spin.centered,.processing:before", "properties": 0}, {"selector": ".loading-spin", "properties": 0}, {"selector": ".button.loading", "properties": 0}, {"selector": ".button.loading:after", "properties": 0}, {"selector": ".is-outline .button.loading:after", "properties": 0}, {"selector": ".ux-loader", "properties": 0}, {"selector": ".ux-loader__inner", "properties": 0}, {"selector": ".ux-loader--style-normal .ux-loader__inner,.ux-loader--style-spotlight .ux-loader__inner", "properties": 0}, {"selector": ".ux-loader--style-spotlight .ux-loader__inner", "properties": 0}, {"selector": ".ux-loader--style-spotlight .dark .ux-loader__inner", "properties": 0}, {"selector": ".ux-loader--position-sticky", "properties": 0}, {"selector": ".ux-loader--position-sticky .ux-loader__inner", "properties": 0}, {"selector": "@keyframes spin", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": ".ux-menu-link", "properties": 0}, {"selector": ".ux-menu-link--active .ux-menu-link__link,.ux-menu-link:hover .ux-menu-link__link", "properties": 0}, {"selector": ".dark .ux-menu-link__link", "properties": 0}, {"selector": ".dark .ux-menu-link--active .ux-menu-link__link,.dark .ux-menu-link:hover .ux-menu-link__link", "properties": 0}, {"selector": ".ux-menu.ux-menu--divider-solid .ux-menu-link:not(:last-of-type) .ux-menu-link__link", "properties": 0}, {"selector": ".ux-menu-link__link", "properties": 0}, {"selector": ".ux-menu-link__icon", "properties": 0}, {"selector": ".ux-menu-link__icon+.ux-menu-link__text", "properties": 0}, {"selector": ".ux-menu-title", "properties": 0}, {"selector": ".ux-menu-link+.ux-menu-title", "properties": 0}, {"selector": ".dark .ux-menu-title", "properties": 0}, {"selector": ".off-canvas .mfp-content", "properties": 0}, {"selector": ".off-canvas .nav-vertical>li>a", "properties": 0}, {"selector": ".off-canvas .nav-vertical li li>a", "properties": 0}, {"selector": ".off-canvas .hide-for-off-canvas", "properties": 0}, {"selector": ".off-canvas-left .mfp-content,.off-canvas-right .mfp-content", "properties": 0}, {"selector": ".off-canvas-left.dark .mfp-content,.off-canvas-right.dark .mfp-content", "properties": 0}, {"selector": ".off-canvas-right .mfp-content", "properties": 0}, {"selector": ".off-canvas-center .nav-vertical>li>a,.off-canvas-center .nav-vertical>li>ul>li a", "properties": 0}, {"selector": ".off-canvas-center .nav-vertical>li>a", "properties": 0}, {"selector": ".off-canvas-center .mfp-container", "properties": 0}, {"selector": ".off-canvas-center .mfp-content", "properties": 0}, {"selector": ".off-canvas-center .mfp-content .searchform", "properties": 0}, {"selector": ".off-canvas-center.mfp-bg.mfp-ready", "properties": 0}, {"selector": ".mfp-bg.off-canvas-center:not(.dark)", "properties": 0}, {"selector": ".off-canvas-center .nav-sidebar.nav-vertical>li", "properties": 0}, {"selector": ".off-canvas-center .nav-sidebar.nav-vertical>li>a", "properties": 0}, {"selector": ".off-canvas-center .nav-sidebar.nav-vertical>li .toggle", "properties": 0}, {"selector": ".off-canvas-center:not(.dark) .mfp-close", "properties": 0}, {"selector": ".off-canvas.mfp-removing .mfp-content", "properties": 0}, {"selector": ".off-canvas-left.mfp-ready .mfp-content,.off-canvas-right.mfp-ready .mfp-content", "properties": 0}, {"selector": ".has-off-canvas .off-canvas-blur", "properties": 0}, {"selector": ".has-off-canvas .off-canvas-zoom", "properties": 0}, {"selector": ".off-canvas .sidebar-menu", "properties": 0}, {"selector": ".off-canvas .sidebar-inner", "properties": 0}, {"selector": ".off-canvas:not(.off-canvas-center) .nav-vertical li>a", "properties": 0}, {"selector": ".off-canvas:not(.off-canvas-center) li.html", "properties": 0}, {"selector": "@media(prefers-reduced-motion)", "properties": 0}, {"selector": ".off-canvas-left .mfp-content,.off-canvas-right .mfp-content", "properties": 0}, {"selector": ".ux-relay__nav-button", "properties": 0}, {"selector": ".ux-relay__nav-button svg", "properties": 0}, {"selector": ".ux-relay__nav-button:not([disabled]):hover svg", "properties": 0}, {"selector": ".stack", "properties": 0}, {"selector": ".stack>.text>:first-child", "properties": 0}, {"selector": ".stack>.text>:last-child", "properties": 0}, {"selector": ".stack-row", "properties": 0}, {"selector": ".stack-row>*", "properties": 0}, {"selector": ".stack-row>*~*", "properties": 0}, {"selector": ".stack-col", "properties": 0}, {"selector": ".stack-col>*", "properties": 0}, {"selector": ".stack-col>*~*", "properties": 0}, {"selector": ".items-stretch", "properties": 0}, {"selector": ".items-start", "properties": 0}, {"selector": ".items-center", "properties": 0}, {"selector": ".items-end", "properties": 0}, {"selector": ".items-baseline", "properties": 0}, {"selector": ".justify-start", "properties": 0}, {"selector": ".justify-center", "properties": 0}, {"selector": ".justify-end", "properties": 0}, {"selector": ".justify-between", "properties": 0}, {"selector": ".justify-around", "properties": 0}, {"selector": "@media(max-width:849px)", "properties": 0}, {"selector": ".md\\:stack-row", "properties": 0}, {"selector": ".md\\:stack-row>*", "properties": 0}, {"selector": ".md\\:stack-row>*~*", "properties": 0}, {"selector": ".md\\:stack-col", "properties": 0}, {"selector": ".md\\:stack-col>*", "properties": 0}, {"selector": ".md\\:stack-col>*~*", "properties": 0}, {"selector": ".md\\:items-stretch", "properties": 0}, {"selector": ".md\\:items-start", "properties": 0}, {"selector": ".md\\:items-center", "properties": 0}, {"selector": ".md\\:items-end", "properties": 0}, {"selector": ".md\\:items-baseline", "properties": 0}, {"selector": ".md\\:justify-start", "properties": 0}, {"selector": ".md\\:justify-center", "properties": 0}, {"selector": ".md\\:justify-end", "properties": 0}, {"selector": ".md\\:justify-between", "properties": 0}, {"selector": ".md\\:justify-around", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".sm\\:stack-row", "properties": 0}, {"selector": ".sm\\:stack-row>*", "properties": 0}, {"selector": ".sm\\:stack-row>*~*", "properties": 0}, {"selector": ".sm\\:stack-col", "properties": 0}, {"selector": ".sm\\:stack-col>*", "properties": 0}, {"selector": ".sm\\:stack-col>*~*", "properties": 0}, {"selector": ".sm\\:items-stretch", "properties": 0}, {"selector": ".sm\\:items-start", "properties": 0}, {"selector": ".sm\\:items-center", "properties": 0}, {"selector": ".sm\\:items-end", "properties": 0}, {"selector": ".sm\\:items-baseline", "properties": 0}, {"selector": ".sm\\:justify-start", "properties": 0}, {"selector": ".sm\\:justify-center", "properties": 0}, {"selector": ".sm\\:justify-end", "properties": 0}, {"selector": ".sm\\:justify-between", "properties": 0}, {"selector": ".sm\\:justify-around", "properties": 0}, {"selector": "table", "properties": 0}, {"selector": "td,th", "properties": 0}, {"selector": "td", "properties": 0}, {"selector": "td:first-child,th:first-child", "properties": 0}, {"selector": "td:last-child,th:last-child", "properties": 0}, {"selector": ".dark td,.dark th", "properties": 0}, {"selector": "td .label,td dl,td form,td input,td label,td p,td select", "properties": 0}, {"selector": "label", "properties": 0}, {"selector": ".table", "properties": 0}, {"selector": ".table-cell", "properties": 0}, {"selector": "@media(max-width:849px)", "properties": 0}, {"selector": ".touch-scroll-table", "properties": 0}, {"selector": ".touch-scroll-table table", "properties": 0}, {"selector": ".text-box", "properties": 0}, {"selector": ".text-box .text-box-content", "properties": 0}, {"selector": ".text-box-circle .text-box-content,.text-box-square .text-box-content", "properties": 0}, {"selector": ".text-box-circle .text-inner,.text-box-square .text-inner", "properties": 0}, {"selector": ".text-box-circle .border,.text-box-circle .text-box-content", "properties": 0}, {"selector": ".slider-wrapper", "properties": 0}, {"selector": ".flickity-slider>.img,.slider-wrapper:last-child,.slider>.img", "properties": 0}, {"selector": ".row-slider,.slider", "properties": 0}, {"selector": ".slider-full .flickity-slider>.col", "properties": 0}, {"selector": ".slider-load-first", "properties": 0}, {"selector": ".slider-load-first:not(.flickity-enabled)", "properties": 0}, {"selector": ".slider-load-first:not(.flickity-enabled)>div", "properties": 0}, {"selector": ".slider:not(.flickity-enabled)", "properties": 0}, {"selector": ".row-slider::-webkit-scrollbar,.slider::-webkit-scrollbar", "properties": 0}, {"selector": ".row.row-slider:not(.flickity-enabled)", "properties": 0}, {"selector": ".slider:not(.flickity-enabled)>*", "properties": 0}, {"selector": ".slider:not(.flickity-enabled)>a,.slider>a,.slider>a>img,.slider>div:not(.col),.slider>img,.slider>p", "properties": 0}, {"selector": ".flickity-slider>a,.flickity-slider>a>img,.flickity-slider>div:not(.col),.flickity-slider>figure,.flickity-slider>img,.flickity-slider>p", "properties": 0}, {"selector": ".flickity-slider>.row:not(.is-selected)", "properties": 0}, {"selector": ".flickity-enabled", "properties": 0}, {"selector": ".flickity-enabled:focus", "properties": 0}, {"selector": ".flickity-viewport", "properties": 0}, {"selector": ".flickity-slider", "properties": 0}, {"selector": ".slider-has-parallax .bg", "properties": 0}, {"selector": ".is-dragging .flickity-viewport .flickity-slider", "properties": 0}, {"selector": ".flickity-enabled.is-draggable", "properties": 0}, {"selector": ".flickity-enabled.is-draggable .flickity-viewport", "properties": 0}, {"selector": ".flickity-enabled.is-draggable .flickity-viewport.is-pointer-down", "properties": 0}, {"selector": ".flickity-prev-next-button", "properties": 0}, {"selector": ".flickity-prev-next-button.next", "properties": 0}, {"selector": ".flickity-prev-next-button.previous", "properties": 0}, {"selector": ".slider-show-nav .flickity-prev-next-button,.slider:hover .flickity-prev-next-button", "properties": 0}, {"selector": ".slider .flickity-prev-next-button:hover", "properties": 0}, {"selector": ".slider .flickity-prev-next-button:hover .arrow,.slider .flickity-prev-next-button:hover svg", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".slider-nav-outside .flickity-prev-next-button.next", "properties": 0}, {"selector": ".slider-nav-outside .flickity-prev-next-button.previous", "properties": 0}, {"selector": ".flickity-prev-next-button:disabled,button.flickity-prev-next-button[disabled]", "properties": 0}, {"selector": ".flickity-prev-next-button svg", "properties": 0}, {"selector": ".slider-nav-push:not(.slider-nav-reveal) .flickity-prev-next-button", "properties": 0}, {"selector": ".slider-nav-push.slider-nav-reveal .flickity-prev-next-button svg", "properties": 0}, {"selector": ".flickity-prev-next-button .arrow,.flickity-prev-next-button svg", "properties": 0}, {"selector": ".animate-height,.slider-auto-height", "properties": 0}, {"selector": ".flickity-prev-next-button.no-svg", "properties": 0}, {"selector": ".slider-no-arrows .flickity-prev-next-button", "properties": 0}, {"selector": ".slider-type-fade .flickity-slider", "properties": 0}, {"selector": ".slider-type-fade .flickity-slider>*", "properties": 0}, {"selector": ".slider-type-fade .flickity-slider>.is-selected", "properties": 0}, {"selector": ".slider-type-fade .flickity-page-dots,.slider-type-fade .flickity-prev-next-button", "properties": 0}, {"selector": "@media screen and (min-width:850px)", "properties": 0}, {"selector": ".slider-type-fade.product-gallery-stacked .flickity-slider>*", "properties": 0}, {"selector": "@media(prefers-reduced-motion)", "properties": 0}, {"selector": ".flickity-prev-next-button.next,.flickity-prev-next-button.previous", "properties": 0}, {"selector": ".flickity-page-dots", "properties": 0}, {"selector": ".row-slider .flickity-page-dots", "properties": 0}, {"selector": ".flickity-rtl .flickity-page-dots", "properties": 0}, {"selector": ".flickity-page-dots .dot", "properties": 0}, {"selector": ".nav-dots-small .flickity-page-dots .dot,.row-slider .flickity-page-dots .dot", "properties": 0}, {"selector": ".flickity-page-dots .dot:hover", "properties": 0}, {"selector": ".flickity-page-dots .dot:first-child:last-child", "properties": 0}, {"selector": ".flickity-page-dots .dot.is-selected", "properties": 0}, {"selector": ".slider-nav-dots-dashes .flickity-page-dots .dot", "properties": 0}, {"selector": ".slider-nav-dots-dashes-spaced .flickity-page-dots .dot", "properties": 0}, {"selector": ".slider-nav-dots-simple .flickity-page-dots .dot", "properties": 0}, {"selector": ".slider-nav-dots-square .flickity-page-dots .dot", "properties": 0}, {"selector": ".slider-nav-circle .flickity-prev-next-button .arrow,.slider-nav-circle .flickity-prev-next-button svg", "properties": 0}, {"selector": ".slider-nav-circle .flickity-prev-next-button:hover .arrow,.slider-nav-circle .flickity-prev-next-button:hover svg", "properties": 0}, {"selector": ".slider-nav-outside.slider-nav-circle .next", "properties": 0}, {"selector": ".slider-nav-outside.slider-nav-circle .previous", "properties": 0}, {"selector": ".slider-nav-reveal", "properties": 0}, {"selector": ".slider-nav-reveal .flickity-prev-next-button,.slider-nav-reveal .flickity-prev-next-button:hover", "properties": 0}, {"selector": ".dark .slider-nav-reveal .flickity-prev-next-button", "properties": 0}, {"selector": ".slider-nav-reveal .flickity-prev-next-button.next", "properties": 0}, {"selector": ".slider-nav-reveal .flickity-prev-next-button.previous", "properties": 0}, {"selector": ".slider-nav-light .flickity-prev-next-button", "properties": 0}, {"selector": ".slider-nav-light .flickity-prev-next-button .arrow,.slider-nav-light .flickity-prev-next-button svg", "properties": 0}, {"selector": ".slider-nav-light .flickity-page-dots .dot", "properties": 0}, {"selector": ".slider-nav-light .flickity-page-dots .dot.is-selected,.slider-nav-light.slider-nav-dots-simple .flickity-page-dots .dot", "properties": 0}, {"selector": ".slider-style-container .flickity-slider>*,.slider-style-focus .flickity-slider>*,.slider-style-shadow .flickity-slider>*", "properties": 0}, {"selector": ".slider-style-container:not(.flickity-enabled) .ux_banner,.slider-style-focus:not(.flickity-enabled) .ux_banner,.slider-style-shadow:not(.flickity-enabled) .ux_banner", "properties": 0}, {"selector": ".slider-style-container .ux_banner,.slider-style-focus .ux_banner,.slider-style-shadow .ux_banner", "properties": 0}, {"selector": ".slider-style-container .flickity-slider>:not(.is-selected),.slider-style-focus .flickity-slider>:not(.is-selected),.slider-style-shadow .flickity-slider>:not(.is-selected)", "properties": 0}, {"selector": ".slider-style-shadow", "properties": 0}, {"selector": ".slider-style-shadow .flickity-slider>:before", "properties": 0}, {"selector": ".slider-style-shadow .flickity-slider>:not(.is-selected)", "properties": 0}, {"selector": ".slider-style-shadow .flickity-slider>:not(.is-selected):before", "properties": 0}, {"selector": ".slider-style-shadow .flickity-slider>.is-selected", "properties": 0}, {"selector": ".slider-style-focus", "properties": 0}, {"selector": ".slider-style-focus .flickity-page-dots", "properties": 0}, {"selector": ".slider-style-focus .flickity-slider>*", "properties": 0}, {"selector": ".slider-style-focus .flickity-slider>:not(.is-selected)", "properties": 0}, {"selector": "@media screen and (max-width:549px)", "properties": 0}, {"selector": ".slider-wrapper .flickity-prev-next-button", "properties": 0}, {"selector": ".row-slider .flickity-prev-next-button", "properties": 0}, {"selector": ".row-slider .flickity-prev-next-button svg", "properties": 0}, {"selector": ".flickity-page-dots", "properties": 0}, {"selector": ".flickity-slider .banner-layers", "properties": 0}, {"selector": "a", "properties": 0}, {"selector": ".button:focus:not(:focus-visible),a:focus:not(:focus-visible),button:focus:not(:focus-visible),input:focus:not(:focus-visible)", "properties": 0}, {"selector": ".button:focus-visible,a:focus-visible,button:focus-visible,input:focus-visible", "properties": 0}, {"selector": "a.plain", "properties": 0}, {"selector": ".nav-dark a.plain:hover", "properties": 0}, {"selector": "a.icon-circle:hover,a.remove:hover,a:hover", "properties": 0}, {"selector": ".primary:focus-visible,.submit-button:focus-visible,button[type=submit]:focus-visible", "properties": 0}, {"selector": ".secondary:focus-visible", "properties": 0}, {"selector": ".alt:focus-visible", "properties": 0}, {"selector": ".dark .widget a,.dark a", "properties": 0}, {"selector": ".dark .widget a:hover,.dark a:hover", "properties": 0}, {"selector": "ul.links", "properties": 0}, {"selector": "ul.links li", "properties": 0}, {"selector": "ul.links li a", "properties": 0}, {"selector": "ul.links li:before", "properties": 0}, {"selector": "ul.links li:first-child", "properties": 0}, {"selector": "ul.links li:last-child", "properties": 0}, {"selector": ".next-prev-nav .flex-col", "properties": 0}, {"selector": ".next-prev-nav i", "properties": 0}, {"selector": ".next-prev-nav .flex-col+.flex-col", "properties": 0}, {"selector": "ul", "properties": 0}, {"selector": "ol", "properties": 0}, {"selector": "ol,ul", "properties": 0}, {"selector": "ol ol,ol ul,ul ol,ul ul", "properties": 0}, {"selector": "li", "properties": 0}, {"selector": "dl dd,dl dt", "properties": 0}, {"selector": "dl dd p,dl dt p", "properties": 0}, {"selector": "dl", "properties": 0}, {"selector": "ul.ul-reset,ul.ul-reset>li", "properties": 0}, {"selector": "ul.ul-reset>li", "properties": 0}, {"selector": ".col-inner ol li,.col-inner ul li,.entry-content ol li,.entry-content ul li,.entry-summary ol li,.entry-summary ul li", "properties": 0}, {"selector": ".col-inner ol li.tab,.col-inner ul li.tab,.entry-content ol li.tab,.entry-content ul li.tab,.entry-summary ol li.tab,.entry-summary ul li.tab", "properties": 0}, {"selector": "ul li.bullet-arrow,ul li.bullet-checkmark,ul li.bullet-cross,ul li.bullet-star", "properties": 0}, {"selector": ".dark ul li.bullet-arrow,.dark ul li.bullet-checkmark,.dark ul li.bullet-cross,.dark ul li.bullet-star", "properties": 0}, {"selector": "ul li.bullet-arrow:before,ul li.bullet-checkmark:before,ul li.bullet-star:before", "properties": 0}, {"selector": "ul li.bullet-cross:before", "properties": 0}, {"selector": "ul li.bullet-checkmark:before", "properties": 0}, {"selector": "ul li.bullet-cross:before", "properties": 0}, {"selector": "ul li.bullet-arrow:before", "properties": 0}, {"selector": "ul li.bullet-star:before", "properties": 0}, {"selector": ".button,button,fieldset,input,select,textarea", "properties": 0}, {"selector": "blockquote,dl,figure,form,ol,p,pre,ul", "properties": 0}, {"selector": "form p", "properties": 0}, {"selector": "body", "properties": 0}, {"selector": "h1,h2,h3,h4,h5,h6", "properties": 0}, {"selector": ".h1,h1", "properties": 0}, {"selector": ".h1,.h2,h1,h2", "properties": 0}, {"selector": ".h2,h2", "properties": 0}, {"selector": ".h3,h3", "properties": 0}, {"selector": ".h4,h4", "properties": 0}, {"selector": ".h5,h5", "properties": 0}, {"selector": ".h6,h6", "properties": 0}, {"selector": "h1.entry-title.mb", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": "h1", "properties": 0}, {"selector": "h2", "properties": 0}, {"selector": "h3", "properties": 0}, {"selector": "h6 span", "properties": 0}, {"selector": "h3 label", "properties": 0}, {"selector": "p", "properties": 0}, {"selector": "h1>span,h2>span,h3>span,h4>span,h5>span,h6>span,p.headline>span", "properties": 0}, {"selector": "a.lead,p.lead", "properties": 0}, {"selector": ".uppercase,h6,span.widget-title,th", "properties": 0}, {"selector": ".lowercase", "properties": 0}, {"selector": "span.widget-title", "properties": 0}, {"selector": ".is-normal", "properties": 0}, {"selector": ".is-bold", "properties": 0}, {"selector": ".is-thin,.thin-font", "properties": 0}, {"selector": ".is-thin strong,.thin-font strong", "properties": 0}, {"selector": ".is-italic", "properties": 0}, {"selector": ".is-uppercase,.uppercase", "properties": 0}, {"selector": ".alt-font,.is-alt-font", "properties": 0}, {"selector": ".is-xxxlarge", "properties": 0}, {"selector": ".is-xxlarge", "properties": 0}, {"selector": ".is-xlarge", "properties": 0}, {"selector": ".is-larger", "properties": 0}, {"selector": ".is-large", "properties": 0}, {"selector": ".is-small,.is-small.button", "properties": 0}, {"selector": ".is-smaller", "properties": 0}, {"selector": ".is-xsmall", "properties": 0}, {"selector": ".is-xxsmall", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".is-xxlarge", "properties": 0}, {"selector": ".is-xlarge", "properties": 0}, {"selector": ".is-larger", "properties": 0}, {"selector": ".is-large", "properties": 0}, {"selector": ".box-text a:not(.button),.box-text h1,.box-text h2,.box-text h3,.box-text h4,.box-text h5,.box-text h6", "properties": 0}, {"selector": ".box-text p", "properties": 0}, {"selector": ".box-text .button", "properties": 0}, {"selector": ".banner .button", "properties": 0}, {"selector": ".banner .is-divider,.banner .text-center .is-divider", "properties": 0}, {"selector": ".banner h1", "properties": 0}, {"selector": ".banner h2", "properties": 0}, {"selector": ".banner h3", "properties": 0}, {"selector": ".banner h1,.banner h2,.banner h3", "properties": 0}, {"selector": ".banner h4", "properties": 0}, {"selector": ".banner h5,.banner h6,.banner p", "properties": 0}, {"selector": ".line-height-small", "properties": 0}, {"selector": "[data-line-height=xs]", "properties": 0}, {"selector": "[data-line-height=s]", "properties": 0}, {"selector": "[data-line-height=m]", "properties": 0}, {"selector": "[data-line-height=l]", "properties": 0}, {"selector": "[data-line-height=xl]", "properties": 0}, {"selector": ".nav>li>a", "properties": 0}, {"selector": ".nav>li.html", "properties": 0}, {"selector": ".nav-size-xsmall>li>a", "properties": 0}, {"selector": ".nav-size-small>li>a", "properties": 0}, {"selector": ".nav-size-medium>li>a", "properties": 0}, {"selector": ".nav-size-large>li>a", "properties": 0}, {"selector": ".nav-size-xlarge>li>a", "properties": 0}, {"selector": ".nav-spacing-xsmall>li", "properties": 0}, {"selector": ".nav-spacing-small>li", "properties": 0}, {"selector": ".nav-spacing-medium>li", "properties": 0}, {"selector": ".nav-spacing-large>li", "properties": 0}, {"selector": ".nav-spacing-xlarge>li", "properties": 0}, {"selector": ".fancy-underline", "properties": 0}, {"selector": ".fancy-underline:after", "properties": 0}, {"selector": "span.count-up", "properties": 0}, {"selector": "span.count-up.active", "properties": 0}, {"selector": "[data-text-color=primary]", "properties": 0}, {"selector": "[data-text-color=secondary]", "properties": 0}, {"selector": "[data-text-color=alert]", "properties": 0}, {"selector": "[data-text-color=success]", "properties": 0}, {"selector": "[data-text-bg=primary]", "properties": 0}, {"selector": "[data-text-bg=secondary]", "properties": 0}, {"selector": "[data-text-bg=alert]", "properties": 0}, {"selector": "[data-text-bg=success]", "properties": 0}, {"selector": ".text-bordered-dark,.text-bordered-primary,.text-bordered-white", "properties": 0}, {"selector": ".text-bordered-dark", "properties": 0}, {"selector": "h1.text-bordered-dark,h1.text-bordered-primary,h1.text-bordered-white", "properties": 0}, {"selector": ".text-boarder-top-bottom-dark,.text-boarder-top-bottom-white", "properties": 0}, {"selector": ".text-boarder-top-bottom-dark", "properties": 0}, {"selector": "blockquote", "properties": 0}, {"selector": ".dark blockquote", "properties": 0}, {"selector": ".clear:after,.clearfix:after,.container:after,.row:after", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".hide-for-small,[data-show=hide-for-small]", "properties": 0}, {"selector": ".small-text-center", "properties": 0}, {"selector": "@media(min-width:550px)", "properties": 0}, {"selector": ".show-for-small,[data-show=show-for-small]", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".show-for-medium,[data-show=show-for-medium]", "properties": 0}, {"selector": "@media(max-width:849px)", "properties": 0}, {"selector": ".hide-for-medium,[data-show=hide-for-medium]", "properties": 0}, {"selector": ".medium-text-center .pull-left,.medium-text-center .pull-right", "properties": 0}, {"selector": ".medium-text-center .ml", "properties": 0}, {"selector": ".medium-text-center .mr", "properties": 0}, {"selector": ".medium-text-center", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".hide-for-large,[data-show=hide-for-large]", "properties": 0}, {"selector": ".expand,.full-width", "properties": 0}, {"selector": ".pull-right", "properties": 0}, {"selector": ".pull-left", "properties": 0}, {"selector": ".mb", "properties": 0}, {"selector": ".mt", "properties": 0}, {"selector": ".mr", "properties": 0}, {"selector": ".ml", "properties": 0}, {"selector": ".mb-0", "properties": 0}, {"selector": ".ml-0", "properties": 0}, {"selector": ".mr-0", "properties": 0}, {"selector": ".mt-0", "properties": 0}, {"selector": ".mb-half", "properties": 0}, {"selector": ".mt-half", "properties": 0}, {"selector": ".mr-half", "properties": 0}, {"selector": ".ml-half", "properties": 0}, {"selector": ".mb-half:last-child,.mb:last-child", "properties": 0}, {"selector": ".pb", "properties": 0}, {"selector": ".pt", "properties": 0}, {"selector": ".pb-half", "properties": 0}, {"selector": ".pt-half", "properties": 0}, {"selector": ".pb-0", "properties": 0}, {"selector": ".pt-0", "properties": 0}, {"selector": ".no-margin", "properties": 0}, {"selector": ".no-padding", "properties": 0}, {"selector": ".inner-padding", "properties": 0}, {"selector": ".first-reset :first-child", "properties": 0}, {"selector": ".last-reset :last-child", "properties": 0}, {"selector": ".no-select", "properties": 0}, {"selector": ".text-left", "properties": 0}, {"selector": ".text-center", "properties": 0}, {"selector": ".text-right", "properties": 0}, {"selector": ".text-center .is-divider,.text-center .is-star-rating,.text-center .star-rating,.text-center>div,.text-center>div>div", "properties": 0}, {"selector": ".text-center .pull-left,.text-center .pull-right", "properties": 0}, {"selector": ".text-left .is-divider,.text-left .is-star-rating,.text-left .star-rating", "properties": 0}, {"selector": ".text-right .is-divider,.text-right .is-star-rating,.text-right .star-rating,.text-right>div,.text-right>div>div", "properties": 0}, {"selector": ".relative", "properties": 0}, {"selector": ".absolute", "properties": 0}, {"selector": ".fixed", "properties": 0}, {"selector": ".top", "properties": 0}, {"selector": ".right", "properties": 0}, {"selector": ".left", "properties": 0}, {"selector": ".bottom,.fill", "properties": 0}, {"selector": ".fill", "properties": 0}, {"selector": ".v-center", "properties": 0}, {"selector": ".h-center", "properties": 0}, {"selector": ".h-center.v-center", "properties": 0}, {"selector": ".pull-right", "properties": 0}, {"selector": ".pull-left", "properties": 0}, {"selector": ".is-full-height", "properties": 0}, {"selector": ".bg-fill", "properties": 0}, {"selector": ".bg-top", "properties": 0}, {"selector": ".circle,.circle img", "properties": 0}, {"selector": ".round", "properties": 0}, {"selector": ".has-border", "properties": 0}, {"selector": ".dashed-border", "properties": 0}, {"selector": ".success-border", "properties": 0}, {"selector": ".bt", "properties": 0}, {"selector": ".bb", "properties": 0}, {"selector": ".bl", "properties": 0}, {"selector": ".br", "properties": 0}, {"selector": ".hidden", "properties": 0}, {"selector": ".is-invisible", "properties": 0}, {"selector": ".z-1", "properties": 0}, {"selector": ".z-2", "properties": 0}, {"selector": ".z-3", "properties": 0}, {"selector": ".z-4", "properties": 0}, {"selector": ".z-5", "properties": 0}, {"selector": ".z-top", "properties": 0}, {"selector": ".z-top-2", "properties": 0}, {"selector": ".z-top-3", "properties": 0}, {"selector": ".no-click", "properties": 0}, {"selector": ".no-wrap", "properties": 0}, {"selector": ".primary-color", "properties": 0}, {"selector": ".secondary-color", "properties": 0}, {"selector": ".success-color", "properties": 0}, {"selector": ".alert-color", "properties": 0}, {"selector": ".bg-primary-color", "properties": 0}, {"selector": ".bg-secondary-color", "properties": 0}, {"selector": ".bg-success-color", "properties": 0}, {"selector": ".bg-alert-color", "properties": 0}, {"selector": ".is-transparent", "properties": 0}, {"selector": ".inline", "properties": 0}, {"selector": ".block", "properties": 0}, {"selector": ".flex", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".small-block", "properties": 0}, {"selector": ".inline-block", "properties": 0}, {"selector": ".inline-images img,img.inline", "properties": 0}, {"selector": ".is-well", "properties": 0}, {"selector": ".no-overflow", "properties": 0}, {"selector": ".no-text-overflow", "properties": 0}, {"selector": ".strong", "properties": 0}, {"selector": ".op-4", "properties": 0}, {"selector": ".op-5", "properties": 0}, {"selector": ".op-6", "properties": 0}, {"selector": ".op-7", "properties": 0}, {"selector": ".op-8", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".sm-touch-scroll", "properties": 0}, {"selector": ".no-scrollbar", "properties": 0}, {"selector": ".no-scrollbar::-webkit-scrollbar", "properties": 0}, {"selector": ".screen-reader-text", "properties": 0}, {"selector": ".screen-reader-text:focus", "properties": 0}, {"selector": "@media screen and (max-width:549px)", "properties": 0}, {"selector": ".x5", "properties": 0}, {"selector": ".x15", "properties": 0}, {"selector": ".x25", "properties": 0}, {"selector": ".x35", "properties": 0}, {"selector": ".x45", "properties": 0}, {"selector": ".x55", "properties": 0}, {"selector": ".x65", "properties": 0}, {"selector": ".x75", "properties": 0}, {"selector": ".x85", "properties": 0}, {"selector": ".x95", "properties": 0}, {"selector": ".x0", "properties": 0}, {"selector": ".x10", "properties": 0}, {"selector": ".x20", "properties": 0}, {"selector": ".x30", "properties": 0}, {"selector": ".x40", "properties": 0}, {"selector": ".x60", "properties": 0}, {"selector": ".x70", "properties": 0}, {"selector": ".x80", "properties": 0}, {"selector": ".x90", "properties": 0}, {"selector": ".x100", "properties": 0}, {"selector": ".y0", "properties": 0}, {"selector": ".y10", "properties": 0}, {"selector": ".y20", "properties": 0}, {"selector": ".y30", "properties": 0}, {"selector": ".y40", "properties": 0}, {"selector": ".y60", "properties": 0}, {"selector": ".y70", "properties": 0}, {"selector": ".y80", "properties": 0}, {"selector": ".y90", "properties": 0}, {"selector": ".y100", "properties": 0}, {"selector": ".y5", "properties": 0}, {"selector": ".y15", "properties": 0}, {"selector": ".y25", "properties": 0}, {"selector": ".y35", "properties": 0}, {"selector": ".y45", "properties": 0}, {"selector": ".y55", "properties": 0}, {"selector": ".y65", "properties": 0}, {"selector": ".y75", "properties": 0}, {"selector": ".y85", "properties": 0}, {"selector": ".y95", "properties": 0}, {"selector": ".x50", "properties": 0}, {"selector": ".y50", "properties": 0}, {"selector": ".y50.x50", "properties": 0}, {"selector": "@media screen and (min-width:550px)and (max-width:849px)", "properties": 0}, {"selector": ".md-x5", "properties": 0}, {"selector": ".md-x15", "properties": 0}, {"selector": ".md-x25", "properties": 0}, {"selector": ".md-x35", "properties": 0}, {"selector": ".md-x45", "properties": 0}, {"selector": ".md-x55", "properties": 0}, {"selector": ".md-x65", "properties": 0}, {"selector": ".md-x75", "properties": 0}, {"selector": ".md-x85", "properties": 0}, {"selector": ".md-x95", "properties": 0}, {"selector": ".md-y5", "properties": 0}, {"selector": ".md-y15", "properties": 0}, {"selector": ".md-y25", "properties": 0}, {"selector": ".md-y35", "properties": 0}, {"selector": ".md-y45", "properties": 0}, {"selector": ".md-y55", "properties": 0}, {"selector": ".md-y65", "properties": 0}, {"selector": ".md-y75", "properties": 0}, {"selector": ".md-y85", "properties": 0}, {"selector": ".md-y95", "properties": 0}, {"selector": ".md-x0", "properties": 0}, {"selector": ".md-x10", "properties": 0}, {"selector": ".md-x20", "properties": 0}, {"selector": ".md-x30", "properties": 0}, {"selector": ".md-x40", "properties": 0}, {"selector": ".md-x60", "properties": 0}, {"selector": ".md-x70", "properties": 0}, {"selector": ".md-x80", "properties": 0}, {"selector": ".md-x90", "properties": 0}, {"selector": ".md-x100", "properties": 0}, {"selector": ".md-y0", "properties": 0}, {"selector": ".md-y10", "properties": 0}, {"selector": ".md-y20", "properties": 0}, {"selector": ".md-y30", "properties": 0}, {"selector": ".md-y40", "properties": 0}, {"selector": ".md-y60", "properties": 0}, {"selector": ".md-y70", "properties": 0}, {"selector": ".md-y80", "properties": 0}, {"selector": ".md-y90", "properties": 0}, {"selector": ".md-y100", "properties": 0}, {"selector": ".md-x50", "properties": 0}, {"selector": ".md-y50", "properties": 0}, {"selector": ".md-x50.md-y50", "properties": 0}, {"selector": "@media screen and (min-width:850px)", "properties": 0}, {"selector": ".lg-x5", "properties": 0}, {"selector": ".lg-x15", "properties": 0}, {"selector": ".lg-x25", "properties": 0}, {"selector": ".lg-x35", "properties": 0}, {"selector": ".lg-x45", "properties": 0}, {"selector": ".lg-x55", "properties": 0}, {"selector": ".lg-x65", "properties": 0}, {"selector": ".lg-x75", "properties": 0}, {"selector": ".lg-x85", "properties": 0}, {"selector": ".lg-x95", "properties": 0}, {"selector": ".lg-y5", "properties": 0}, {"selector": ".lg-y15", "properties": 0}, {"selector": ".lg-y25", "properties": 0}, {"selector": ".lg-y35", "properties": 0}, {"selector": ".lg-y45", "properties": 0}, {"selector": ".lg-y55", "properties": 0}, {"selector": ".lg-y65", "properties": 0}, {"selector": ".lg-y75", "properties": 0}, {"selector": ".lg-y85", "properties": 0}, {"selector": ".lg-y95", "properties": 0}, {"selector": ".lg-x0", "properties": 0}, {"selector": ".lg-x10", "properties": 0}, {"selector": ".lg-x20", "properties": 0}, {"selector": ".lg-x30", "properties": 0}, {"selector": ".lg-x40", "properties": 0}, {"selector": ".lg-x60", "properties": 0}, {"selector": ".lg-x70", "properties": 0}, {"selector": ".lg-x80", "properties": 0}, {"selector": ".lg-x90", "properties": 0}, {"selector": ".lg-x100", "properties": 0}, {"selector": ".lg-y0", "properties": 0}, {"selector": ".lg-y10", "properties": 0}, {"selector": ".lg-y20", "properties": 0}, {"selector": ".lg-y30", "properties": 0}, {"selector": ".lg-y40", "properties": 0}, {"selector": ".lg-y60", "properties": 0}, {"selector": ".lg-y70", "properties": 0}, {"selector": ".lg-y80", "properties": 0}, {"selector": ".lg-y90", "properties": 0}, {"selector": ".lg-y100", "properties": 0}, {"selector": ".lg-x50", "properties": 0}, {"selector": ".lg-y50", "properties": 0}, {"selector": ".lg-x50.lg-y50", "properties": 0}, {"selector": ".res-text", "properties": 0}, {"selector": ".small-1 .res-text,.small-2 .res-text,.small-3 .res-text,.widget-area .res-text", "properties": 0}, {"selector": ".small-1 .res-text h6,.small-2 .res-text h6,.small-3 .res-text h6", "properties": 0}, {"selector": ".small-1 .res-text p.lead,.small-2 .res-text p.lead,.small-3 .res-text p.lead", "properties": 0}, {"selector": ".small-4 .res-text", "properties": 0}, {"selector": ".small-5 .res-text", "properties": 0}, {"selector": ".small-6 .res-text", "properties": 0}, {"selector": ".small-7 .res-text", "properties": 0}, {"selector": ".small-8 .res-text", "properties": 0}, {"selector": ".small-9 .res-text", "properties": 0}, {"selector": ".small-10 .res-text", "properties": 0}, {"selector": ".small-11 .res-text", "properties": 0}, {"selector": "@media screen and (min-width:550px)", "properties": 0}, {"selector": ".medium-1 .res-text,.medium-2 .res-text,.medium-3 .res-text,.medium-4 .res-text,.medium-5 .res-text,.medium-6 .res-text,.medium-7 .res-text", "properties": 0}, {"selector": ".medium-8 .res-text", "properties": 0}, {"selector": ".medium-9 .res-text", "properties": 0}, {"selector": ".medium-10 .res-text", "properties": 0}, {"selector": ".medium-11 .res-text", "properties": 0}, {"selector": ".res-text", "properties": 0}, {"selector": "@media screen and (min-width:850px)", "properties": 0}, {"selector": ".res-text", "properties": 0}, {"selector": ".large-1 .res-text,.large-2 .res-text,.large-3 .res-text,.widget-area .res-text", "properties": 0}, {"selector": ".large-1 .res-text h6,.large-2 .res-text h6,.large-3 .res-text h6", "properties": 0}, {"selector": ".large-1 .res-text p.lead,.large-2 .res-text p.lead,.large-3 .res-text p.lead", "properties": 0}, {"selector": ".large-4 .res-text", "properties": 0}, {"selector": ".large-5 .res-text", "properties": 0}, {"selector": ".large-6 .res-text", "properties": 0}, {"selector": ".large-7 .res-text", "properties": 0}, {"selector": ".large-8 .res-text", "properties": 0}, {"selector": ".large-9 .res-text", "properties": 0}, {"selector": ".large-10 .res-text", "properties": 0}, {"selector": ".large-11 .res-text", "properties": 0}, {"selector": "@media(prefers-reduced-motion:no-preference)", "properties": 0}, {"selector": ".slider [data-animate],[data-animate]", "properties": 0}, {"selector": ".slider [data-animate]:not([data-animate-transform]),[data-animate]:not([data-animate-transform])", "properties": 0}, {"selector": ".slider [data-animate]:not([data-animate-transition]),[data-animate]:not([data-animate-transition])", "properties": 0}, {"selector": ".slider [data-animate=bounceInDown],.slider [data-animate=bounceInLeft],.slider [data-animate=bounceInRight],.slider [data-animate=bounceInUp],[data-animate=bounceInDown],[data-animate=bounceInLeft],[data-animate=bounceInRight],[data-animate=bounceInUp]", "properties": 0}, {"selector": ".slider [data-animate=bounceInLeft],[data-animate=bounceInLeft]", "properties": 0}, {"selector": ".slider [data-animate=blurIn],[data-animate=blurIn]", "properties": 0}, {"selector": ".slider [data-animate=fadeInLeft],[data-animate=fadeInLeft]", "properties": 0}, {"selector": ".slider [data-animate=fadeInRight],[data-animate=fadeInRight]", "properties": 0}, {"selector": ".slider [data-animate=bounceInUp],.slider [data-animate=fadeInUp],[data-animate=bounceInUp],[data-animate=fadeInUp]", "properties": 0}, {"selector": ".slider [data-animate=bounceInRight],[data-animate=bounceInRight]", "properties": 0}, {"selector": ".slider [data-animate=bounceIn],[data-animate=bounceIn]", "properties": 0}, {"selector": ".slider [data-animate=bounceInDown],.slider [data-animate=fadeInDown],[data-animate=bounceInDown],[data-animate=fadeInDown]", "properties": 0}, {"selector": ".slider [data-animate=flipInY],[data-animate=flipInY]", "properties": 0}, {"selector": ".slider [data-animate=flipInX],[data-animate=flipInX]", "properties": 0}, {"selector": ".row-slider.slider [data-animated=true],.slider .is-selected [data-animated=true],[data-animated=true]", "properties": 0}, {"selector": ".flickity-slider>:not(.is-selected) [data-animated=true]", "properties": 0}, {"selector": ".slider [data-animate=none],[data-animate=none]", "properties": 0}, {"selector": ".slider [data-animate=blurIn][data-animated=true],[data-animated=true][data-animate=blurIn]", "properties": 0}, {"selector": "[data-animated=false]", "properties": 0}, {"selector": ".has-shadow [data-animate],[data-animate]:hover", "properties": 0}, {"selector": "[data-animate-delay=\"100\"]", "properties": 0}, {"selector": ".nav-anim>li", "properties": 0}, {"selector": ".active .nav-anim>li,.mfp-ready .nav-anim>li,.nav-anim.active>li", "properties": 0}, {"selector": ".col+.col [data-animate],.nav-anim>li,[data-animate-delay=\"200\"],[data-animate]+[data-animate]", "properties": 0}, {"selector": ".nav-anim>li+li,[data-animate-delay=\"300\"]", "properties": 0}, {"selector": ".col+.col+.col [data-animate],.nav-anim>li+li+li,[data-animate-delay=\"400\"],[data-animate]+[data-animate]+[data-animate]", "properties": 0}, {"selector": ".nav-anim>li+li+li+li,[data-animate-delay=\"500\"]", "properties": 0}, {"selector": ".col+.col+.col+.col [data-animate],.nav-anim>li+li+li+li+li,[data-animate-delay=\"600\"],[data-animate]+[data-animate]+[data-animate]+[data-animate]", "properties": 0}, {"selector": ".nav-anim>li+li+li+li+li+li,[data-animate-delay=\"700\"]", "properties": 0}, {"selector": ".col+.col+.col+.col+.col [data-animate],.nav-anim>li+li+li+li+li+li+li,[data-animate-delay=\"800\"],[data-animate]+[data-animate]+[data-animate]+[data-animate]+[data-animate]", "properties": 0}, {"selector": ".col+.col+.col+.col+.col+.col [data-animate],.nav-anim>li+li+li+li+li+li+li+li,[data-animate-delay=\"900\"]", "properties": 0}, {"selector": ".col+.col+.col+.col+.col+.col+.col [data-animate],.nav-anim>li+li+li+li+li+li+li+li+li,[data-animate-delay=\"1000\"]", "properties": 0}, {"selector": ".slider-type-fade .flickity-slider>:not(.is-selected) [data-animate]", "properties": 0}, {"selector": "@keyframes stuckMoveDown", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": "@keyframes stuckMoveUp", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": "@keyframes stuckFadeIn", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": ".ux-stagger", "properties": 0}, {"selector": ".ux-stagger>*", "properties": 0}, {"selector": ".ux-stagger>:first-child", "properties": 0}, {"selector": ".ux-stagger>:nth-child(2)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(3)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(4)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(5)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(6)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(7)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(8)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(9)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(10)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(11)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(12)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(13)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(14)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(15)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(16)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(17)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(18)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(19)", "properties": 0}, {"selector": ".ux-stagger>:nth-child(20)", "properties": 0}, {"selector": "@keyframes ux-animate", "properties": 0}, {"selector": "0%", "properties": 0}, {"selector": "to", "properties": 0}, {"selector": ".is-border", "properties": 0}, {"selector": ".is-dashed", "properties": 0}, {"selector": ".is-dotted", "properties": 0}, {"selector": ".has-shadow .box:not(.box-overlay):not(.box-shade),.has-shadow.box:not(.box-overlay):not(.box-shade)", "properties": 0}, {"selector": ".dark .has-shadow .box:not(.box-overlay):not(.box-shade),.dark .has-shadow.box:not(.box-overlay):not(.box-shade)", "properties": 0}, {"selector": ".box-shadow-1,.box-shadow-1-hover,.box-shadow-2,.box-shadow-2-hover,.box-shadow-3,.box-shadow-3-hover,.box-shadow-4,.box-shadow-4-hover,.box-shadow-5,.box-shadow-5-hover,.row-box-shadow-1 .col-inner,.row-box-shadow-1-hover .col-inner,.row-box-shadow-2 .col-inner,.row-box-shadow-2-hover .col-inner,.row-box-shadow-3 .col-inner,.row-box-shadow-3-hover .col-inner,.row-box-shadow-4 .col-inner,.row-box-shadow-4-hover .col-inner,.row-box-shadow-5 .col-inner,.row-box-shadow-5-hover .col-inner", "properties": 0}, {"selector": ".box-shadow-1,.box-shadow-1-hover:hover,.row-box-shadow-1 .col-inner,.row-box-shadow-1-hover .col-inner:hover", "properties": 0}, {"selector": ".box-shadow,.box-shadow-2,.box-shadow-2-hover:hover,.row-box-shadow-2 .col-inner,.row-box-shadow-2-hover .col-inner:hover", "properties": 0}, {"selector": ".box-shadow-3,.box-shadow-3-hover:hover,.row-box-shadow-3 .col-inner,.row-box-shadow-3-hover .col-inner:hover", "properties": 0}, {"selector": ".box-shadow-4,.box-shadow-4-hover:hover,.row-box-shadow-4 .col-inner,.row-box-shadow-4-hover .col-inner:hover", "properties": 0}, {"selector": ".box-shadow-5,.box-shadow-5-hover:hover,.row-box-shadow-5 .col-inner,.row-box-shadow-5-hover .col-inner:hover", "properties": 0}, {"selector": ".box-shadow-4-hover:hover,.box-shadow-5-hover:hover,.row-box-shadow-4-hover .col-inner:hover,.row-box-shadow-5-hover .col-inner:hover", "properties": 0}, {"selector": ".text-shadow,.text-shadow-1", "properties": 0}, {"selector": ".text-shadow-2", "properties": 0}, {"selector": ".text-shadow-3", "properties": 0}, {"selector": ".text-shadow-4", "properties": 0}, {"selector": ".text-shadow-5", "properties": 0}, {"selector": "a.hotspot", "properties": 0}, {"selector": "a.hotspot i", "properties": 0}, {"selector": "a.hotspot:hover", "properties": 0}, {"selector": ".has-hover .bg,.has-hover [class*=image-] img", "properties": 0}, {"selector": ".has-hover .has-image-zoom img", "properties": 0}, {"selector": ".bg-color .bg,.bg-grayscale:hover .bg,.has-hover .image-color img,.has-hover:hover .image-grayscale img", "properties": 0}, {"selector": ".bg-color:hover .bg,.has-hover:hover .image-color img", "properties": 0}, {"selector": ".bg-zoom:hover .bg,.has-hover:hover .image-zoom img", "properties": 0}, {"selector": ".bg-zoom-long .bg,.has-hover .image-zoom-long img", "properties": 0}, {"selector": ".bg-zoom-long:hover .bg,.has-hover:hover .image-zoom-long img", "properties": 0}, {"selector": ".bg-zoom-fade:hover .bg,.has-hover:hover .image-zoom-fade img", "properties": 0}, {"selector": ".bg-glow:hover .bg,.has-hover:hover .image-glow img", "properties": 0}, {"selector": ".bg-fade-out:hover .bg,.has-hover:hover .image-fade-out img", "properties": 0}, {"selector": ".has-hover:hover .image-fade img,.image-fade:hover .bg", "properties": 0}, {"selector": ".bg-fade-in .bg,.image-fade-in img", "properties": 0}, {"selector": ".bg-fade-in:hover .bg,.has-hover:hover .image-fade-in img", "properties": 0}, {"selector": ".bg-blur:hover .bg,.has-hover:hover .image-blur img", "properties": 0}, {"selector": ".bg-overlay-add:not(:hover) .overlay,.bg-overlay-remove:hover .overlay,.has-hover:hover .image-overlay-remove .overlay,.has-hover:not(:hover) .image-overlay-add .overlay", "properties": 0}, {"selector": ".bg-overlay-add-50:not(:hover) .overlay,.bg-overlay-remove-50:hover .overlay,.has-hover:hover .image-overlay-remove-50 .overlay,.has-hover:not(:hover) .image-overlay-add-50 .overlay", "properties": 0}, {"selector": ".has-mask", "properties": 0}, {"selector": ".mask-circle", "properties": 0}, {"selector": ".mask-angled", "properties": 0}, {"selector": ".mask-angled-right", "properties": 0}, {"selector": ".mask-arrow", "properties": 0}, {"selector": ".mask-angled-large", "properties": 0}, {"selector": ".mask-angled-right-large", "properties": 0}, {"selector": ".mask-arrow-large", "properties": 0}, {"selector": ".mask-angled,.mask-angled-right,.mask-arrow", "properties": 0}, {"selector": ".mask-angled-large,.mask-angled-right-large,.mask-arrow-large", "properties": 0}, {"selector": "[data-parallax-container]", "properties": 0}, {"selector": "@media(prefers-reduced-motion:no-preference)", "properties": 0}, {"selector": "[data-parallax]", "properties": 0}, {"selector": "[data-parallax]:not(.parallax-active),[data-parallax]:not(.parallax-active) .bg", "properties": 0}, {"selector": ".shade", "properties": 0}, {"selector": ".shade-top", "properties": 0}, {"selector": ".box:hover .shade", "properties": 0}, {"selector": ".box-shade:not(.dark) .shade", "properties": 0}, {"selector": ".hover-slide,.hover-slide-in,.hover-zoom,.hover-zoom-in,.show-on-hover", "properties": 0}, {"selector": ".hover-slide", "properties": 0}, {"selector": ".hover-slide-in", "properties": 0}, {"selector": ".box-text-middle .box-text.hover-slide", "properties": 0}, {"selector": ".hover-fade-in", "properties": 0}, {"selector": ".has-hover:hover .hover-fade-in", "properties": 0}, {"selector": ".hover-fade-out", "properties": 0}, {"selector": ".has-hover:hover .hover-fade-out", "properties": 0}, {"selector": ".hover-invert", "properties": 0}, {"selector": ".has-hover:hover .hover-invert", "properties": 0}, {"selector": ".hover-reveal", "properties": 0}, {"selector": ".has-hover:hover .hover-reveal", "properties": 0}, {"selector": ".has-hover:hover .hover-reveal.reveal-small", "properties": 0}, {"selector": ".hover-blur", "properties": 0}, {"selector": ".has-hover:hover .hover-blur", "properties": 0}, {"selector": ".hover-zoom", "properties": 0}, {"selector": ".box-text-middle .box-text.hover-zoom", "properties": 0}, {"selector": ".hover-zoom-in", "properties": 0}, {"selector": ".box-text-middle .box-text.hover-zoom-in", "properties": 0}, {"selector": ".hover-bounce", "properties": 0}, {"selector": ".has-hover:hover .hover-bounce", "properties": 0}, {"selector": ".box-text-middle .hover-bounce", "properties": 0}, {"selector": ".box-text-middle:hover .box-text.hover-bounce", "properties": 0}, {"selector": ".has-hover:hover img.show-on-hover", "properties": 0}, {"selector": ".has-hover .box-image:focus-within .show-on-hover,.has-hover:hover .hover-slide,.has-hover:hover .hover-slide-in,.has-hover:hover .hover-zoom,.has-hover:hover .hover-zoom-in,.has-hover:hover .show-on-hover", "properties": 0}, {"selector": ".box-text-middle:hover .show-on-hover.box-text,.has-hover:hover .show-on-hover.center", "properties": 0}, {"selector": "@media(prefers-reduced-motion)", "properties": 0}, {"selector": ".box-text-middle .box-text.hover-slide,.box-text-middle:hover .show-on-hover.box-text,.has-hover:hover .hover-bounce,.has-hover:hover .hover-slide,.has-hover:hover .hover-slide-in,.has-hover:hover .hover-zoom,.has-hover:hover .hover-zoom-in,.has-hover:hover .show-on-hover,.has-hover:hover .show-on-hover.center,.hover-bounce,.hover-reveal,.hover-slide,.hover-slide-in,.hover-zoom,.hover-zoom-in", "properties": 0}, {"selector": ".slider .has-slide-effect", "properties": 0}, {"selector": ".slider .has-slide-effect .bg", "properties": 0}, {"selector": ".slide-zoom-in .bg,.slide-zoom-in-fast .bg", "properties": 0}, {"selector": ".slide-zoom-out .bg,.slide-zoom-out-fast .bg", "properties": 0}, {"selector": ".has-slide-effect.is-selected .bg", "properties": 0}, {"selector": ".slide-fade-in.is-selected .bg", "properties": 0}, {"selector": ".slide-fade-in-fast.is-selected .bg", "properties": 0}, {"selector": ".slide-zoom-in-fast.is-selected .bg,.slide-zoom-in.is-selected .bg", "properties": 0}, {"selector": ".slide-zoom-out-fast.is-selected .bg", "properties": 0}, {"selector": ".tooltipster-base", "properties": 0}, {"selector": ".tooltipster-box", "properties": 0}, {"selector": ".tooltipster-content", "properties": 0}, {"selector": ".tooltipster-ruler", "properties": 0}, {"selector": ".tooltipster-fade", "properties": 0}, {"selector": ".tooltipster-fade.tooltipster-show", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default .tooltipster-box", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-bottom .tooltipster-box", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-left .tooltipster-box", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-right .tooltipster-box", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-top .tooltipster-box", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default .tooltipster-content", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default .tooltipster-arrow", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-bottom .tooltipster-arrow", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-left .tooltipster-arrow", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-right .tooltipster-arrow", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-top .tooltipster-arrow", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default .tooltipster-arrow-background,.tooltipster-sidetip.tooltipster-default .tooltipster-arrow-border", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default .tooltipster-arrow-background", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-bottom .tooltipster-arrow-background", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-left .tooltipster-arrow-background", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-right .tooltipster-arrow-background", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-top .tooltipster-arrow-background", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default .tooltipster-arrow-border", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-bottom .tooltipster-arrow-border", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-left .tooltipster-arrow-border", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-right .tooltipster-arrow-border", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-top .tooltipster-arrow-border", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default .tooltipster-arrow-uncropped", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-bottom .tooltipster-arrow-uncropped", "properties": 0}, {"selector": ".tooltipster-sidetip.tooltipster-default.tooltipster-right .tooltipster-arrow-uncropped", "properties": 0}, {"selector": ".dark,.dark p,.dark td", "properties": 0}, {"selector": ".dark .heading-font,.dark a.lead,.dark h1,.dark h2,.dark h3,.dark h4,.dark h5,.dark h6,.dark label,.hover-dark:hover a,.hover-dark:hover h1,.hover-dark:hover h2,.hover-dark:hover h3,.hover-dark:hover h4,.hover-dark:hover h5,.hover-dark:hover h6,.hover-dark:hover p", "properties": 0}, {"selector": ".dark .product-footer .woocommerce-tabs,.dark .product-section,.dark .stars a+a", "properties": 0}, {"selector": ".dark .nav-vertical li li.menu-item-has-children>a,.dark .nav-vertical-fly-out>li>a,.dark .nav-vertical>li>ul li a,.dark .nav>li.html,.dark .nav>li>a,.nav-dark .is-outline,.nav-dark .nav>li>a,.nav-dark .nav>li>button,.nav-dropdown.dark .nav-column>li>a,.nav-dropdown.dark>li>a", "properties": 0}, {"selector": ".dark .nav-vertical-fly-out>li>a:hover,.dark .nav>li.active>a,.dark .nav>li>a.active,.dark .nav>li>a:hover,.nav-dark .nav>li.active>a,.nav-dark .nav>li.current>a,.nav-dark .nav>li>a.active,.nav-dark .nav>li>a:hover,.nav-dark a.plain:hover,.nav-dropdown.dark .nav-column>li>a:hover,.nav-dropdown.dark>li>a:hover", "properties": 0}, {"selector": ".dark.nav-dropdown.nav-dropdown-bold>li.nav-dropdown-col,.dark.nav-dropdown.nav-dropdown-simple>li.nav-dropdown-col,.nav-dark .header-divider:after,.nav-dark .nav-divided>li+li>a:after,.nav-dark .nav>li.header-divider,.nav-dropdown.dark .nav-column>li>a,.nav-dropdown.dark>li>a", "properties": 0}, {"selector": ".dark .nav-vertical li li.menu-item-has-children>a,.nav-dark .header-button,.nav-dark .nav-vertical li li.menu-item-has-children>a,.nav-dark .nav>li.html", "properties": 0}, {"selector": ".nav-box a:hover span.amount,.nav-dark span.amount", "properties": 0}, {"selector": ":root", "properties": 0}, {"selector": "html", "properties": 0}, {"selector": "@supports(overflow:clip)", "properties": 0}, {"selector": "body", "properties": 0}, {"selector": "#main,#wrapper", "properties": 0}, {"selector": "#main.dark", "properties": 0}, {"selector": ".page-wrapper", "properties": 0}, {"selector": ".page-wrapper-full", "properties": 0}, {"selector": ".is-sticky-column__inner", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".is-sticky-column", "properties": 0}, {"selector": ".is-sticky-column[data-sticky-mode]", "properties": 0}, {"selector": ".is-sticky-column:not([data-sticky-mode])", "properties": 0}, {"selector": ".ux-body-overlay", "properties": 0}, {"selector": ".ux-body-overlay--click-active,.ux-body-overlay--hover-active", "properties": 0}, {"selector": ".header,.header-wrapper", "properties": 0}, {"selector": ".header-bg-color", "properties": 0}, {"selector": ".header-bottom,.header-top", "properties": 0}, {"selector": ".header.has-transparent,.header.show-on-scroll,.header.transparent", "properties": 0}, {"selector": ".header.show-on-scroll:not(.stuck),.header.transparent:not(.stuck)", "properties": 0}, {"selector": ".header.show-on-scroll .header-wrapper", "properties": 0}, {"selector": ".header.show-on-scroll .header-wrapper.stuck", "properties": 0}, {"selector": ".header.transparent .header-bg-color,.header.transparent .header-bg-image,.header.transparent .header-bottom,.header.transparent .header-wrapper", "properties": 0}, {"selector": ".header-bg-color,.header-bg-image", "properties": 0}, {"selector": ".header-top", "properties": 0}, {"selector": ".header-main", "properties": 0}, {"selector": ".header-bottom", "properties": 0}, {"selector": ".top-divider", "properties": 0}, {"selector": ".nav-dark .top-divider", "properties": 0}, {"selector": ".stuck", "properties": 0}, {"selector": ".sticky-jump .stuck:not(.ux-no-animation)", "properties": 0}, {"selector": ".sticky-fade .stuck:not(.ux-no-animation)", "properties": 0}, {"selector": ".sticky-shrink .stuck .header-main", "properties": 0}, {"selector": ".stuck .header-inner,.stuck .logo,.stuck .logo img", "properties": 0}, {"selector": ".header-wrapper:not(.stuck) .logo img", "properties": 0}, {"selector": ".header.show-on-scroll .hide-for-sticky,.stuck .hide-for-sticky", "properties": 0}, {"selector": ".header-shadow .header-wrapper,.header-wrapper.stuck,.layout-shadow #wrapper", "properties": 0}, {"selector": ".nav>li.header-divider", "properties": 0}, {"selector": ".nav-dark .nav>li.header-divider", "properties": 0}, {"selector": ".header-block", "properties": 0}, {"selector": ".header-full-width .container", "properties": 0}, {"selector": "@media(prefers-reduced-motion)", "properties": 0}, {"selector": ".sticky-jump .stuck", "properties": 0}, {"selector": "@media(max-width:549px)", "properties": 0}, {"selector": ".nav-mobile>li>a:not(.button)", "properties": 0}, {"selector": ".post", "properties": 0}, {"selector": ".masonery .post", "properties": 0}, {"selector": ".entry-header-text", "properties": 0}, {"selector": ".entry-header-text.text-center", "properties": 0}, {"selector": ".entry-header-text-top", "properties": 0}, {"selector": ".entry-header-text-bottom", "properties": 0}, {"selector": ".entry-content", "properties": 0}, {"selector": ".masonery .article-inner .box", "properties": 0}, {"selector": ".article-inner", "properties": 0}, {"selector": ".blog-featured-title+#main .post-sidebar", "properties": 0}, {"selector": ".from_the_blog_comments", "properties": 0}, {"selector": ".article-inner.has-shadow", "properties": 0}, {"selector": ".article-inner.has-shadow .author-box,.article-inner.has-shadow .entry-content,.article-inner.has-shadow .entry-header-text,.article-inner.has-shadow footer.entry-meta", "properties": 0}, {"selector": ".article-inner.has-shadow .entry-header-text-top", "properties": 0}, {"selector": ".entry-author", "properties": 0}, {"selector": ".banner h2.entry-title", "properties": 0}, {"selector": ".badge.post-date", "properties": 0}, {"selector": ".entry-image img", "properties": 0}, {"selector": ".entry-image-float", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".entry-image-float", "properties": 0}, {"selector": ".entry-image-float+.entry-content", "properties": 0}, {"selector": ".post .entry-summary p:last-of-type", "properties": 0}, {"selector": "footer.entry-meta", "properties": 0}, {"selector": "li.recentcomments", "properties": 0}, {"selector": "li.recentcomments a", "properties": 0}, {"selector": ".box-blog-post .is-divider", "properties": 0}, {"selector": ".bypostaut<PERSON>", "properties": 0}, {"selector": ".more-link", "properties": 0}, {"selector": ".updated:not(.published)", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".alignleft", "properties": 0}, {"selector": ".alignright", "properties": 0}, {"selector": ".aligncenter", "properties": 0}, {"selector": ".wp-caption", "properties": 0}, {"selector": ".wp-caption .wp-caption-text", "properties": 0}, {"selector": ".wp-caption-text", "properties": 0}, {"selector": "img.size-full.align<PERSON>e", "properties": 0}, {"selector": ".row .entry-content .gallery", "properties": 0}, {"selector": ".gallery-caption", "properties": 0}, {"selector": ".comment-content .wp-smiley,.entry-content .wp-smiley,.page-content .wp-smiley", "properties": 0}, {"selector": ".widget", "properties": 0}, {"selector": ".widget ul", "properties": 0}, {"selector": ".widget li", "properties": 0}, {"selector": ".widgets-boxed .widget,.widgets-framed .widget", "properties": 0}, {"selector": ".widgets-boxed .widget h3", "properties": 0}, {"selector": ".widgets-boxed h3+.is-divider", "properties": 0}, {"selector": ".widget>ul>li,ul.menu>li", "properties": 0}, {"selector": ".widget>ul>li li,ul.menu>li li", "properties": 0}, {"selector": ".widget>ul>li>a,.widget>ul>li>span:first-child,ul.menu>li>a,ul.menu>li>span:first-child", "properties": 0}, {"selector": ".widget>ul>li:before,ul.menu>li:before", "properties": 0}, {"selector": ".widget>ul>li li>a,ul.menu>li li>a", "properties": 0}, {"selector": ".widget>ul>li+li,ul.menu>li+li", "properties": 0}, {"selector": ".dark .widget>ul>li+li,.dark ul.menu>li+li", "properties": 0}, {"selector": ".widget>ul>li ul,ul.menu>li ul", "properties": 0}, {"selector": ".widget>ul>li ul li,ul.menu>li ul li", "properties": 0}, {"selector": ".dark .widget>ul>li ul,.dark ul.menu>li ul", "properties": 0}, {"selector": ".dark .widget>ul>li.active>a,.dark .widget>ul>li.current-cat>a,.dark .widget>ul>li>a:hover,.dark ul.menu>li.active>a,.dark ul.menu>li.current-cat>a,.dark ul.menu>li>a:hover", "properties": 0}, {"selector": ".widget>ul>li.has-child,ul.menu>li.has-child", "properties": 0}, {"selector": ".widget>ul>li .count", "properties": 0}, {"selector": ".widget .current-cat-parent>ul,.widget .current>ul", "properties": 0}, {"selector": ".widget .current-cat>a", "properties": 0}, {"selector": ".recent-blog-posts", "properties": 0}, {"selector": ".recent-blog-posts a", "properties": 0}, {"selector": ".widget .instagram-pics:after", "properties": 0}, {"selector": ".widget .instagram-pics>li", "properties": 0}, {"selector": ".widget .instagram-pics>li a", "properties": 0}, {"selector": ".widget .instagram-pics>li img", "properties": 0}, {"selector": ".widget_layered_nav li", "properties": 0}, {"selector": ".widget_display-latest-tweets li", "properties": 0}, {"selector": ".widget_display-latest-tweets a", "properties": 0}, {"selector": ".widget_display-latest-tweets span", "properties": 0}, {"selector": ".wpcf7", "properties": 0}, {"selector": ".wpcf7 .ajax-loader", "properties": 0}, {"selector": ".dark .wpcf7", "properties": 0}, {"selector": ".wpcf7 p", "properties": 0}, {"selector": ".wpcf7 br", "properties": 0}, {"selector": ".wpcf7 label", "properties": 0}, {"selector": ".wpcf7 span.wpcf7-list-item", "properties": 0}, {"selector": ".wpcf7 .wpcf7-form-control-wrap", "properties": 0}, {"selector": ".wpcf7 .ajax-loader", "properties": 0}, {"selector": ".wpcf7 .wpcf7-not-valid-tip", "properties": 0}, {"selector": ".wpcf7 .wpcf7-not-valid-tip:after", "properties": 0}, {"selector": ".wpcf7 .wpcf7-validation-errors", "properties": 0}, {"selector": ".wpcf7 .flex-col .wpcf7-not-valid-tip", "properties": 0}, {"selector": ".wpcf7 .wpcf7-response-output", "properties": 0}, {"selector": ".wpcf7-form .processing *", "properties": 0}, {"selector": ".password-required form.post-password-form", "properties": 0}, {"selector": ".portfolio-inner img", "properties": 0}, {"selector": ".portfolio-bottom", "properties": 0}, {"selector": ".row+.portfolio-related .portfolio-element-wrapper", "properties": 0}, {"selector": ".accordion-inner", "properties": 0}, {"selector": ".accordion-title", "properties": 0}, {"selector": ".dark .accordion-title", "properties": 0}, {"selector": ".accordion-title.active", "properties": 0}, {"selector": ".dark .accordion-title.active", "properties": 0}, {"selector": ".accordion .toggle", "properties": 0}, {"selector": ".accordion .active .toggle", "properties": 0}, {"selector": ".breadcrumbs", "properties": 0}, {"selector": ".product-info .breadcrumbs", "properties": 0}, {"selector": ".breadcrumbs .divider,.breadcrumbs .separator", "properties": 0}, {"selector": ".dark .breadcrumbs", "properties": 0}, {"selector": ".breadcrumbs a", "properties": 0}, {"selector": ".breadcrumbs a:first-of-type", "properties": 0}, {"selector": ".breadcrumbs a.current,.breadcrumbs a:hover", "properties": 0}, {"selector": ".dark .breadcrumbs a", "properties": 0}, {"selector": ".checkout-breadcrumbs", "properties": 0}, {"selector": ".checkout-breadcrumbs a", "properties": 0}, {"selector": ".checkout-breadcrumbs .divider", "properties": 0}, {"selector": ".breadcrumb-step", "properties": 0}, {"selector": ".current .breadcrumb-step", "properties": 0}, {"selector": "@media(min-width:850px)", "properties": 0}, {"selector": ".comment-form", "properties": 0}, {"selector": ".comment-form>div,.comment-form>p", "properties": 0}, {"selector": ".comment-form p.comment-form-author,.comment-form p.comment-form-email,.comment-form p.comment-form-url", "properties": 0}, {"selector": ".comment-inner:target", "properties": 0}, {"selector": ".comment-list li,.commentlist li", "properties": 0}, {"selector": ".comment-list li .meta,.commentlist li .meta", "properties": 0}, {"selector": ".comment-list li .avatar,.commentlist li .avatar", "properties": 0}, {"selector": ".comment-list li .description,.commentlist li .description", "properties": 0}, {"selector": ".comment-list li .comment-text,.commentlist li .comment-text", "properties": 0}, {"selector": ".comment-list>li:not(:first-child),.commentlist>li:not(:first-child)", "properties": 0}, {"selector": "#comments .comment-respond", "properties": 0}, {"selector": "#comments .comment-respond .comment-reply-title", "properties": 0}, {"selector": "#comments .comment-respond small", "properties": 0}, {"selector": ".footer-wrapper", "properties": 0}, {"selector": ".footer", "properties": 0}, {"selector": ".footer-1", "properties": 0}, {"selector": ".footer-2", "properties": 0}, {"selector": ".footer-1,.footer-2", "properties": 0}, {"selector": ".footer-secondary", "properties": 0}, {"selector": ".absolute-footer,html", "properties": 0}, {"selector": ".footer ul", "properties": 0}, {"selector": ".absolute-footer", "properties": 0}, {"selector": ".absolute-footer.dark", "properties": 0}, {"selector": ".absolute-footer.fixed", "properties": 0}, {"selector": ".absolute-footer ul", "properties": 0}, {"selector": ".absolute-footer.dark ul", "properties": 0}, {"selector": ".absolute-footer.text-center ul", "properties": 0}, {"selector": ".reveal-footer", "properties": 0}, {"selector": ".reveal-footer+.footer-wrapper", "properties": 0}, {"selector": ".back-to-top", "properties": 0}, {"selector": ".back-to-top.left", "properties": 0}, {"selector": ".back-to-top.active", "properties": 0}, {"selector": ".instagram-image-container", "properties": 0}, {"selector": ".instagram-image-container a>img", "properties": 0}, {"selector": ".instagram-image-type--placeholder img", "properties": 0}, {"selector": ".logo", "properties": 0}, {"selector": ".logo-tagline", "properties": 0}, {"selector": ".logo a", "properties": 0}, {"selector": ".logo img", "properties": 0}, {"selector": ".header-logo-dark,.header-logo-sticky,.nav-dark .header-logo,.sticky .dark .header-logo-dark,.sticky .has-sticky-logo .header-logo", "properties": 0}, {"selector": ".nav-dark .header-logo-dark,.stuck .header-logo-sticky", "properties": 0}, {"selector": ".stuck .header-logo-sticky+img,.stuck .header-logo-sticky+img+img", "properties": 0}, {"selector": ".nav-dark .logo a,.nav-dark .logo-tagline", "properties": 0}, {"selector": ".logo-left .logo", "properties": 0}, {"selector": ".logo-center .flex-left", "properties": 0}, {"selector": ".logo-center .logo", "properties": 0}, {"selector": ".logo-center .logo img", "properties": 0}, {"selector": ".logo-center .flex-right", "properties": 0}, {"selector": "@media screen and (max-width:849px)", "properties": 0}, {"selector": ".header-inner .nav", "properties": 0}, {"selector": ".medium-logo-left .logo", "properties": 0}, {"selector": ".medium-logo-left .flex-left", "properties": 0}, {"selector": ".medium-logo-left .flex-right", "properties": 0}, {"selector": ".medium-logo-center .flex-left", "properties": 0}, {"selector": ".medium-logo-center .logo", "properties": 0}, {"selector": ".medium-logo-center .logo img", "properties": 0}, {"selector": ".medium-logo-center .flex-right", "properties": 0}, {"selector": ".ux-lottie", "properties": 0}, {"selector": ".map-height", "properties": 0}, {"selector": ".map-inner", "properties": 0}, {"selector": ".google-map .gm-style button", "properties": 0}, {"selector": ".google-map .gm-style .gm-style-mtc li", "properties": 0}, {"selector": ".message-box", "properties": 0}, {"selector": ".message-box.dark", "properties": 0}, {"selector": ".message-box .col,.message-box .col-inner,.message-box .row", "properties": 0}, {"selector": ".next-prev-thumbs li", "properties": 0}, {"selector": ".next-prev-thumbs li .button", "properties": 0}, {"selector": ".next-prev-thumbs .nav-dropdown", "properties": 0}, {"selector": ".page-title", "properties": 0}, {"selector": ".page-title .widget", "properties": 0}, {"selector": ".page-title-bg", "properties": 0}, {"selector": ".title-bg", "properties": 0}, {"selector": ".title-overlay", "properties": 0}, {"selector": ".page-title-inner", "properties": 0}, {"selector": ".page-title-inner button,.page-title-inner form,.page-title-inner p,.page-title-inner select,.page-title-inner ul", "properties": 0}, {"selector": ".normal-title", "properties": 0}, {"selector": ".normal-title .page-title-inner", "properties": 0}, {"selector": ".featured-title", "properties": 0}, {"selector": ".featured-title .page-title-inner", "properties": 0}, {"selector": ".featured-title .page-title-bg", "properties": 0}, {"selector": ".featured-title .overlay", "properties": 0}, {"selector": ".payment-icons .payment-icon", "properties": 0}, {"selector": ".payment-icons .payment-icon svg", "properties": 0}, {"selector": ".payment-icons .payment-icon:hover", "properties": 0}, {"selector": ".dark .payment-icons .payment-icon", "properties": 0}, {"selector": ".dark .payment-icons .payment-icon svg", "properties": 0}, {"selector": ".pricing-table", "properties": 0}, {"selector": ".dark .pricing-table", "properties": 0}, {"selector": ".pricing-table .title", "properties": 0}, {"selector": ".dark .pricing-table .title", "properties": 0}, {"selector": ".pricing-table .price", "properties": 0}, {"selector": ".pricing-table .description", "properties": 0}, {"selector": ".pricing-table .items", "properties": 0}, {"selector": ".pricing-table .is-disabled", "properties": 0}, {"selector": ".pricing-table .items .button:last-child", "properties": 0}, {"selector": ".pricing-table .bullet-more-info", "properties": 0}, {"selector": ".pricing-table .items>div", "properties": 0}, {"selector": ".dark .pricing-table .items>div", "properties": 0}, {"selector": ".dark .pricing-table", "properties": 0}, {"selector": ".pricing-table .title", "properties": 0}, {"selector": ".featured-table", "properties": 0}, {"selector": ".featured-table .title", "properties": 0}, {"selector": ".scroll-to", "properties": 0}, {"selector": ".scroll-to-bullets", "properties": 0}, {"selector": ".scroll-to-bullets a", "properties": 0}, {"selector": ".scroll-to-bullets a.active,.scroll-to-bullets a:hover", "properties": 0}, {"selector": ".scroll-to-bullets a.active", "properties": 0}, {"selector": ".sidebar-menu .search-form", "properties": 0}, {"selector": ".searchform-wrapper form", "properties": 0}, {"selector": ".mobile-nav>.search-form,.sidebar-menu .search-form", "properties": 0}, {"selector": ".form-flat .search-form-categories", "properties": 0}, {"selector": ".searchform-wrapper:not(.form-flat) .submit-button,.widget_search .submit-button", "properties": 0}, {"selector": ".searchform", "properties": 0}, {"selector": ".searchform .button.icon", "properties": 0}, {"selector": ".searchform .button.icon i", "properties": 0}, {"selector": ".searchform-wrapper", "properties": 0}, {"selector": ".searchform-wrapper.form-flat .submit-button.loading .icon-search", "properties": 0}, {"selector": ".searchform-wrapper.form-flat .submit-button.loading:after", "properties": 0}, {"selector": ".searchform-wrapper.form-flat .flex-col:last-of-type", "properties": 0}, {"selector": "@media(max-width:849px)", "properties": 0}, {"selector": ".searchform-wrapper", "properties": 0}, {"selector": ".searchform-wrapper .autocomplete-suggestions", "properties": 0}, {"selector": ".header .search-form .autocomplete-suggestions,.header-block .autocomplete-suggestions", "properties": 0}, {"selector": ".col .live-search-results,.header .search-form .live-search-results,.header-block .live-search-results", "properties": 0}, {"selector": ".header li .html .live-search-results", "properties": 0}, {"selector": ".autocomplete-suggestion", "properties": 0}, {"selector": ".autocomplete-suggestion .search-name", "properties": 0}, {"selector": ".autocomplete-suggestion img", "properties": 0}, {"selector": ".autocomplete-suggestion img+.search-name", "properties": 0}, {"selector": ".autocomplete-suggestion .search-price", "properties": 0}, {"selector": ".autocomplete-suggestion:last-child", "properties": 0}, {"selector": ".autocomplete-selected", "properties": 0}, {"selector": "#search-lightbox", "properties": 0}, {"selector": ".section-title-container", "properties": 0}, {"selector": ".banner+.section-title-container,.row-collapse+.section-title-container,.slider-wrapper+.section-title-container", "properties": 0}, {"selector": ".section-title", "properties": 0}, {"selector": ".section-title i", "properties": 0}, {"selector": ".section-title i.icon-angle-right", "properties": 0}, {"selector": ".section-title span", "properties": 0}, {"selector": ".section-title small", "properties": 0}, {"selector": ".section-title b", "properties": 0}, {"selector": ".section-title a", "properties": 0}, {"selector": ".section-title-normal", "properties": 0}, {"selector": ".section-title-normal span", "properties": 0}, {"selector": ".dark .section-title-normal,.dark .section-title-normal span", "properties": 0}, {"selector": ".section-title-normal b", "properties": 0}, {"selector": ".section-title-center span", "properties": 0}, {"selector": ".section-title-bold-center span,.section-title-center span", "properties": 0}, {"selector": ".section-title-bold-center small,.section-title-center small", "properties": 0}, {"selector": ".section-title-bold span,.section-title-bold-center span", "properties": 0}, {"selector": ".section-title-bold b:first-of-type", "properties": 0}, {"selector": ".flatsome-cookies", "properties": 0}, {"selector": ".flatsome-cookies__inner", "properties": 0}, {"selector": ".flatsome-cookies__text", "properties": 0}, {"selector": ".flatsome-cookies__buttons", "properties": 0}, {"selector": ".flatsome-cookies__buttons>a", "properties": 0}, {"selector": ".flatsome-cookies__buttons>a:last-child", "properties": 0}, {"selector": ".flatsome-cookies--inactive", "properties": 0}, {"selector": ".flatsome-cookies--active", "properties": 0}, {"selector": "@media(max-width:849px)", "properties": 0}, {"selector": ".flatsome-cookies__inner", "properties": 0}, {"selector": ".flatsome-cookies__buttons", "properties": 0}, {"selector": ".flatsome-cookies__text", "properties": 0}, {"selector": ".icon-lock:before", "properties": 0}, {"selector": ".icon-user-o:before", "properties": 0}, {"selector": ".icon-chat:before,.icon-line:before", "properties": 0}, {"selector": ".icon-user:before", "properties": 0}, {"selector": ".icon-shopping-cart:before", "properties": 0}, {"selector": ".icon-tumblr:before", "properties": 0}, {"selector": ".icon-gift:before", "properties": 0}, {"selector": ".icon-phone:before", "properties": 0}, {"selector": ".icon-play:before", "properties": 0}, {"selector": ".icon-menu:before", "properties": 0}, {"selector": ".icon-equalizer:before", "properties": 0}, {"selector": ".icon-shopping-basket:before", "properties": 0}, {"selector": ".icon-shopping-bag:before", "properties": 0}, {"selector": ".icon-google-plus:before", "properties": 0}, {"selector": ".icon-heart-o:before", "properties": 0}, {"selector": ".icon-heart:before", "properties": 0}, {"selector": ".icon-500px:before", "properties": 0}, {"selector": ".icon-vk:before", "properties": 0}, {"selector": ".icon-angle-left:before", "properties": 0}, {"selector": ".icon-angle-right:before", "properties": 0}, {"selector": ".icon-angle-up:before", "properties": 0}, {"selector": ".icon-angle-down:before", "properties": 0}, {"selector": ".icon-x:before", "properties": 0}, {"selector": ".icon-twitter:before", "properties": 0}, {"selector": ".icon-envelop:before", "properties": 0}, {"selector": ".icon-tag:before", "properties": 0}, {"selector": ".icon-star:before", "properties": 0}, {"selector": ".icon-star-o:before", "properties": 0}, {"selector": ".icon-facebook:before", "properties": 0}, {"selector": ".icon-feed:before", "properties": 0}, {"selector": ".icon-checkmark:before", "properties": 0}, {"selector": ".icon-plus:before", "properties": 0}, {"selector": ".icon-cross:before", "properties": 0}, {"selector": ".icon-instagram:before", "properties": 0}, {"selector": ".icon-tiktok:before", "properties": 0}, {"selector": ".icon-pinterest:before", "properties": 0}, {"selector": ".icon-search:before", "properties": 0}, {"selector": ".icon-skype:before", "properties": 0}, {"selector": ".icon-dribbble:before", "properties": 0}, {"selector": ".icon-certificate:before", "properties": 0}, {"selector": ".icon-expand:before", "properties": 0}, {"selector": ".icon-linkedin:before", "properties": 0}, {"selector": ".icon-map-pin-fill:before", "properties": 0}, {"selector": ".icon-pen-alt-fill:before", "properties": 0}, {"selector": ".icon-youtube:before", "properties": 0}, {"selector": ".icon-flickr:before", "properties": 0}, {"selector": ".icon-clock:before", "properties": 0}, {"selector": ".icon-snapchat:before", "properties": 0}, {"selector": ".icon-whatsapp:before", "properties": 0}, {"selector": ".icon-telegram:before", "properties": 0}, {"selector": ".icon-twitch:before", "properties": 0}, {"selector": ".icon-discord:before", "properties": 0}, {"selector": ".icon-threads:before", "properties": 0}, {"selector": ".ux-shop-ajax-filters .widget_price_filter .price_slider_amount .button", "properties": 0}, {"selector": ".ux-shape-divider", "properties": 0}, {"selector": ".ux-shape-divider svg", "properties": 0}, {"selector": ".ux-shape-divider--top", "properties": 0}, {"selector": ".ux-shape-divider--top svg", "properties": 0}, {"selector": ".ux-shape-divider--bottom", "properties": 0}, {"selector": ".ux-shape-divider--bottom svg", "properties": 0}, {"selector": ".ux-shape-divider--flip svg", "properties": 0}, {"selector": ".ux-shape-divider--to-front", "properties": 0}, {"selector": ".ux-shape-divider .ux-shape-fill", "properties": 0}, {"selector": ".text-center .social-icons", "properties": 0}, {"selector": ".social-icons", "properties": 0}, {"selector": ".html .social-icons", "properties": 0}, {"selector": ".html .social-icons .button", "properties": 0}, {"selector": ".social-icons span", "properties": 0}, {"selector": ".social-icons i", "properties": 0}, {"selector": ".dark .social-icons,.nav-dark .social-icons", "properties": 0}, {"selector": ".dark .social-icons .button.is-outline,.nav-dark .social-icons .button.is-outline", "properties": 0}, {"selector": ".social-button,.social-icons .button.icon:hover,.social-icons .button.icon:not(.is-outline)", "properties": 0}, {"selector": ".social-button>i,.social-button>span,.social-icons .button.icon:hover>i,.social-icons .button.icon:hover>span,.social-icons .button.icon:not(.is-outline)>i,.social-icons .button.icon:not(.is-outline)>span", "properties": 0}, {"selector": ".button.facebook:hover,.button.facebook:not(.is-outline)", "properties": 0}, {"selector": ".button.instagram:hover,.button.instagram:not(.is-outline)", "properties": 0}, {"selector": ".button.whatsapp:hover,.button.whatsapp:not(.is-outline)", "properties": 0}, {"selector": ".button.x:hover,.button.x:not(.is-outline)", "properties": 0}, {"selector": ".button.twitter:hover,.button.twitter:not(.is-outline)", "properties": 0}, {"selector": ".button.email:hover,.button.email:not(.is-outline),.button.threads:hover,.button.threads:not(.is-outline),.button.tiktok:hover,.button.tiktok:not(.is-outline)", "properties": 0}, {"selector": ".button.phone:hover,.button.phone:not(.is-outline)", "properties": 0}, {"selector": ".button.pinterest:hover,.button.pinterest:not(.is-outline)", "properties": 0}, {"selector": ".button.rss:hover,.button.rss:not(.is-outline)", "properties": 0}, {"selector": ".button.tumblr:hover,.button.tumblr:not(.is-outline)", "properties": 0}, {"selector": ".button.vk:hover,.button.vk:not(.is-outline)", "properties": 0}, {"selector": ".button.google-plus:hover,.button.google-plus:not(.is-outline)", "properties": 0}, {"selector": ".button.linkedin:hover,.button.linkedin:not(.is-outline)", "properties": 0}, {"selector": ".button.youtube:hover,.button.youtube:not(.is-outline)", "properties": 0}, {"selector": ".button.flickr:hover,.button.flickr:not(.is-outline)", "properties": 0}, {"selector": ".button.snapchat:hover,.button.snapchat:not(.is-outline)", "properties": 0}, {"selector": ".button.snapchat:hover i,.button.snapchat:not(.is-outline) i", "properties": 0}, {"selector": ".button.px500:hover,.button.px500:not(.is-outline)", "properties": 0}, {"selector": ".button.telegram:hover,.button.telegram:not(.is-outline)", "properties": 0}, {"selector": ".button.twitch:hover,.button.twitch:not(.is-outline)", "properties": 0}, {"selector": ".button.discord:hover,.button.discord:not(.is-outline)", "properties": 0}, {"selector": ".woocommerce-product-rating", "properties": 0}, {"selector": ".star-rating", "properties": 0}, {"selector": ".star-rating:before,.woocommerce-page .star-rating:before", "properties": 0}, {"selector": ".star-rating span", "properties": 0}, {"selector": ".star-rating span:before", "properties": 0}, {"selector": ".star-rating--inline", "properties": 0}, {"selector": ".woocommerce-review-link", "properties": 0}, {"selector": "li.wc-layered-nav-rating", "properties": 0}, {"selector": "li.wc-layered-nav-rating .star-rating", "properties": 0}, {"selector": ".stars a", "properties": 0}, {"selector": ".stars a+a", "properties": 0}, {"selector": ".stars a.active:after,.stars a:hover:after", "properties": 0}, {"selector": ".stars a:after", "properties": 0}, {"selector": ".stars a.star-2:after", "properties": 0}, {"selector": ".stars a.star-3:after", "properties": 0}, {"selector": ".stars a.star-4:after", "properties": 0}, {"selector": ".stars a.star-5:after", "properties": 0}], "media_queries": [{"media": "screen and (min-width:850px)", "rules": 0}, {"media": "screen and (max-width:549px)", "rules": 0}, {"media": "screen and (min-width:850px)", "rules": 0}, {"media": "screen and (max-width:849px)", "rules": 0}, {"media": "screen and (min-width:850px)", "rules": 0}, {"media": "screen and (min-width:550px)", "rules": 0}, {"media": "screen and (min-width:850px)", "rules": 0}, {"media": "screen and (min-width:850px)", "rules": 0}, {"media": "(-ms-high-contrast:none)", "rules": 0}, {"media": "screen and (max-width:549px)", "rules": 0}, {"media": "screen and (max-width:849px)", "rules": 0}, {"media": "screen and (min-width:850px)", "rules": 0}, {"media": "screen and (max-width:549px)", "rules": 0}, {"media": "screen and (min-width:849px)", "rules": 0}, {"media": "screen and (max-width:549px)", "rules": 0}, {"media": "screen and (min-width:550px)", "rules": 0}, {"media": "(-ms-high-contrast:none),screen and (-ms-high-contrast:active)", "rules": 0}, {"media": "only screen and (max-device-width:1024px)", "rules": 0}, {"media": "screen and (max-height:300px),screen and (max-width:800px)and (orientation:landscape)", "rules": 0}, {"media": "screen and (min-width:850px)", "rules": 0}, {"media": "screen and (max-width:549px)", "rules": 0}, {"media": "screen and (max-width:549px)", "rules": 0}, {"media": "screen and (min-width:550px)and (max-width:849px)", "rules": 0}, {"media": "screen and (min-width:850px)", "rules": 0}, {"media": "screen and (min-width:550px)", "rules": 0}, {"media": "screen and (min-width:850px)", "rules": 0}, {"media": "screen and (max-width:849px)", "rules": 0}], "imports": [], "fonts": ["sans-serif}body{margin:0}article,aside,details,figcaption,figure,footer,header,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block}audio:not([controls]){display:none", "arial,monospace", "fl-icons!important", "\"object-fit: cover", "sans-serif", "arial,sans-serif", "fl-icons", "georgia}.is-xxxlarge{font-size:2.5em}.is-xxlarge{font-size:2em}.is-xlarge{font-size:1.5em}.is-larger{font-size:1.3em}.is-large{font-size:1.15em}.is-small,.is-small.button{font-size:.8em}.is-smaller{font-size:.75em}.is-xsmall{font-size:.7em}.is-xxsmall{font-size:.6em}@media(max-width:549px){.is-xxlarge{font-size:2.5em}.is-xlarge{font-size:1.8em}.is-larger{font-size:1.2em}.is-large{font-size:1em}}.box-text a:not(.button),.box-text h1,.box-text h2,.box-text h3,.box-text h4,.box-text h5,.box-text h6{line-height:1.3", "arial,sans-serif!important"], "colors": ["#111", "#7189d9", "rgba(0,0,0,.2)", "rgba(0,0,0,0)", "#ddd", "rgba(0,0,0,.5)", "#9146fe", "#ff0", "#fff600", "#21759b", "rgba(0,0,0,.1)", "#3b6994", "#222", "rgba(0,0,0,.9)", "#0b0b0b", "#f9f9f9", "#e1e1e1", "#54a9ea", "rgba(0,0,0,.14)", "#36455d", "rgba(0,0,0,.24)", "rgba(0,0,0,.07)", "rgba(0,0,0,.19)", "#444", "#ccc", "#f7f7f7", "#527498", "rgba(0,0,0,.04)", "rgba(0,0,0,.6)", "#fc7600", "rgba(0,0,0,.12)", "#cb2320", "#f1f1f1", "#0099e5", "rgba(0,0,0,.09)", "rgba(0,0,0,.4)", "rgba(0,0,0,.05)", "#2478ba", "rgba(0,0,0,.03)", "rgba(0,0,0,.95)", "#5b5b5b", "#51cb5a", "#0072b7", "#f3f3f3", "#3a589d", "rgba(0,0,0,.16)", "#333", "rgba(0,0,0,.3)", "#323232", "#bdbdbd", "rgba(0,0,0,.23)", "#000", "rgba(0,0,0,.22)", "#666", "#e5086f", "#999", "#dd4e31", "rgba(50,50,50,0)", "#555", "rgba(0,0,0,.02)", "rgba(136,183,213,0)", "rgba(0,0,0,.7)", "rgba(0,0,0,.25)", "#c33223", "rgba(0,0,0,.15)", "#fff", "#777", "#ececec"], "size": 151896}, "v4-shims.css": {"url": "https://use.fontawesome.com/releases/v5.15.4/css/v4-shims.css", "selectors": [{"selector": "/*!\n * Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com\n * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)\n */\n.fa.fa-glass:before", "properties": 0}, {"selector": ".fa.fa-meetup", "properties": 0}, {"selector": ".fa.fa-star-o", "properties": 0}, {"selector": ".fa.fa-star-o:before", "properties": 0}, {"selector": ".fa.fa-close:before,.fa.fa-remove:before", "properties": 0}, {"selector": ".fa.fa-gear:before", "properties": 0}, {"selector": ".fa.fa-trash-o", "properties": 0}, {"selector": ".fa.fa-trash-o:before", "properties": 0}, {"selector": ".fa.fa-file-o", "properties": 0}, {"selector": ".fa.fa-file-o:before", "properties": 0}, {"selector": ".fa.fa-clock-o", "properties": 0}, {"selector": ".fa.fa-clock-o:before", "properties": 0}, {"selector": ".fa.fa-arrow-circle-o-down", "properties": 0}, {"selector": ".fa.fa-arrow-circle-o-down:before", "properties": 0}, {"selector": ".fa.fa-arrow-circle-o-up", "properties": 0}, {"selector": ".fa.fa-arrow-circle-o-up:before", "properties": 0}, {"selector": ".fa.fa-play-circle-o", "properties": 0}, {"selector": ".fa.fa-play-circle-o:before", "properties": 0}, {"selector": ".fa.fa-repeat:before,.fa.fa-rotate-right:before", "properties": 0}, {"selector": ".fa.fa-refresh:before", "properties": 0}, {"selector": ".fa.fa-list-alt", "properties": 0}, {"selector": ".fa.fa-dedent:before", "properties": 0}, {"selector": ".fa.fa-video-camera:before", "properties": 0}, {"selector": ".fa.fa-picture-o", "properties": 0}, {"selector": ".fa.fa-picture-o:before", "properties": 0}, {"selector": ".fa.fa-photo", "properties": 0}, {"selector": ".fa.fa-photo:before", "properties": 0}, {"selector": ".fa.fa-image", "properties": 0}, {"selector": ".fa.fa-image:before", "properties": 0}, {"selector": ".fa.fa-pencil:before", "properties": 0}, {"selector": ".fa.fa-map-marker:before", "properties": 0}, {"selector": ".fa.fa-pencil-square-o", "properties": 0}, {"selector": ".fa.fa-pencil-square-o:before", "properties": 0}, {"selector": ".fa.fa-share-square-o", "properties": 0}, {"selector": ".fa.fa-share-square-o:before", "properties": 0}, {"selector": ".fa.fa-check-square-o", "properties": 0}, {"selector": ".fa.fa-check-square-o:before", "properties": 0}, {"selector": ".fa.fa-arrows:before", "properties": 0}, {"selector": ".fa.fa-times-circle-o", "properties": 0}, {"selector": ".fa.fa-times-circle-o:before", "properties": 0}, {"selector": ".fa.fa-check-circle-o", "properties": 0}, {"selector": ".fa.fa-check-circle-o:before", "properties": 0}, {"selector": ".fa.fa-mail-forward:before", "properties": 0}, {"selector": ".fa.fa-expand:before", "properties": 0}, {"selector": ".fa.fa-compress:before", "properties": 0}, {"selector": ".fa.fa-eye,.fa.fa-eye-slash", "properties": 0}, {"selector": ".fa.fa-warning:before", "properties": 0}, {"selector": ".fa.fa-calendar:before", "properties": 0}, {"selector": ".fa.fa-arrows-v:before", "properties": 0}, {"selector": ".fa.fa-arrows-h:before", "properties": 0}, {"selector": ".fa.fa-bar-chart", "properties": 0}, {"selector": ".fa.fa-bar-chart:before", "properties": 0}, {"selector": ".fa.fa-bar-chart-o", "properties": 0}, {"selector": ".fa.fa-bar-chart-o:before", "properties": 0}, {"selector": ".fa.fa-facebook-square,.fa.fa-twitter-square", "properties": 0}, {"selector": ".fa.fa-gears:before", "properties": 0}, {"selector": ".fa.fa-thumbs-o-up", "properties": 0}, {"selector": ".fa.fa-thumbs-o-up:before", "properties": 0}, {"selector": ".fa.fa-thumbs-o-down", "properties": 0}, {"selector": ".fa.fa-thumbs-o-down:before", "properties": 0}, {"selector": ".fa.fa-heart-o", "properties": 0}, {"selector": ".fa.fa-heart-o:before", "properties": 0}, {"selector": ".fa.fa-sign-out:before", "properties": 0}, {"selector": ".fa.fa-linkedin-square", "properties": 0}, {"selector": ".fa.fa-linkedin-square:before", "properties": 0}, {"selector": ".fa.fa-thumb-tack:before", "properties": 0}, {"selector": ".fa.fa-external-link:before", "properties": 0}, {"selector": ".fa.fa-sign-in:before", "properties": 0}, {"selector": ".fa.fa-github-square", "properties": 0}, {"selector": ".fa.fa-lemon-o", "properties": 0}, {"selector": ".fa.fa-lemon-o:before", "properties": 0}, {"selector": ".fa.fa-square-o", "properties": 0}, {"selector": ".fa.fa-square-o:before", "properties": 0}, {"selector": ".fa.fa-bookmark-o", "properties": 0}, {"selector": ".fa.fa-bookmark-o:before", "properties": 0}, {"selector": ".fa.fa-facebook,.fa.fa-twitter", "properties": 0}, {"selector": ".fa.fa-facebook:before", "properties": 0}, {"selector": ".fa.fa-facebook-f", "properties": 0}, {"selector": ".fa.fa-facebook-f:before", "properties": 0}, {"selector": ".fa.fa-github", "properties": 0}, {"selector": ".fa.fa-credit-card", "properties": 0}, {"selector": ".fa.fa-feed:before", "properties": 0}, {"selector": ".fa.fa-hdd-o", "properties": 0}, {"selector": ".fa.fa-hdd-o:before", "properties": 0}, {"selector": ".fa.fa-hand-o-right", "properties": 0}, {"selector": ".fa.fa-hand-o-right:before", "properties": 0}, {"selector": ".fa.fa-hand-o-left", "properties": 0}, {"selector": ".fa.fa-hand-o-left:before", "properties": 0}, {"selector": ".fa.fa-hand-o-up", "properties": 0}, {"selector": ".fa.fa-hand-o-up:before", "properties": 0}, {"selector": ".fa.fa-hand-o-down", "properties": 0}, {"selector": ".fa.fa-hand-o-down:before", "properties": 0}, {"selector": ".fa.fa-arrows-alt:before", "properties": 0}, {"selector": ".fa.fa-group:before", "properties": 0}, {"selector": ".fa.fa-chain:before", "properties": 0}, {"selector": ".fa.fa-scissors:before", "properties": 0}, {"selector": ".fa.fa-files-o", "properties": 0}, {"selector": ".fa.fa-files-o:before", "properties": 0}, {"selector": ".fa.fa-floppy-o", "properties": 0}, {"selector": ".fa.fa-floppy-o:before", "properties": 0}, {"selector": ".fa.fa-navicon:before,.fa.fa-reorder:before", "properties": 0}, {"selector": ".fa.fa-google-plus,.fa.fa-google-plus-square,.fa.fa-pinterest,.fa.fa-pinterest-square", "properties": 0}, {"selector": ".fa.fa-google-plus:before", "properties": 0}, {"selector": ".fa.fa-money", "properties": 0}, {"selector": ".fa.fa-money:before", "properties": 0}, {"selector": ".fa.fa-unsorted:before", "properties": 0}, {"selector": ".fa.fa-sort-desc:before", "properties": 0}, {"selector": ".fa.fa-sort-asc:before", "properties": 0}, {"selector": ".fa.fa-linkedin", "properties": 0}, {"selector": ".fa.fa-linkedin:before", "properties": 0}, {"selector": ".fa.fa-rotate-left:before", "properties": 0}, {"selector": ".fa.fa-legal:before", "properties": 0}, {"selector": ".fa.fa-dashboard:before,.fa.fa-tachometer:before", "properties": 0}, {"selector": ".fa.fa-comment-o", "properties": 0}, {"selector": ".fa.fa-comment-o:before", "properties": 0}, {"selector": ".fa.fa-comments-o", "properties": 0}, {"selector": ".fa.fa-comments-o:before", "properties": 0}, {"selector": ".fa.fa-flash:before", "properties": 0}, {"selector": ".fa.fa-clipboard,.fa.fa-paste", "properties": 0}, {"selector": ".fa.fa-paste:before", "properties": 0}, {"selector": ".fa.fa-lightbulb-o", "properties": 0}, {"selector": ".fa.fa-lightbulb-o:before", "properties": 0}, {"selector": ".fa.fa-exchange:before", "properties": 0}, {"selector": ".fa.fa-cloud-download:before", "properties": 0}, {"selector": ".fa.fa-cloud-upload:before", "properties": 0}, {"selector": ".fa.fa-bell-o", "properties": 0}, {"selector": ".fa.fa-bell-o:before", "properties": 0}, {"selector": ".fa.fa-cutlery:before", "properties": 0}, {"selector": ".fa.fa-file-text-o", "properties": 0}, {"selector": ".fa.fa-file-text-o:before", "properties": 0}, {"selector": ".fa.fa-building-o", "properties": 0}, {"selector": ".fa.fa-building-o:before", "properties": 0}, {"selector": ".fa.fa-hospital-o", "properties": 0}, {"selector": ".fa.fa-hospital-o:before", "properties": 0}, {"selector": ".fa.fa-tablet:before", "properties": 0}, {"selector": ".fa.fa-mobile-phone:before,.fa.fa-mobile:before", "properties": 0}, {"selector": ".fa.fa-circle-o", "properties": 0}, {"selector": ".fa.fa-circle-o:before", "properties": 0}, {"selector": ".fa.fa-mail-reply:before", "properties": 0}, {"selector": ".fa.fa-github-alt", "properties": 0}, {"selector": ".fa.fa-folder-o", "properties": 0}, {"selector": ".fa.fa-folder-o:before", "properties": 0}, {"selector": ".fa.fa-folder-open-o", "properties": 0}, {"selector": ".fa.fa-folder-open-o:before", "properties": 0}, {"selector": ".fa.fa-smile-o", "properties": 0}, {"selector": ".fa.fa-smile-o:before", "properties": 0}, {"selector": ".fa.fa-frown-o", "properties": 0}, {"selector": ".fa.fa-frown-o:before", "properties": 0}, {"selector": ".fa.fa-meh-o", "properties": 0}, {"selector": ".fa.fa-meh-o:before", "properties": 0}, {"selector": ".fa.fa-keyboard-o", "properties": 0}, {"selector": ".fa.fa-keyboard-o:before", "properties": 0}, {"selector": ".fa.fa-flag-o", "properties": 0}, {"selector": ".fa.fa-flag-o:before", "properties": 0}, {"selector": ".fa.fa-mail-reply-all:before", "properties": 0}, {"selector": ".fa.fa-star-half-o", "properties": 0}, {"selector": ".fa.fa-star-half-o:before", "properties": 0}, {"selector": ".fa.fa-star-half-empty", "properties": 0}, {"selector": ".fa.fa-star-half-empty:before", "properties": 0}, {"selector": ".fa.fa-star-half-full", "properties": 0}, {"selector": ".fa.fa-star-half-full:before", "properties": 0}, {"selector": ".fa.fa-code-fork:before", "properties": 0}, {"selector": ".fa.fa-chain-broken:before", "properties": 0}, {"selector": ".fa.fa-shield:before", "properties": 0}, {"selector": ".fa.fa-calendar-o", "properties": 0}, {"selector": ".fa.fa-calendar-o:before", "properties": 0}, {"selector": ".fa.fa-css3,.fa.fa-html5,.fa.fa-maxcdn", "properties": 0}, {"selector": ".fa.fa-ticket:before", "properties": 0}, {"selector": ".fa.fa-minus-square-o", "properties": 0}, {"selector": ".fa.fa-minus-square-o:before", "properties": 0}, {"selector": ".fa.fa-level-up:before", "properties": 0}, {"selector": ".fa.fa-level-down:before", "properties": 0}, {"selector": ".fa.fa-pencil-square:before", "properties": 0}, {"selector": ".fa.fa-external-link-square:before", "properties": 0}, {"selector": ".fa.fa-compass", "properties": 0}, {"selector": ".fa.fa-caret-square-o-down", "properties": 0}, {"selector": ".fa.fa-caret-square-o-down:before", "properties": 0}, {"selector": ".fa.fa-toggle-down", "properties": 0}, {"selector": ".fa.fa-toggle-down:before", "properties": 0}, {"selector": ".fa.fa-caret-square-o-up", "properties": 0}, {"selector": ".fa.fa-caret-square-o-up:before", "properties": 0}, {"selector": ".fa.fa-toggle-up", "properties": 0}, {"selector": ".fa.fa-toggle-up:before", "properties": 0}, {"selector": ".fa.fa-caret-square-o-right", "properties": 0}, {"selector": ".fa.fa-caret-square-o-right:before", "properties": 0}, {"selector": ".fa.fa-toggle-right", "properties": 0}, {"selector": ".fa.fa-toggle-right:before", "properties": 0}, {"selector": ".fa.fa-eur:before,.fa.fa-euro:before", "properties": 0}, {"selector": ".fa.fa-gbp:before", "properties": 0}, {"selector": ".fa.fa-dollar:before,.fa.fa-usd:before", "properties": 0}, {"selector": ".fa.fa-inr:before,.fa.fa-rupee:before", "properties": 0}, {"selector": ".fa.fa-cny:before,.fa.fa-jpy:before,.fa.fa-rmb:before,.fa.fa-yen:before", "properties": 0}, {"selector": ".fa.fa-rouble:before,.fa.fa-rub:before,.fa.fa-ruble:before", "properties": 0}, {"selector": ".fa.fa-krw:before,.fa.fa-won:before", "properties": 0}, {"selector": ".fa.fa-bitcoin,.fa.fa-btc", "properties": 0}, {"selector": ".fa.fa-bitcoin:before", "properties": 0}, {"selector": ".fa.fa-file-text:before", "properties": 0}, {"selector": ".fa.fa-sort-alpha-asc:before", "properties": 0}, {"selector": ".fa.fa-sort-alpha-desc:before", "properties": 0}, {"selector": ".fa.fa-sort-amount-asc:before", "properties": 0}, {"selector": ".fa.fa-sort-amount-desc:before", "properties": 0}, {"selector": ".fa.fa-sort-numeric-asc:before", "properties": 0}, {"selector": ".fa.fa-sort-numeric-desc:before", "properties": 0}, {"selector": ".fa.fa-xing,.fa.fa-xing-square,.fa.fa-youtube,.fa.fa-youtube-play,.fa.fa-youtube-square", "properties": 0}, {"selector": ".fa.fa-youtube-play:before", "properties": 0}, {"selector": ".fa.fa-adn,.fa.fa-bitbucket,.fa.fa-bitbucket-square,.fa.fa-dropbox,.fa.fa-flickr,.fa.fa-instagram,.fa.fa-stack-overflow", "properties": 0}, {"selector": ".fa.fa-bitbucket-square:before", "properties": 0}, {"selector": ".fa.fa-tumblr,.fa.fa-tumblr-square", "properties": 0}, {"selector": ".fa.fa-long-arrow-down:before", "properties": 0}, {"selector": ".fa.fa-long-arrow-up:before", "properties": 0}, {"selector": ".fa.fa-long-arrow-left:before", "properties": 0}, {"selector": ".fa.fa-long-arrow-right:before", "properties": 0}, {"selector": ".fa.fa-android,.fa.fa-apple,.fa.fa-dribbble,.fa.fa-foursquare,.fa.fa-gittip,.fa.fa-gratipay,.fa.fa-linux,.fa.fa-skype,.fa.fa-trello,.fa.fa-windows", "properties": 0}, {"selector": ".fa.fa-gittip:before", "properties": 0}, {"selector": ".fa.fa-sun-o", "properties": 0}, {"selector": ".fa.fa-sun-o:before", "properties": 0}, {"selector": ".fa.fa-moon-o", "properties": 0}, {"selector": ".fa.fa-moon-o:before", "properties": 0}, {"selector": ".fa.fa-pagelines,.fa.fa-renren,.fa.fa-stack-exchange,.fa.fa-vk,.fa.fa-weibo", "properties": 0}, {"selector": ".fa.fa-arrow-circle-o-right", "properties": 0}, {"selector": ".fa.fa-arrow-circle-o-right:before", "properties": 0}, {"selector": ".fa.fa-arrow-circle-o-left", "properties": 0}, {"selector": ".fa.fa-arrow-circle-o-left:before", "properties": 0}, {"selector": ".fa.fa-caret-square-o-left", "properties": 0}, {"selector": ".fa.fa-caret-square-o-left:before", "properties": 0}, {"selector": ".fa.fa-toggle-left", "properties": 0}, {"selector": ".fa.fa-toggle-left:before", "properties": 0}, {"selector": ".fa.fa-dot-circle-o", "properties": 0}, {"selector": ".fa.fa-dot-circle-o:before", "properties": 0}, {"selector": ".fa.fa-vimeo-square", "properties": 0}, {"selector": ".fa.fa-try:before,.fa.fa-turkish-lira:before", "properties": 0}, {"selector": ".fa.fa-plus-square-o", "properties": 0}, {"selector": ".fa.fa-plus-square-o:before", "properties": 0}, {"selector": ".fa.fa-openid,.fa.fa-slack,.fa.fa-wordpress", "properties": 0}, {"selector": ".fa.fa-bank:before,.fa.fa-institution:before", "properties": 0}, {"selector": ".fa.fa-mortar-board:before", "properties": 0}, {"selector": ".fa.fa-delicious,.fa.fa-digg,.fa.fa-drupal,.fa.fa-google,.fa.fa-joomla,.fa.fa-pied-piper-alt,.fa.fa-pied-piper-pp,.fa.fa-reddit,.fa.fa-reddit-square,.fa.fa-stumbleupon,.fa.fa-stumbleupon-circle,.fa.fa-yahoo", "properties": 0}, {"selector": ".fa.fa-spoon:before", "properties": 0}, {"selector": ".fa.fa-behance,.fa.fa-behance-square,.fa.fa-steam,.fa.fa-steam-square", "properties": 0}, {"selector": ".fa.fa-automobile:before", "properties": 0}, {"selector": ".fa.fa-envelope-o", "properties": 0}, {"selector": ".fa.fa-envelope-o:before", "properties": 0}, {"selector": ".fa.fa-deviantart,.fa.fa-soundcloud,.fa.fa-spotify", "properties": 0}, {"selector": ".fa.fa-file-pdf-o", "properties": 0}, {"selector": ".fa.fa-file-pdf-o:before", "properties": 0}, {"selector": ".fa.fa-file-word-o", "properties": 0}, {"selector": ".fa.fa-file-word-o:before", "properties": 0}, {"selector": ".fa.fa-file-excel-o", "properties": 0}, {"selector": ".fa.fa-file-excel-o:before", "properties": 0}, {"selector": ".fa.fa-file-powerpoint-o", "properties": 0}, {"selector": ".fa.fa-file-powerpoint-o:before", "properties": 0}, {"selector": ".fa.fa-file-image-o", "properties": 0}, {"selector": ".fa.fa-file-image-o:before", "properties": 0}, {"selector": ".fa.fa-file-photo-o", "properties": 0}, {"selector": ".fa.fa-file-photo-o:before", "properties": 0}, {"selector": ".fa.fa-file-picture-o", "properties": 0}, {"selector": ".fa.fa-file-picture-o:before", "properties": 0}, {"selector": ".fa.fa-file-archive-o", "properties": 0}, {"selector": ".fa.fa-file-archive-o:before", "properties": 0}, {"selector": ".fa.fa-file-zip-o", "properties": 0}, {"selector": ".fa.fa-file-zip-o:before", "properties": 0}, {"selector": ".fa.fa-file-audio-o", "properties": 0}, {"selector": ".fa.fa-file-audio-o:before", "properties": 0}, {"selector": ".fa.fa-file-sound-o", "properties": 0}, {"selector": ".fa.fa-file-sound-o:before", "properties": 0}, {"selector": ".fa.fa-file-video-o", "properties": 0}, {"selector": ".fa.fa-file-video-o:before", "properties": 0}, {"selector": ".fa.fa-file-movie-o", "properties": 0}, {"selector": ".fa.fa-file-movie-o:before", "properties": 0}, {"selector": ".fa.fa-file-code-o", "properties": 0}, {"selector": ".fa.fa-file-code-o:before", "properties": 0}, {"selector": ".fa.fa-codepen,.fa.fa-jsfiddle,.fa.fa-vine", "properties": 0}, {"selector": ".fa.fa-life-bouy,.fa.fa-life-ring", "properties": 0}, {"selector": ".fa.fa-life-bouy:before", "properties": 0}, {"selector": ".fa.fa-life-buoy", "properties": 0}, {"selector": ".fa.fa-life-buoy:before", "properties": 0}, {"selector": ".fa.fa-life-saver", "properties": 0}, {"selector": ".fa.fa-life-saver:before", "properties": 0}, {"selector": ".fa.fa-support", "properties": 0}, {"selector": ".fa.fa-support:before", "properties": 0}, {"selector": ".fa.fa-circle-o-notch:before", "properties": 0}, {"selector": ".fa.fa-ra,.fa.fa-rebel", "properties": 0}, {"selector": ".fa.fa-ra:before", "properties": 0}, {"selector": ".fa.fa-resistance", "properties": 0}, {"selector": ".fa.fa-resistance:before", "properties": 0}, {"selector": ".fa.fa-empire,.fa.fa-ge", "properties": 0}, {"selector": ".fa.fa-ge:before", "properties": 0}, {"selector": ".fa.fa-git,.fa.fa-git-square,.fa.fa-hacker-news,.fa.fa-y-combinator-square", "properties": 0}, {"selector": ".fa.fa-y-combinator-square:before", "properties": 0}, {"selector": ".fa.fa-yc-square", "properties": 0}, {"selector": ".fa.fa-yc-square:before", "properties": 0}, {"selector": ".fa.fa-qq,.fa.fa-tencent-weibo,.fa.fa-wechat,.fa.fa-weixin", "properties": 0}, {"selector": ".fa.fa-wechat:before", "properties": 0}, {"selector": ".fa.fa-send:before", "properties": 0}, {"selector": ".fa.fa-paper-plane-o", "properties": 0}, {"selector": ".fa.fa-paper-plane-o:before", "properties": 0}, {"selector": ".fa.fa-send-o", "properties": 0}, {"selector": ".fa.fa-send-o:before", "properties": 0}, {"selector": ".fa.fa-circle-thin", "properties": 0}, {"selector": ".fa.fa-circle-thin:before", "properties": 0}, {"selector": ".fa.fa-header:before", "properties": 0}, {"selector": ".fa.fa-sliders:before", "properties": 0}, {"selector": ".fa.fa-futbol-o", "properties": 0}, {"selector": ".fa.fa-futbol-o:before", "properties": 0}, {"selector": ".fa.fa-soccer-ball-o", "properties": 0}, {"selector": ".fa.fa-soccer-ball-o:before", "properties": 0}, {"selector": ".fa.fa-slideshare,.fa.fa-twitch,.fa.fa-yelp", "properties": 0}, {"selector": ".fa.fa-newspaper-o", "properties": 0}, {"selector": ".fa.fa-newspaper-o:before", "properties": 0}, {"selector": ".fa.fa-cc-amex,.fa.fa-cc-discover,.fa.fa-cc-mastercard,.fa.fa-cc-paypal,.fa.fa-cc-stripe,.fa.fa-cc-visa,.fa.fa-google-wallet,.fa.fa-paypal", "properties": 0}, {"selector": ".fa.fa-bell-slash-o", "properties": 0}, {"selector": ".fa.fa-bell-slash-o:before", "properties": 0}, {"selector": ".fa.fa-trash:before", "properties": 0}, {"selector": ".fa.fa-copyright", "properties": 0}, {"selector": ".fa.fa-eyedropper:before", "properties": 0}, {"selector": ".fa.fa-area-chart:before", "properties": 0}, {"selector": ".fa.fa-pie-chart:before", "properties": 0}, {"selector": ".fa.fa-line-chart:before", "properties": 0}, {"selector": ".fa.fa-angellist,.fa.fa-i<PERSON><PERSON><PERSON>,.fa.fa-lastfm,.fa.fa-lastfm-square", "properties": 0}, {"selector": ".fa.fa-cc", "properties": 0}, {"selector": ".fa.fa-cc:before", "properties": 0}, {"selector": ".fa.fa-ils:before,.fa.fa-shekel:before,.fa.fa-sheq<PERSON>:before", "properties": 0}, {"selector": ".fa.fa-meanpath", "properties": 0}, {"selector": ".fa.fa-meanpath:before", "properties": 0}, {"selector": ".fa.fa-buysellads,.fa.fa-connectdevelop,.fa.fa-dashcube,.fa.fa-forumbee,.fa.fa-leanpub,.fa.fa-sellsy,.fa.fa-shirtsinbulk,.fa.fa-simplybuilt,.fa.fa-skyatlas", "properties": 0}, {"selector": ".fa.fa-diamond", "properties": 0}, {"selector": ".fa.fa-diamond:before", "properties": 0}, {"selector": ".fa.fa-intersex:before", "properties": 0}, {"selector": ".fa.fa-facebook-official", "properties": 0}, {"selector": ".fa.fa-facebook-official:before", "properties": 0}, {"selector": ".fa.fa-pinterest-p,.fa.fa-whatsapp", "properties": 0}, {"selector": ".fa.fa-hotel:before", "properties": 0}, {"selector": ".fa.fa-medium,.fa.fa-viacoin,.fa.fa-y-combinator,.fa.fa-yc", "properties": 0}, {"selector": ".fa.fa-yc:before", "properties": 0}, {"selector": ".fa.fa-expeditedssl,.fa.fa-opencart,.fa.fa-optin-monster", "properties": 0}, {"selector": ".fa.fa-battery-4:before,.fa.fa-battery:before", "properties": 0}, {"selector": ".fa.fa-battery-3:before", "properties": 0}, {"selector": ".fa.fa-battery-2:before", "properties": 0}, {"selector": ".fa.fa-battery-1:before", "properties": 0}, {"selector": ".fa.fa-battery-0:before", "properties": 0}, {"selector": ".fa.fa-object-group,.fa.fa-object-ungroup,.fa.fa-sticky-note-o", "properties": 0}, {"selector": ".fa.fa-sticky-note-o:before", "properties": 0}, {"selector": ".fa.fa-cc-diners-club,.fa.fa-cc-jcb", "properties": 0}, {"selector": ".fa.fa-clone,.fa.fa-hourglass-o", "properties": 0}, {"selector": ".fa.fa-hourglass-o:before", "properties": 0}, {"selector": ".fa.fa-hourglass-1:before", "properties": 0}, {"selector": ".fa.fa-hourglass-2:before", "properties": 0}, {"selector": ".fa.fa-hourglass-3:before", "properties": 0}, {"selector": ".fa.fa-hand-rock-o", "properties": 0}, {"selector": ".fa.fa-hand-rock-o:before", "properties": 0}, {"selector": ".fa.fa-hand-grab-o", "properties": 0}, {"selector": ".fa.fa-hand-grab-o:before", "properties": 0}, {"selector": ".fa.fa-hand-paper-o", "properties": 0}, {"selector": ".fa.fa-hand-paper-o:before", "properties": 0}, {"selector": ".fa.fa-hand-stop-o", "properties": 0}, {"selector": ".fa.fa-hand-stop-o:before", "properties": 0}, {"selector": ".fa.fa-hand-scissors-o", "properties": 0}, {"selector": ".fa.fa-hand-scissors-o:before", "properties": 0}, {"selector": ".fa.fa-hand-lizard-o", "properties": 0}, {"selector": ".fa.fa-hand-lizard-o:before", "properties": 0}, {"selector": ".fa.fa-hand-spock-o", "properties": 0}, {"selector": ".fa.fa-hand-spock-o:before", "properties": 0}, {"selector": ".fa.fa-hand-pointer-o", "properties": 0}, {"selector": ".fa.fa-hand-pointer-o:before", "properties": 0}, {"selector": ".fa.fa-hand-peace-o", "properties": 0}, {"selector": ".fa.fa-hand-peace-o:before", "properties": 0}, {"selector": ".fa.fa-registered", "properties": 0}, {"selector": ".fa.fa-chrome,.fa.fa-creative-commons,.fa.fa-firefox,.fa.fa-get-pocket,.fa.fa-gg,.fa.fa-gg-circle,.fa.fa-internet-explorer,.fa.fa-odnoklassniki,.fa.fa-odnoklassniki-square,.fa.fa-opera,.fa.fa-safari,.fa.fa-tripadvisor,.fa.fa-wikipedia-w", "properties": 0}, {"selector": ".fa.fa-television:before", "properties": 0}, {"selector": ".fa.fa-500px,.fa.fa-amazon,.fa.fa-contao", "properties": 0}, {"selector": ".fa.fa-calendar-plus-o", "properties": 0}, {"selector": ".fa.fa-calendar-plus-o:before", "properties": 0}, {"selector": ".fa.fa-calendar-minus-o", "properties": 0}, {"selector": ".fa.fa-calendar-minus-o:before", "properties": 0}, {"selector": ".fa.fa-calendar-times-o", "properties": 0}, {"selector": ".fa.fa-calendar-times-o:before", "properties": 0}, {"selector": ".fa.fa-calendar-check-o", "properties": 0}, {"selector": ".fa.fa-calendar-check-o:before", "properties": 0}, {"selector": ".fa.fa-map-o", "properties": 0}, {"selector": ".fa.fa-map-o:before", "properties": 0}, {"selector": ".fa.fa-commenting:before", "properties": 0}, {"selector": ".fa.fa-commenting-o", "properties": 0}, {"selector": ".fa.fa-commenting-o:before", "properties": 0}, {"selector": ".fa.fa-houzz,.fa.fa-vimeo", "properties": 0}, {"selector": ".fa.fa-vimeo:before", "properties": 0}, {"selector": ".fa.fa-black-tie,.fa.fa-edge,.fa.fa-fonticons,.fa.fa-reddit-alien", "properties": 0}, {"selector": ".fa.fa-credit-card-alt:before", "properties": 0}, {"selector": ".fa.fa-codiepie,.fa.fa-fort-awesome,.fa.fa-mixcloud,.fa.fa-modx,.fa.fa-product-hunt,.fa.fa-scribd,.fa.fa-usb", "properties": 0}, {"selector": ".fa.fa-pause-circle-o", "properties": 0}, {"selector": ".fa.fa-pause-circle-o:before", "properties": 0}, {"selector": ".fa.fa-stop-circle-o", "properties": 0}, {"selector": ".fa.fa-stop-circle-o:before", "properties": 0}, {"selector": ".fa.fa-bluetooth,.fa.fa-bluetooth-b,.fa.fa-envira,.fa.fa-gitlab,.fa.fa-wheelchair-alt,.fa.fa-wpbe<PERSON>ner,.fa.fa-wpforms", "properties": 0}, {"selector": ".fa.fa-wheelchair-alt:before", "properties": 0}, {"selector": ".fa.fa-question-circle-o", "properties": 0}, {"selector": ".fa.fa-question-circle-o:before", "properties": 0}, {"selector": ".fa.fa-volume-control-phone:before", "properties": 0}, {"selector": ".fa.fa-asl-interpreting:before", "properties": 0}, {"selector": ".fa.fa-deafness:before,.fa.fa-hard-of-hearing:before", "properties": 0}, {"selector": ".fa.fa-glide,.fa.fa-glide-g", "properties": 0}, {"selector": ".fa.fa-signing:before", "properties": 0}, {"selector": ".fa.fa-first-order,.fa.fa-google-plus-official,.fa.fa-pied-piper,.fa.fa-snapchat,.fa.fa-snapchat-ghost,.fa.fa-snapchat-square,.fa.fa-themeisle,.fa.fa-viadeo,.fa.fa-viadeo-square,.fa.fa-yoast", "properties": 0}, {"selector": ".fa.fa-google-plus-official:before", "properties": 0}, {"selector": ".fa.fa-google-plus-circle", "properties": 0}, {"selector": ".fa.fa-google-plus-circle:before", "properties": 0}, {"selector": ".fa.fa-fa,.fa.fa-font-awesome", "properties": 0}, {"selector": ".fa.fa-fa:before", "properties": 0}, {"selector": ".fa.fa-handshake-o", "properties": 0}, {"selector": ".fa.fa-handshake-o:before", "properties": 0}, {"selector": ".fa.fa-envelope-open-o", "properties": 0}, {"selector": ".fa.fa-envelope-open-o:before", "properties": 0}, {"selector": ".fa.fa-linode", "properties": 0}, {"selector": ".fa.fa-address-book-o", "properties": 0}, {"selector": ".fa.fa-address-book-o:before", "properties": 0}, {"selector": ".fa.fa-vcard:before", "properties": 0}, {"selector": ".fa.fa-address-card-o", "properties": 0}, {"selector": ".fa.fa-address-card-o:before", "properties": 0}, {"selector": ".fa.fa-vcard-o", "properties": 0}, {"selector": ".fa.fa-vcard-o:before", "properties": 0}, {"selector": ".fa.fa-user-circle-o", "properties": 0}, {"selector": ".fa.fa-user-circle-o:before", "properties": 0}, {"selector": ".fa.fa-user-o", "properties": 0}, {"selector": ".fa.fa-user-o:before", "properties": 0}, {"selector": ".fa.fa-id-badge", "properties": 0}, {"selector": ".fa.fa-drivers-license:before", "properties": 0}, {"selector": ".fa.fa-id-card-o", "properties": 0}, {"selector": ".fa.fa-id-card-o:before", "properties": 0}, {"selector": ".fa.fa-drivers-license-o", "properties": 0}, {"selector": ".fa.fa-drivers-license-o:before", "properties": 0}, {"selector": ".fa.fa-free-code-camp,.fa.fa-quora,.fa.fa-telegram", "properties": 0}, {"selector": ".fa.fa-thermometer-4:before,.fa.fa-thermometer:before", "properties": 0}, {"selector": ".fa.fa-thermometer-3:before", "properties": 0}, {"selector": ".fa.fa-thermometer-2:before", "properties": 0}, {"selector": ".fa.fa-thermometer-1:before", "properties": 0}, {"selector": ".fa.fa-thermometer-0:before", "properties": 0}, {"selector": ".fa.fa-bathtub:before,.fa.fa-s15:before", "properties": 0}, {"selector": ".fa.fa-window-maximize,.fa.fa-window-restore", "properties": 0}, {"selector": ".fa.fa-times-rectangle:before", "properties": 0}, {"selector": ".fa.fa-window-close-o", "properties": 0}, {"selector": ".fa.fa-window-close-o:before", "properties": 0}, {"selector": ".fa.fa-times-rectangle-o", "properties": 0}, {"selector": ".fa.fa-times-rectangle-o:before", "properties": 0}, {"selector": ".fa.fa-bandcamp,.fa.fa-eercast,.fa.fa-etsy,.fa.fa-grav,.fa.fa-imdb,.fa.fa-ravelry", "properties": 0}, {"selector": ".fa.fa-eercast:before", "properties": 0}, {"selector": ".fa.fa-snowflake-o", "properties": 0}, {"selector": ".fa.fa-snowflake-o:before", "properties": 0}, {"selector": ".fa.fa-superpowers,.fa.fa-wpexplorer", "properties": 0}, {"selector": ".fa.fa-cab:before", "properties": 0}], "media_queries": [], "imports": [], "fonts": ["\"font awesome 5 brands\"", "\"font awesome 5 free\""], "colors": [], "size": 26702}}