#!/usr/bin/env python3
"""
Create Final Optimized Website
T<PERSON>o phiên bản tối ưu cuối cùng một cách đúng đắn
"""

import os
import re
from pathlib import Path
from bs4 import BeautifulSoup

def create_final_optimized_website(source_html, output_html):
    """Create final optimized website"""
    print(f"Creating final optimized website...")
    print(f"Source: {source_html}")
    print(f"Output: {output_html}")
    
    # Read source HTML
    with open(source_html, 'r', encoding='utf-8') as f:
        content = f.read()
    
    soup = BeautifulSoup(content, 'html.parser')
    
    # 1. Remove all existing CSS links (except external ones)
    css_links = soup.find_all('link', rel='stylesheet')
    for link in css_links:
        href = link.get('href', '')
        if not href.startswith('http') and 'css/' in href:
            link.decompose()
    
    # 2. Remove all inline style tags
    style_tags = soup.find_all('style')
    for style in style_tags:
        style.decompose()
    
    # 3. Add single optimized CSS link
    head = soup.find('head')
    if head:
        css_link = soup.new_tag('link', rel='stylesheet', href='css/combined.min.css', type='text/css')
        head.append(css_link)
        print("✓ Added combined CSS link")
    
    # 4. Keep essential JavaScript files only
    script_tags = soup.find_all('script', src=True)
    essential_js = [
        'jquery.min.js',
        'jquery-migrate.min.js', 
        'flatsome.js',
        'component-loader.js'
    ]
    
    for script in script_tags:
        src = script.get('src', '')
        if src and not any(js in src for js in essential_js):
            if not src.startswith('http'):  # Keep external scripts
                script.decompose()
    
    # 5. Add essential JS files if missing
    if head:
        # Add jQuery first
        jquery_script = soup.new_tag('script', src='js/jquery.min.js', type='text/javascript')
        head.append(jquery_script)
        
        # Add jQuery migrate
        migrate_script = soup.new_tag('script', src='js/jquery-migrate.min.js', type='text/javascript')
        head.append(migrate_script)
        
        # Add Flatsome
        flatsome_script = soup.new_tag('script', src='js/flatsome.js', type='text/javascript')
        head.append(flatsome_script)
        
        print("✓ Added essential JS files")
    
    # 6. Add performance optimizations
    performance_css = """
<style>
/* Performance optimizations */
* { box-sizing: border-box; }
img { max-width: 100%; height: auto; }
html { scroll-behavior: smooth; }
.loading { opacity: 0; animation: fadeIn 0.5s ease-in-out forwards; }
@keyframes fadeIn { to { opacity: 1; } }
</style>
"""
    
    if head:
        head.append(BeautifulSoup(performance_css, 'html.parser'))
        print("✓ Added performance optimizations")
    
    # 7. Save optimized HTML
    with open(output_html, 'w', encoding='utf-8') as f:
        f.write(str(soup))
    
    print(f"✓ Final optimized website created: {output_html}")
    
    # Show file sizes
    original_size = os.path.getsize(source_html)
    optimized_size = os.path.getsize(output_html)
    savings = original_size - optimized_size
    
    print(f"\nFile Size Comparison:")
    print(f"Original: {original_size:,} bytes")
    print(f"Optimized: {optimized_size:,} bytes")
    print(f"Savings: {savings:,} bytes ({savings/original_size*100:.1f}%)")
    
    return output_html

def main():
    website_dir = Path("vandamtour")
    source_html = website_dir / "index.html"
    output_html = website_dir / "index-final-fixed.html"
    
    if not source_html.exists():
        print(f"Source file not found: {source_html}")
        return
    
    try:
        # Create final optimized website
        result = create_final_optimized_website(source_html, output_html)
        
        print(f"\n✅ Success!")
        print(f"📁 Optimized website: {result}")
        print(f"🌐 Open: http://localhost/duan/vandamtour/index-final-fixed.html")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
