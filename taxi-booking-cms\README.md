# 🚖 Taxi Booking CMS

**Professional WordPress-style CMS built with pure HTML/CSS/JavaScript**

## ✨ Features

### 🎯 **Frontend Features**
- **Responsive Design** - Mobile-first approach, works on all devices
- **Dynamic Content** - All content managed through admin panel
- **Service Management** - Add/edit/delete taxi services
- **Booking System** - Online booking with form validation
- **Contact Forms** - Contact and quick booking forms
- **Hero Slider** - Dynamic image slider with smooth transitions
- **Floating Contacts** - Sticky contact buttons (phone, zalo)
- **SEO Optimized** - Semantic HTML structure
- **Performance** - Optimized loading and animations

### 🛠️ **Admin Panel Features**
- **Dashboard** - Statistics and recent activities overview
- **Service Management** - CRUD operations for taxi services
- **Booking Management** - View and manage customer bookings
- **Message Management** - Handle contact form submissions
- **Settings Panel** - Configure website content and appearance
- **Data Export/Import** - Backup and restore functionality
- **Real-time Updates** - Changes reflect immediately on frontend

### 📱 **Technical Features**
- **Pure HTML/CSS/JS** - No frameworks, lightweight and fast
- **LocalStorage Database** - Client-side data persistence
- **Component Architecture** - Modular and maintainable code
- **CSS Variables** - Easy theming and customization
- **Progressive Enhancement** - Works without JavaScript
- **Print Styles** - Optimized for printing
- **Dark Mode Support** - Respects user preferences

## 🚀 Quick Start

### 1. **Setup**
```bash
# Clone or download the project
git clone <repository-url>
cd taxi-booking-cms

# Open in browser
open index.html
```

### 2. **Admin Access**
```bash
# Open admin panel
open admin/index.html
```

### 3. **Customization**
- Edit content through admin panel
- Modify CSS variables in `assets/css/main.css`
- Add custom JavaScript in `assets/js/main.js`

## 📁 Project Structure

```
taxi-booking-cms/
├── index.html                 # Main website
├── assets/                    # Frontend assets
│   ├── css/
│   │   ├── main.css          # Main styles
│   │   ├── components.css    # Component styles
│   │   └── responsive.css    # Responsive styles
│   └── js/
│       ├── data-manager.js   # Data management
│       ├── components.js     # UI components
│       └── main.js          # Main functionality
├── admin/                     # Admin panel
│   ├── index.html            # Admin dashboard
│   └── assets/
│       ├── css/
│       │   └── admin.css     # Admin styles
│       └── js/
│           └── admin.js      # Admin functionality
└── README.md                 # This file
```

## 🎨 Customization

### **Colors & Theming**
Edit CSS variables in `assets/css/main.css`:
```css
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #f39c12;
    --accent-color: #e74c3c;
    /* ... more variables */
}
```

### **Content Management**
All content is managed through the admin panel:
- **Services**: Add/edit taxi services with pricing
- **Settings**: Update company info, contact details
- **Hero Section**: Change banner text and images
- **Social Links**: Manage social media links

### **Data Storage**
Data is stored in browser's localStorage:
- **Persistent**: Data survives browser restarts
- **Exportable**: Backup/restore functionality
- **Portable**: Easy to migrate between environments

## 🔧 Development

### **Adding New Features**
1. **Frontend**: Add components in `assets/js/components.js`
2. **Admin**: Extend admin panel in `admin/assets/js/admin.js`
3. **Data**: Update data models in `assets/js/data-manager.js`
4. **Styles**: Add CSS in respective files

### **Data Structure**
```javascript
{
    settings: {
        siteName: "Taxi Booking CMS",
        phone: "0123 456 789",
        email: "<EMAIL>",
        // ... more settings
    },
    services: [
        {
            id: 1,
            name: "Airport Taxi",
            price: "200,000đ",
            // ... more fields
        }
    ],
    bookings: [...],
    messages: [...]
}
```

## 📱 Browser Support

- **Modern Browsers**: Chrome 60+, Firefox 60+, Safari 12+, Edge 79+
- **Mobile**: iOS Safari 12+, Chrome Mobile 60+
- **Features**: CSS Grid, Flexbox, LocalStorage, ES6+

## 🎯 Use Cases

### **Perfect For:**
- **Small Taxi Companies** - Professional online presence
- **Freelance Drivers** - Personal booking website
- **Transport Services** - Any vehicle rental business
- **Local Businesses** - Template for service-based websites

### **Benefits:**
- **No Server Required** - Pure client-side application
- **Easy Deployment** - Upload to any web hosting
- **Low Maintenance** - No database or backend to manage
- **Cost Effective** - No monthly hosting fees for dynamic features

## 🛡️ Security & Privacy

- **Client-Side Only** - No server-side vulnerabilities
- **Local Data** - User data stays in browser
- **No Tracking** - Privacy-focused design
- **HTTPS Ready** - Works with SSL certificates

## 📈 Performance

- **Lightweight** - ~50KB total size
- **Fast Loading** - Optimized assets
- **Smooth Animations** - 60fps transitions
- **Efficient** - Minimal JavaScript execution

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **FontAwesome** - Icons
- **Unsplash** - Demo images
- **CSS Grid** - Layout system
- **LocalStorage API** - Data persistence

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Issues**: GitHub Issues
- **Documentation**: This README

---

**Built with ❤️ for the taxi industry**

*Professional, lightweight, and powerful CMS for taxi booking websites*
