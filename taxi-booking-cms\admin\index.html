<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Taxi Booking CMS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="assets/css/admin.css">
    
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="admin-body">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-header-content">
            <div class="admin-logo">
                <i class="fas fa-tachometer-alt"></i>
                <h1>Admin Panel</h1>
            </div>
            
            <div class="admin-header-actions">
                <button class="btn btn-outline btn-sm" onclick="viewWebsite()">
                    <i class="fas fa-external-link-alt"></i> Xem Website
                </button>
                <button class="btn btn-secondary btn-sm" onclick="exportData()">
                    <i class="fas fa-download"></i> Export
                </button>
                <button class="btn btn-primary btn-sm" onclick="showImportModal()">
                    <i class="fas fa-upload"></i> Import
                </button>
            </div>
        </div>
    </header>

    <!-- Admin Layout -->
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <nav class="admin-nav">
                <ul class="admin-nav-menu">
                    <li class="admin-nav-item active">
                        <a href="#dashboard" onclick="showSection('dashboard')">
                            <i class="fas fa-chart-line"></i> Dashboard
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#services" onclick="showSection('services')">
                            <i class="fas fa-car"></i> Dịch Vụ
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#bookings" onclick="showSection('bookings')">
                            <i class="fas fa-calendar-check"></i> Đặt Xe
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#messages" onclick="showSection('messages')">
                            <i class="fas fa-envelope"></i> Tin Nhắn
                        </a>
                    </li>
                    <li class="admin-nav-item">
                        <a href="#settings" onclick="showSection('settings')">
                            <i class="fas fa-cog"></i> Cài Đặt
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="admin-section active">
                <div class="admin-section-header">
                    <h2><i class="fas fa-chart-line"></i> Dashboard</h2>
                    <p>Tổng quan về hoạt động của website</p>
                </div>
                
                <div class="admin-content">
                    <!-- Stats Cards -->
                    <div id="stats-container" class="stats-grid">
                        <!-- Dynamic stats -->
                    </div>
                    
                    <!-- Recent Activities -->
                    <div class="admin-grid">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h3><i class="fas fa-clock"></i> Đặt Xe Gần Đây</h3>
                                <a href="#bookings" onclick="showSection('bookings')" class="btn btn-sm btn-outline">Xem Tất Cả</a>
                            </div>
                            <div class="admin-card-body">
                                <div id="recent-bookings">
                                    <!-- Dynamic recent bookings -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h3><i class="fas fa-comments"></i> Tin Nhắn Mới</h3>
                                <a href="#messages" onclick="showSection('messages')" class="btn btn-sm btn-outline">Xem Tất Cả</a>
                            </div>
                            <div class="admin-card-body">
                                <div id="recent-messages">
                                    <!-- Dynamic recent messages -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Services Section -->
            <section id="services-section" class="admin-section">
                <div class="admin-section-header">
                    <h2><i class="fas fa-car"></i> Quản Lý Dịch Vụ</h2>
                    <button class="btn btn-primary" onclick="showServiceModal()">
                        <i class="fas fa-plus"></i> Thêm Dịch Vụ
                    </button>
                </div>
                
                <div class="admin-content">
                    <div class="admin-table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Tên Dịch Vụ</th>
                                    <th>Giá</th>
                                    <th>Trạng Thái</th>
                                    <th>Nổi Bật</th>
                                    <th>Thao Tác</th>
                                </tr>
                            </thead>
                            <tbody id="services-table-body">
                                <!-- Dynamic services -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Bookings Section -->
            <section id="bookings-section" class="admin-section">
                <div class="admin-section-header">
                    <h2><i class="fas fa-calendar-check"></i> Quản Lý Đặt Xe</h2>
                    <div class="admin-filters">
                        <select id="booking-status-filter" onchange="filterBookings()">
                            <option value="">Tất cả trạng thái</option>
                            <option value="pending">Chờ xử lý</option>
                            <option value="confirmed">Đã xác nhận</option>
                            <option value="completed">Hoàn thành</option>
                            <option value="cancelled">Đã hủy</option>
                        </select>
                    </div>
                </div>
                
                <div class="admin-content">
                    <div class="admin-table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Khách Hàng</th>
                                    <th>Điện Thoại</th>
                                    <th>Điểm Đón</th>
                                    <th>Điểm Đến</th>
                                    <th>Dịch Vụ</th>
                                    <th>Thời Gian</th>
                                    <th>Trạng Thái</th>
                                    <th>Thao Tác</th>
                                </tr>
                            </thead>
                            <tbody id="bookings-table-body">
                                <!-- Dynamic bookings -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Messages Section -->
            <section id="messages-section" class="admin-section">
                <div class="admin-section-header">
                    <h2><i class="fas fa-envelope"></i> Quản Lý Tin Nhắn</h2>
                    <div class="admin-filters">
                        <select id="message-status-filter" onchange="filterMessages()">
                            <option value="">Tất cả tin nhắn</option>
                            <option value="unread">Chưa đọc</option>
                            <option value="read">Đã đọc</option>
                            <option value="replied">Đã trả lời</option>
                        </select>
                    </div>
                </div>
                
                <div class="admin-content">
                    <div class="admin-table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Người Gửi</th>
                                    <th>Email</th>
                                    <th>Tiêu Đề</th>
                                    <th>Thời Gian</th>
                                    <th>Trạng Thái</th>
                                    <th>Thao Tác</th>
                                </tr>
                            </thead>
                            <tbody id="messages-table-body">
                                <!-- Dynamic messages -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="admin-section">
                <div class="admin-section-header">
                    <h2><i class="fas fa-cog"></i> Cài Đặt Website</h2>
                    <button class="btn btn-success" onclick="saveSettings()">
                        <i class="fas fa-save"></i> Lưu Cài Đặt
                    </button>
                </div>
                
                <div class="admin-content">
                    <div class="admin-grid">
                        <!-- General Settings -->
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h3><i class="fas fa-globe"></i> Thông Tin Chung</h3>
                            </div>
                            <div class="admin-card-body">
                                <form id="general-settings-form">
                                    <div class="form-group">
                                        <label for="site-name">Tên Website</label>
                                        <input type="text" id="site-name" name="siteName" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="site-title">Tiêu Đề Trang</label>
                                        <input type="text" id="site-title-input" name="siteTitle" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="site-description">Mô Tả</label>
                                        <textarea id="site-description" name="siteDescription" rows="3"></textarea>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="company-name">Tên Công Ty</label>
                                        <input type="text" id="company-name" name="companyName" required>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Contact Settings -->
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h3><i class="fas fa-phone"></i> Thông Tin Liên Hệ</h3>
                            </div>
                            <div class="admin-card-body">
                                <form id="contact-settings-form">
                                    <div class="form-group">
                                        <label for="phone">Số Điện Thoại</label>
                                        <input type="tel" id="phone" name="phone" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="email">Email</label>
                                        <input type="email" id="email" name="email" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="address">Địa Chỉ</label>
                                        <textarea id="address" name="address" rows="3"></textarea>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Hero Settings -->
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h3><i class="fas fa-image"></i> Banner Chính</h3>
                            </div>
                            <div class="admin-card-body">
                                <form id="hero-settings-form">
                                    <div class="form-group">
                                        <label for="hero-title">Tiêu Đề Banner</label>
                                        <input type="text" id="hero-title-input" name="heroTitle" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="hero-subtitle">Phụ Đề Banner</label>
                                        <input type="text" id="hero-subtitle-input" name="heroSubtitle" required>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Social Links -->
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h3><i class="fas fa-share-alt"></i> Mạng Xã Hội</h3>
                            </div>
                            <div class="admin-card-body">
                                <div id="social-links-container">
                                    <!-- Dynamic social links -->
                                </div>
                                <button type="button" class="btn btn-outline btn-sm" onclick="addSocialLink()">
                                    <i class="fas fa-plus"></i> Thêm Mạng Xã Hội
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Service Modal -->
    <div id="service-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="service-modal-title">Thêm Dịch Vụ Mới</h3>
                <button class="modal-close" onclick="closeServiceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="service-form">
                    <input type="hidden" id="service-id" name="id">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="service-name">Tên Dịch Vụ *</label>
                            <input type="text" id="service-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="service-icon">Icon</label>
                            <input type="text" id="service-icon" name="icon" placeholder="fas fa-car">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="service-description">Mô Tả</label>
                        <textarea id="service-description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="service-price">Giá *</label>
                            <input type="text" id="service-price" name="price" required>
                        </div>
                        <div class="form-group">
                            <label for="service-price-unit">Đơn Vị</label>
                            <input type="text" id="service-price-unit" name="priceUnit" placeholder="/chuyến">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="service-image">URL Hình Ảnh</label>
                        <input type="url" id="service-image" name="image">
                    </div>
                    
                    <div class="form-group">
                        <label for="service-features">Tính Năng (mỗi dòng một tính năng)</label>
                        <textarea id="service-features" name="features" rows="4" placeholder="Tính năng 1&#10;Tính năng 2&#10;Tính năng 3"></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="service-active" name="active" checked>
                                <span class="checkmark"></span>
                                Kích hoạt dịch vụ
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="service-featured" name="featured">
                                <span class="checkmark"></span>
                                Dịch vụ nổi bật
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeServiceModal()">Hủy</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Lưu Dịch Vụ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div id="import-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Import Dữ Liệu</h3>
                <button class="modal-close" onclick="closeImportModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="import-data">Dữ liệu JSON</label>
                    <textarea id="import-data" rows="10" placeholder="Paste JSON data here..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeImportModal()">Hủy</button>
                    <button type="button" class="btn btn-primary" onclick="importData()">
                        <i class="fas fa-upload"></i> Import
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="admin-notification" class="notification">
        <div class="notification-content">
            <i class="notification-icon"></i>
            <span class="notification-message"></span>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="../assets/js/data-manager.js"></script>
    <script src="assets/js/admin.js"></script>
</body>
</html>
